# Airspan ACP Metrics Collection

Airspan ACP Metrics Collection is a FastAPI-based microservice designed for automated collection and management of metrics from ACP (Airspan Cloud Platform) instances. The service provides scheduled metrics collection, SSH connectivity monitoring, and audit tracking capabilities.

## Overview

This microservice automatically:
- Monitors SSH connectivity to ACP instances
- Collects metrics files from remote ACP instance Host via SSH
- Uploads metrics to Google Cloud Storage (GCS)
- Maintains audit trails for all metrics collection activities
- Provides backfill capabilities for missing metrics

## Architecture

The service consists of three main background workers:
- **MetricsCollectionWorker**: Handles the actual collection of metrics from instances
- **MetricsAuditWorker**: Creates audit records for missing metrics intervals
- **SSHConnectivityWorker**: Monitors SSH connectivity status of instances

## Getting Started

### Prerequisites

- Python 3.11 or higher
- PostgreSQL database
- Google Cloud Platform (GCP) account with:
  - Google Cloud Storage bucket
  - Secret Manager for SSH credentials
  - Pub/Sub for metrics publishing
- uv package manager

### Database Configuration

Configure your database settings in `airspan_acp_metrics/cfg/starter.toml`:

```toml
[db]
host = "localhost"
port = 5432
user = "postgres"
database = "airspan_acp_agent"
password = "postgres"
```

### Google Cloud Configuration

Update the GCP settings in the configuration file:

```toml
[google]
project = "your-gcp-project-id"
projectconfig = "your-common-config-project"

[pubsub.metrics]
topic = "your-metrics-topic"
```

### Metrics Configuration

Configure metrics collection settings:

```toml
[metrics.Stats]
enabled = true
metric_name = "Stats"
frequency = 5         # Collection interval in minutes
backfill_enabled = true
max_days_lookback = 3 # Maximum days to look back for missing metrics
max_retry_attempts = 3      # Maximum retry attempts for failed collections
```

### Installation

1. Clone the repository
2. Install dependencies using uv:

```bash
# Install uv if you don't have it
pip install uv

# Install project dependencies
uv pip install -e .

# Install all required dependencies for running the microservice and unit tests
uv pip install ".[deploy]"
uv pip install ".[dev]"
uv pip install ".[test]"
```

### Running the Microservice

Start the microservice with the following command:

```bash
python -m airspan_acp_metrics.main
```

The service will be available at http://localhost:8080 by default.

### Scheduled Tasks

The microservice uses APScheduler to run background workers:

- **MetricsCollectionWorker**: Runs every minute to collect missing metrics
- **MetricsAuditWorker**: Runs every 5 minutes to create audit records for missing intervals
- **SSHConnectivityWorker**: Runs every 5 minutes to check SSH connectivity

All workers are automatically started during application startup and gracefully shutdown on termination.

### API Endpoints

Health check and security endpoints are available:
- Health check: `/health`
- Security endpoints: `/security/*`

Swagger documentation is available at http://localhost:8080/docs

### Running Tests

```bash
python -m pytest
```

Run tests with coverage:
```bash
python -m pytest --cov=airspan_acp_metrics
```

## Directory Structure

```
airspan_acp_metrics/
├── __init__.py
├── app.py                      # FastAPI application setup
├── main.py                     # Application entry point
├── constants.py                # Application constants
├── custom_exceptions.py        # Custom exception classes
├── utils.py                    # Utility functions
├── alembic_migrations/         # Database migrations
│   └── versions/
├── cfg/                        # Configuration files
│   ├── starter.toml            # Main configuration
│   └── local.toml              # Local overrides
├── database/                   # Database layer
│   ├── connection.py           # Database connection setup
│   ├── session_management.py   # Session management
│   ├── models/                 # SQLAlchemy models
│   │   ├── instance_db_model.py
│   │   ├── metrics_db_model.py
│   │   └── metrics_audits_db_model.py
│   └── services/               # Database services
│       ├── instance_db_service.py
│       ├── metrics_db_service.py
│       └── metric_audit_db_service.py
├── managers/                   # Business logic managers
│   ├── database_operations_manager.py
│   ├── file_operations_manager.py
│   ├── gcs_operations_manager.py
│   ├── instance_reconciliation_manager.py
│   ├── metrics_collector_manager.py
│   └── ssh_connection_manager.py
├── mem_cache/                  # Memory caching
├── pyd_models/                 # Pydantic models
│   ├── instance_pyd_model.py
│   ├── metrics_pyd_model.py
│   ├── metrics_audit_pyd_model.py
│   └── utils.py
├── workers/                    # Background workers
│   ├── metrics_collection_worker.py
│   ├── metrics_audit_worker.py
│   └── ssh_connectivity_worker.py
└── test/                       # Test suite
    ├── conftest.py
    ├── factories/              # Test data factories
    ├── test_data/              # Test data files
    └── unit_tests/             # Unit tests
        ├── test_managers/
        ├── test_sql/
        └── test_workers/
```

## Key Features

### Metrics Collection
- Automated collection of metrics from ACP instances via SSH
- Support for multiple metric types with configurable collection intervals
- Retry mechanism for failed collections
- Backfill capability for missing historical metrics

### SSH Connectivity Monitoring
- Continuous monitoring of SSH connectivity to instances
- Automatic status updates in the database
- Connection error tracking and reporting

### Audit Trail
- Comprehensive audit logging for all metrics collection activities
- Track collection status, attempt counts, and failure reasons
- Support for multiple collection statuses: NOT_STARTED, RUNNING, COLLECTED, FAILED, SKIPPED, METRICS_EMPTY

### Google Cloud Integration
- Upload collected metrics to Google Cloud Storage
- Publish metrics metadata to Pub/Sub
- Use Secret Manager for secure credential storage

### Database Management
- PostgreSQL database with proper relationships
- Automatic database migrations using Alembic
- Efficient querying with SQLAlchemy 2.0

## Configuration

The application uses TOML configuration files with support for environment variable overrides. Key configuration sections include:

- `[app]`: Application server settings
- `[db]`: Database connection parameters
- `[google]`: Google Cloud Platform settings
- `[pubsub]`: Pub/Sub configuration
- `[metrics]`: Metrics collection settings
- `[logging]`: Logging configuration

## Development Workflow

### Pre-commit Hooks

This project uses pre-commit hooks to ensure code quality and consistency.

#### Installation

To set up all hooks including the prepare-commit-msg hook, run:

```bash
./install-hooks.sh
```

Or manually:

```bash
.venv/bin/pre-commit install
.venv/bin/pre-commit install --hook-type prepare-commit-msg
```

#### Prepare Commit Message Hook

The prepare-commit-msg hook automatically adds a prefix to your commit messages based on the branch name. For example, if your branch is named `feature/NMS-123-add-new-feature`, the hook will prefix your commit message with `NMS-123:`.

### Usage

Pre-commit hooks will run automatically when you commit changes. To run them manually:

```bash
.venv/bin/pre-commit run --all-files
```

### Available Hooks

- **black**: Formats Python code
- **isort**: Sorts imports
- **ruff**: Fast Python linter and formatter
- **trailing-whitespace**: Removes trailing whitespace
- **end-of-file-fixer**: Ensures files end with a newline
- **detect-private-key**: Detects private keys in your code

### Configuration

Pre-commit hooks are configured in the `.pre-commit-config.yaml` file in the project root.

The prepare-commit-msg hook script is located in the `hooks/prepare-commit-msg` file. You can modify this script to customize how ticket numbers are extracted from branch names and added to commit messages.

## Package Management with uv

This project uses `uv` for fast and modern Python package management. The `pyproject.toml` file defines project dependencies organized into several sections:

- **Core Dependencies**: Listed under `[project.dependencies]` - essential packages required by the application
- **Deploy Dependencies**: Under `[project.optional-dependencies.deploy]` - packages needed for production deployment
- **Development Dependencies**: Under `[project.optional-dependencies.dev]` - development tools like linters and formatters
- **Test Dependencies**: Under `[project.optional-dependencies.test]` - packages needed for running tests

### Common `uv` Commands

- `uv pip install ".[deploy]"`: Install deployment dependencies
- `uv pip install ".[dev]"`: Install development dependencies
- `uv pip install ".[test]"`: Install test dependencies
- `uv add <package-name>`: Add a package to core dependencies
- `uv add --optional deploy <package-name>`: Add a package to deploy dependencies
- `uv pip list`: List installed packages
- `uv sync`: Install packages to match the `uv.lock` file
- `uv lock`: Update the `uv.lock` file with current dependencies
- `uv run <command>`: Execute a command within the project's virtual environment

### Managing `uv.lock`

The `uv.lock` file ensures reproducible environments by pinning exact versions of all dependencies. It's automatically generated and should be committed to version control.

## Docker Support

The project includes a Dockerfile for containerization. Build and run the container:

```bash
docker build -t acp-metrics-collection .
docker run -p 8080:8080 acp-metrics-collection
```

## Monitoring and Logging

The application provides comprehensive logging with configurable levels:
- Application logs are written to both console and audit files
- Each worker and manager component has dedicated loggers
- Request/response logging through middleware
- Error tracking with detailed stack traces

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes with appropriate tests
4. Ensure all pre-commit hooks pass
5. Submit a pull request

## License

Proprietary - All rights reserved
