#!/bin/bash

# This hook automatically adds the ticket number from the branch name to the commit message
# Example: If branch name is feature/ABC-123-add-new-feature, it adds "ABC-123: " to the commit message

# The first argument is the file where the commit message is stored
COMMIT_MSG_FILE=$1
COMMIT_SOURCE=$2

# Skip if this is a merge commit or an amend
if [ "$COMMIT_SOURCE" = "merge" ] || [ "$COMMIT_SOURCE" = "commit" ]; then
    exit 0
fi

# Get the current branch name
BRANCH_NAME=$(git symbolic-ref --short HEAD 2>/dev/null || echo '')

# Skip if not on a branch
if [ -z "$BRANCH_NAME" ]; then
    exit 0
fi

# Extract ticket number using regex
# This regex looks for patterns like ABC-123 in the branch name
if [[ $BRANCH_NAME =~ ([A-Z]+-[0-9]+) ]]; then
    TICKET="${BASH_REMATCH[1]}"

    # Read the commit message
    COMMIT_MSG=$(cat "$COMMIT_MSG_FILE")

    # First, clean up any existing ticket references to avoid duplication
    # Remove "[TICKET] " pattern if it exists
    CLEAN_MSG=$(echo "$COMMIT_MSG" | sed "s/\[$TICKET\] //g")
    # Remove "TICKET: " pattern if it exists
    CLEAN_MSG=$(echo "$CLEAN_MSG" | sed "s/$TICKET: //g")

    # Now add the ticket in the desired format
    if [[ "$CLEAN_MSG" != "$COMMIT_MSG" ]]; then
        # We cleaned something, so add the prefix in the correct format
        echo "$TICKET: $CLEAN_MSG" > "$COMMIT_MSG_FILE"
    elif [[ $COMMIT_MSG != *"$TICKET:"* ]] && [[ $COMMIT_MSG != *"[$TICKET]"* ]]; then
        # No ticket reference found, add it
        echo "$TICKET: $COMMIT_MSG" > "$COMMIT_MSG_FILE"
    fi
fi

exit 0
