import datetime
from uuid import UUID

from metrics_collector.api_schema.models import Severity as AlarmSeverityEnum
from pydantic import BaseModel
from pydantic import Field

from airspan_acp_metrics.pyd_models.alarm_enums import AlarmPriority
from airspan_acp_metrics.pyd_models.alarm_enums import AlarmStatus
from airspan_acp_metrics.pyd_models.alarm_enums import AlarmType


class AlarmTypePydModel(BaseModel):
    """Pydantic model for alarm type data."""

    type: AlarmType
    severity: AlarmSeverityEnum
    priority: AlarmPriority
    repairs: list[str] | None = None

    class Config:
        from_attributes = True


class AlarmTypeUpdatePydModel(BaseModel):
    """Pydantic model for updating alarm type data."""

    severity: AlarmSeverityEnum
    priority: AlarmPriority
    repairs: list[str] | None = None

    class Config:
        from_attributes = True


class AlarmCreatePydModel(BaseModel):
    """Pydantic model for creating a new alarm."""

    type: AlarmType
    name: str
    source: str
    description: str | None = None
    manager_instance: str

    class Config:
        from_attributes = True


class AlarmResponsePydModel(BaseModel):
    """Pydantic model for alarm response data."""

    id: UUID
    type: AlarmType
    name: str
    severity: AlarmSeverityEnum
    priority: AlarmPriority
    source: str
    created_at: datetime.datetime
    updated_at: datetime.datetime | None = None
    resolved_at: datetime.datetime | None = None
    resolver: str | None = None
    acknowledged: datetime.datetime | None = None
    acknowledger: str | None = None
    status: AlarmStatus
    description: str | None = None
    repairs: list[str] | None = None
    manager_instance: str

    class Config:
        from_attributes = True


class AlarmUpdatePydModel(BaseModel):
    """Pydantic model for updating an existing alarm."""

    description: str | None = None
    updated_at: datetime.datetime | None = None

    class Config:
        from_attributes = True


class AlarmAcknowledgePydModel(BaseModel):
    """Pydantic model for acknowledging an alarm."""

    acknowledger: str
    acknowledged: datetime.datetime = Field(default_factory=datetime.datetime.now)

    class Config:
        from_attributes = True


class AlarmResolvePydModel(BaseModel):
    """Pydantic model for resolving an alarm."""

    resolver: str
    resolved_at: datetime.datetime = Field(default_factory=datetime.datetime.now)

    class Config:
        from_attributes = True


class AlarmFilterPydModel(BaseModel):
    """Pydantic model for filtering alarms."""

    type: AlarmType | None = None
    severity: AlarmSeverityEnum | None = None
    priority: AlarmPriority | None = None
    status: AlarmStatus | None = None
    manager_instance: str | None = None
    source: str | None = None
    limit: int | None = Field(default=100, ge=1, le=1000)
    offset: int | None = Field(default=0, ge=0)

    class Config:
        from_attributes = True
