from datetime import datetime
from enum import Enum

from da_common.models import Status
from pydantic import BaseModel
from pydantic import ConfigDict
from pydantic import Field


class AuthType(str, Enum):
    """Authentication types for SSH connections."""

    PASSWORD = "password"
    SSH_KEY = "ssh-key"


# flake8: noqa
# fmt: off


class InstancePydModel(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    manager_instance:          str             = Field(..., max_length=255, description="Unique name of the instance")
    url:                       str             = Field(..., max_length=255, description="url of the instance")
    host_ip_address:           str  | None     = Field(..., max_length=255, description="IP address of the host")
    host_secret_id:            str | None      = Field(..., description="GCP Secret Manager secret id for the host credentials")
    auth_type:                 AuthType        = Field(default=AuthType.PASSWORD, description="Authentication type: password or ssh_key")
    metrics_collection_status: Status          = Field(default=Status.OK, description="Status of the metrics collection for the instance")
    reason:                    str | None      = Field(default=None, description="Reason for any SSH or connection errors")
    created_at:                datetime | None = Field(default_factory=datetime.now, description="Timestamp of the instance creation")
    updated_at:                datetime | None = Field(default_factory=datetime.now, description="Timestamp of the instance last update")


class InstanceUpdatePydModel(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    manager_instance:          str           = Field(..., max_length=255, description="Unique name of the instance")
    url:                       str | None    = Field(None, max_length=255, description="url of the instance")
    host_ip_address:           str | None    = Field(None, max_length=255, description="IP address of the host")
    host_secret_id:            str | None    = Field(None, description="GCP Secret Manager secret id for the host credentials")
    auth_type:                 AuthType | None = Field(None, description="Authentication type: password or ssh_key")
    metrics_collection_status: Status | None = Field(None, description="Status of the metrics collection for the instance")
    reason:                    str | None    = Field(None, description="Reason for any SSH or connection errors")
    updated_at:                datetime      = Field(default_factory=datetime.now, description="Timestamp of the instance last update")
