import datetime

from pydantic import BaseModel
from pydantic import Field

# flake8: noqa
# fmt: off


class MetricsPydModel(BaseModel):

    manager_instance: str               = Field(..., description="Manager instance name")
    metric_name:      str               = Field(..., description="Metric name")
    gcs_file_path:    str               = Field(..., description="File path in GCS")
    interval_start:   datetime.datetime = Field(..., description="Start time of the metrics")
    interval_end:     datetime.datetime = Field(..., description="End time of the metrics")
    created_at:       datetime.datetime = Field(default_factory=lambda: datetime.datetime.now(datetime.UTC), description="Created at")
    updated_at:       datetime.datetime = Field(default_factory=lambda: datetime.datetime.now(datetime.UTC), description="Updated at")
