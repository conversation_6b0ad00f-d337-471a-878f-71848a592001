import datetime
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field, field_serializer, model_validator
from pydantic import ConfigDict
from pydantic import Field
from pydantic import field_validator

from airspan_acp_metrics.pyd_models.utils import MetricsCollectionStatus, ensure_utc
from airspan_acp_metrics.pyd_models.utils import ensure_utc

# flake8: noqa
# fmt: off

class MetricsAuditPydModel(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    manager_instance:   str                      = Field(..., max_length=255, description="Unique name of the instance")
    metric_name:        str                      = Field(..., max_length=255, description="Name of the metric")
    file_metadata:      dict | None              = Field(None, description="Metadata of the metrics file")
    interval_start:     datetime.datetime        = Field(..., description="Start time of the metric")
    interval_end:       datetime.datetime        = Field(..., description="End time of the metric")
    collection_status:  MetricsCollectionStatus  = Field(..., description="Status of the metric collection")
    last_attempt:       datetime.datetime | None = Field(None, description="Last attempt to collect metrics")
    attempt_count:      int                      = Field(..., description="Number of attempts to collect metrics")
    reason:             str | None               = Field(default="", description="Error message if collection failed")
    created_at:         datetime.datetime | None = Field(default_factory=lambda: datetime.datetime.now(datetime.UTC), description="Creation timestamp")
    updated_at:         datetime.datetime | None = Field(default_factory=lambda: datetime.datetime.now(datetime.UTC), description="Last update timestamp")
    metrics_id:         UUID | None              = Field(None, description="ID of the metric record")

    @field_validator("created_at", "updated_at", "last_attempt", "interval_end" , "interval_start", mode="after")
    @classmethod
    def convert_to_utc(cls, value: datetime.datetime) -> datetime.datetime:
        return ensure_utc(value)


class UpdateMetricsAuditPydModel(BaseModel):
    metrics_id:         UUID | None              = Field(None, description="ID of the metric record")
    file_metadata:      dict | None              = Field(None, description="Metadata of the metrics file")
    collection_status:  MetricsCollectionStatus  = Field(..., description="Status of the metric collection")
    last_attempt:       datetime.datetime | None = Field(None, description="Last attempt to collect metrics")
    attempt_count:      int | None               = Field(None, description="Number of attempts to collect metrics")
    reason:             str | None               = Field(default="", description="Error message if collection failed")
    updated_at:         datetime.datetime | None = Field(default_factory=lambda: datetime.datetime.now(datetime.UTC), description="Last update timestamp")

    @field_validator("updated_at", "last_attempt", mode="after")
    @classmethod
    def convert_to_utc(cls, value: datetime.datetime) -> datetime.datetime:
        return ensure_utc(value)


class DeleteMetricsAuditRequest(BaseModel):
    model_config = ConfigDict(
        # Enable JSON schema generation for OpenAPI docs
        json_schema_extra={
            "example": {
                "start_time": "2025-06-01T00:00:00Z",
                "end_time": "2025-06-01T01:00:00Z",
                "manager_instance": "example-instance",
                "metric_name": "Stats",
            }
        }
    )

    start_time: datetime.datetime = Field(..., description="Start time for the deletion range")
    end_time: datetime.datetime = Field(..., description="End time for the deletion range")
    manager_instance: str = Field(..., description="Manager instance for the deletion range")
    metric_name: str = Field(..., description="Metric name for the deletion range")

    @field_serializer("start_time", "end_time")
    def serialize_datetime(self, value: datetime.datetime) -> str:
        """Serialize datetime fields to ISO format."""
        return value.isoformat()

    @model_validator(mode="after")
    def validate_time_range(self):
        start_time_utc = ensure_utc(self.start_time)
        end_time_utc = ensure_utc(self.end_time)
        # Now check if end_time is at least 5 minutes after start_time
        if end_time_utc - start_time_utc < datetime.timedelta(minutes=5):
            raise ValueError("End time must be at least 5 minutes after start time")
        return self
