from enum import Enum


class AlarmType(str, Enum):
    """Alarm types that can be raised."""

    SSH_CONNECTIVITY_FAILED = "ssh_connectivity_failed"
    AIRSPAN_ACP_AGENT_UNREACHABLE = "airspan_acp_agent_unreachable"
    INVENTORY_MANAGER_UNREACHABLE = "inventory_manager_unreachable"
    NMS_ORCHESTRATOR_UNREACHABLE = "nms_orchestrator_unreachable"
    INVENTORY_DATA_MISSING = "inventory_data_missing"
    ORCHESTRATOR_DATA_MISSING = "orchestrator_data_missing"
    AIRSPAN_INSTANCE_UNREACHABLE = "airspan_instance_unreachable"


class AlarmPriority(str, Enum):
    """Alarm priority levels."""

    high = "high"
    medium = "medium"
    low = "low"


class AlarmStatus(str, Enum):
    """Alarm status values."""

    new = "new"
    acknowledged = "acknowledged"
    updated = "updated"
    resolved = "resolved"
