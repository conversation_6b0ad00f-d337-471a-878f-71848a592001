import datetime
from enum import Enum

# flake8: noqa
# fmt: off

class MetricsCollectionStatus(str, Enum):
    NOT_STARTED   = "NOT_STARTED"
    RUNNING       = "RUNNING"
    METRICS_EMPTY = "METRICS_EMPTY"
    COLLECTED     = "COLLECTED"
    FAILED        = "FAILED"
    # its used only in race condition
    SKIPPED       = "SKIPPED"


# fmt: on
def ensure_utc(value: datetime.datetime) -> datetime.datetime:
    if value is None:
        return value
    if value.tzinfo is None:
        # Assume naive datetimes are UTC
        return value.replace(tzinfo=datetime.UTC)
    # Convert to UTC if not already
    return value.astimezone(datetime.UTC)
