import datetime
import fnmatch
import io
import logging
import os
import tempfile

from asyncssh import SSHClientConnection

from airspan_acp_metrics.pyd_models.instance_pyd_model import InstancePydModel


class FileOperationsManager:
    """Manages file operations for metrics collection."""

    def __init__(self, nms_dir_path: str = "/var/log/nms"):
        self.nms_dir_path = nms_dir_path
        self.logger = logging.getLogger("airspan_acp_metrics.managers.file_operations_manager")

    def _build_file_pattern(
        self,
        metric_config: dict,
        interval_start: datetime.datetime,
        interval_end: datetime.datetime,
    ) -> tuple[str, str]:
        """Build file pattern and directory path for searching."""
        metric_name = metric_config["metric_name"]
        date_str = interval_start.strftime("%Y%m%d")
        interval_start_str = interval_start.strftime("%H%M")
        # interval_end_str = interval_end.strftime("%H%M")

        # Create a pattern to match files like: *_Stats_20250507_0120_0125.zip
        file_pattern = f"*_{metric_name}_{date_str}_{interval_start_str}_*.zip"
        remote_dir_path = f"{self.nms_dir_path}/{metric_name}"

        return file_pattern, remote_dir_path

    async def _find_matching_files(
        self,
        sftp_client,
        remote_dir_path: str,
        file_pattern: str,
        instance_pyd_model: InstancePydModel,
        metric_config: dict,
    ) -> str | None:
        """Find files matching the pattern in the remote directory."""
        try:
            files = await sftp_client.listdir(remote_dir_path)
            matching_files = [f for f in files if fnmatch.fnmatch(f, file_pattern)]

            if not matching_files:
                self.logger.error(
                    f"MetricsCollector {metric_config['metric_name']}, Instance: {instance_pyd_model.manager_instance}, No files found matching pattern: {file_pattern}"
                )
                return None

            if len(matching_files) > 1:
                self.logger.warning(
                    f"MetricsCollector {metric_config['metric_name']}, Instance: {instance_pyd_model.manager_instance}, Multiple files found matching pattern: {matching_files}. Using first one: {matching_files[0]}"
                )

            return matching_files[0]

        except Exception as e:
            self.logger.error(
                f"MetricsCollector {metric_config['metric_name']}, Instance: {instance_pyd_model.manager_instance}, Error listing directory {remote_dir_path}: {e}"
            )
            return None

    async def _get_remote_file_metadata(
        self,
        sftp_client,
        remote_file_path: str,
        prefix_log: str = "",
    ) -> dict | None:
        """Get metadata about the remote file.

        Args:
            sftp_client: SFTP client connection
            remote_file_path: Path to the remote file
            prefix_log: Prefix for log messages

        Returns:
            Dictionary containing file metadata or None if failed
        """

        try:
            # Get file attributes from remote server
            file_attrs = await sftp_client.stat(remote_file_path)

            # Extract relevant metadata (only what SFTP protocol provides)
            metadata = {
                "file_size_bytes": file_attrs.size,
                "remote_file_path": remote_file_path,
                "remote_file_mode": file_attrs.permissions,  # File permissions
            }

            # Convert timestamps to ISO format for consistency
            if file_attrs.mtime:
                # Modification time (Unix timestamp)
                metadata["remote_file_mtime_iso"] = datetime.datetime.fromtimestamp(
                    file_attrs.mtime, tz=datetime.UTC
                ).isoformat()

            if file_attrs.atime:
                # Access time (Unix timestamp)
                metadata["remote_file_atime_iso"] = datetime.datetime.fromtimestamp(
                    file_attrs.atime, tz=datetime.UTC
                ).isoformat()

            self.logger.info(f"{prefix_log}, Retrieved remote file metadata: {metadata}")
            return metadata

        except Exception as e:
            self.logger.error(
                f"{prefix_log}, Error getting remote file metadata for {remote_file_path}: {e}",
                exc_info=True,
            )
            return None

    async def _download_file_to_memory(
        self,
        sftp_client,
        remote_file_path: str,
        prefix_log: str = "",
    ) -> tuple[io.BytesIO | None, str | None]:
        """Download the file from remote server to memory and get metadata."""

        try:
            # Download the file to temporary location first
            with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
                temp_path = tmp_file.name

            await sftp_client.get(remote_file_path, temp_path)

            # Read the file into memory
            with open(temp_path, "rb") as f:
                file_data = io.BytesIO(f.read())
                self.logger.info(
                    f"{prefix_log}, Successfully downloaded file: {remote_file_path} to memory"
                )

            try:
                # Clean up temp file
                os.unlink(temp_path)
                self.logger.info(f"{prefix_log}, Cleaned up temp file: {temp_path}")

                return file_data, None
            except Exception as e:
                self.logger.error(
                    f"{prefix_log}, Error cleaning up temp file {temp_path}: {e}",
                    exc_info=True,
                )
                return file_data, "Error cleaning up temp file"

        except Exception as e:
            self.logger.error(
                f"{prefix_log}, Error downloading file {remote_file_path} to memory: {e}",
                exc_info=True,
            )
            return None, "Error downloading file to memory"

    async def download_file_from_remote(
        self,
        connection: SSHClientConnection,
        instance_pyd_model: InstancePydModel,
        metric_config: dict,
        interval_start: datetime.datetime,
        interval_end: datetime.datetime,
        prefix_log: str = "",
    ) -> tuple[io.BytesIO | None, str | None, dict | None]:
        """Download the metrics file from remote server.

        Args:
            connection: SSH connection to the remote server
            instance_pyd_model: Instance details
            metric_config: Metric configuration
            interval_start: Start time for the metrics interval
            interval_end: End time for the metrics interval
            prefix_log: Prefix for log messages

        Returns:
            Tuple of (file_data, error_message, remote_file_metadata) where:
            - file_data: BytesIO object containing file data if successful, None otherwise
            - error_message: Error message if failed, None if successful
            - remote_file_metadata: Dictionary containing remote file metadata, None if failed
        """
        file_pattern, remote_dir_path = self._build_file_pattern(
            metric_config, interval_start, interval_end
        )

        self.logger.info(
            f"{prefix_log}, Searching for files matching pattern: {remote_dir_path}/{file_pattern}"
        )

        try:
            async with connection.start_sftp_client() as sftp:
                # Find matching files
                remote_filename = await self._find_matching_files(
                    sftp, remote_dir_path, file_pattern, instance_pyd_model, metric_config
                )

                if not remote_filename:
                    self.logger.error(
                        f"{prefix_log}, No files found matching pattern: {file_pattern}"
                    )
                    return None, "File not found", None

                self.logger.info(
                    f"{prefix_log}, File found: {remote_dir_path}/{remote_filename}"
                )

                remote_file_path = f"{remote_dir_path}/{remote_filename}"

                # Get remote file metadata before downloading
                remote_file_metadata = await self._get_remote_file_metadata(
                    sftp, remote_file_path, prefix_log
                )

                # Download file and get metadata
                file_data, error = await self._download_file_to_memory(
                    sftp, remote_file_path, prefix_log
                )
                return file_data, error, remote_file_metadata

        except Exception as e:
            self.logger.error(
                f"{prefix_log}, Error fetching file from remote server: {e}",
                exc_info=True,
            )
            return None, "Error fetching file from remote server", None
