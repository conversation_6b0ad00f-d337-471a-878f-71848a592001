import logging
import time
from typing import Any

import asyncssh
from asyncssh import SSHClientConnection
from da_common.config import Config
from da_common.models import Status
from da_common.secrets import get_secret_data
from google.cloud.secretmanager_v1 import SecretManagerServiceAsyncClient

from airspan_acp_metrics.constants import SERVER_CONNECTION_REFUSED_ALARM_TYPE
from airspan_acp_metrics.constants import SERVER_CONNECTION_TIMED_OUT_ALARM_TYPE
from airspan_acp_metrics.constants import SERVER_HOST_UNREACHABLE_ALARM_TYPE
from airspan_acp_metrics.constants import SERVER_MISSING_CREDENTIALS_ALARM_TYPE
from airspan_acp_metrics.constants import SERVER_PERMISSION_DENIED_ALARM_TYPE
from airspan_acp_metrics.constants import SERVER_UNEXPECTED_CONNECTION_ERROR_ALARM_TYPE
from airspan_acp_metrics.pyd_models.instance_pyd_model import AuthType
from airspan_acp_metrics.pyd_models.instance_pyd_model import InstancePydModel


class SSHConnectionManager:
    """Manages SSH connections to remote servers."""

    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger("airspan_acp_metrics.managers.ssh_connection_manager")

    @staticmethod
    def _build_error_response(
        *, alarm_type: str, reason: str, default_repairs: list[str]
    ) -> dict[str, Any]:
        """Build standardized error response."""
        return {
            "status": Status.ERROR,
            "cause": alarm_type,
            "reason": reason,
            "repairs": default_repairs,
        }

    @staticmethod
    def extract_credentials_from_secret_value(secret_value: str) -> dict:
        lines = secret_value.splitlines()
        credentials = {}
        key_lines = []
        is_private_key = False

        for line in lines:
            # Extract username
            if line.startswith("username:"):
                credentials["username"] = line.split(":", 1)[1].strip()
            # Start capturing the private key
            elif line.startswith("private_key:"):
                is_private_key = True
                key_lines.append(
                    line.split(":", 1)[1].strip()
                )  # Include content after "private_key:"
            # Capture private key's other lines
            elif is_private_key:
                key_lines.append(line.strip())

        # Join the captured lines to form the complete private key
        credentials["private_key"] = "\n".join(key_lines)
        return credentials

    async def get_secret_data_for_ssh_key_auth(self, project_id: str, secret_id: str) -> dict:
        secret_detail = f"projects/{project_id}/secrets/{secret_id}/versions/latest"
        client = SecretManagerServiceAsyncClient()
        response = await client.access_secret_version(request={"name": secret_detail})
        secret_value = response.payload.data.decode("UTF-8").strip()
        credentials = self.extract_credentials_from_secret_value(secret_value)
        return credentials

    async def _establish_connection(
        self,
        *,
        ip_address: str,
        credentials: dict[str, str],
        auth_type: AuthType,
        connect_timeout: float,
        prefix_log: str = "",
    ) -> asyncssh.SSHClientConnection:
        """Establish SSH connection with given parameters."""
        options = {
            "known_hosts": None,
            "connect_timeout": connect_timeout,
            "username": credentials["username"],
        }

        if auth_type == AuthType.SSH_KEY:
            # Use SSH key authentication
            private_key = credentials.get("private_key")
            if not private_key:
                raise ValueError("SSH private key not found in credentials")

            # Import the private key
            try:
                imported_key = asyncssh.import_private_key(private_key)
                options["client_keys"] = [imported_key]
                options["password"] = None
            except Exception as e:
                raise ValueError(f"Invalid SSH private key format: {e}") from e
        else:
            # Use password authentication (default)
            options["client_keys"] = None
            options["password"] = credentials["password"]

        self.logger.info(
            f"{prefix_log}, Attempting {auth_type.value} connection to {ip_address}..."
        )
        start_time = time.perf_counter()
        connection = await asyncssh.connect(ip_address, **options)
        self.logger.info(
            f"{prefix_log}, {auth_type.value.title()} connection successful in {round(time.perf_counter() - start_time, 2)} seconds"
        )
        return connection

    async def get_connection(
        self, instance_pyd_model: InstancePydModel, connect_timeout: float, prefix_log: str = ""
    ) -> tuple[asyncssh.SSHClientConnection | None, dict[str, Any]]:
        """Establishes an AsyncSSH connection to a server.

        Args:
            instance_pyd_model: Server details including ID and IP address
            connect_timeout: Connection timeout in seconds
            prefix_log: Prefix for log messages
        Returns:
            tuple containing:
            - SSHClientConnection if successful, None otherwise
            - dict with connection status, cause, reason and repair steps
        """
        ip_address = instance_pyd_model.host_ip_address
        host_secret_id = f"server-ssh-{instance_pyd_model.host_secret_id}"
        project_id = self.config.data["google"]["project"]

        # Get and validate credentials
        credentials_result = await self._get_and_validate_credentials(
            project_id, host_secret_id, instance_pyd_model.auth_type
        )
        if credentials_result["error"]:
            return None, credentials_result["error"]

        credentials = credentials_result["credentials"]

        # Attempt connection with error handling
        return await self._attempt_connection(
            ip_address=ip_address,
            credentials=credentials,
            auth_type=instance_pyd_model.auth_type,
            connect_timeout=connect_timeout,
            host_secret_id=host_secret_id,
            prefix_log=prefix_log,
        )

    async def _get_and_validate_credentials(
        self, project_id: str, host_secret_id: str, auth_type: AuthType
    ) -> dict[str, Any]:
        """Get credentials from secret manager and validate based on auth type."""
        if auth_type == AuthType.PASSWORD:
            credentials = await get_secret_data(project_id, host_secret_id)
        else:
            credentials = await self.get_secret_data_for_ssh_key_auth(
                project_id, host_secret_id
            )

        if not credentials:
            auth_type_desc = "password" if auth_type == AuthType.PASSWORD else "SSH key"
            return {
                "error": self._build_error_response(
                    alarm_type=SERVER_MISSING_CREDENTIALS_ALARM_TYPE,
                    reason=f"SSH credentials not found in Google Secret Manager for {auth_type_desc} authentication",
                    default_repairs=[
                        f"Update secret manager with server username and {auth_type_desc}."
                    ],
                ),
                "credentials": None,
            }

        # Validate and preprocess credentials based on the auth type
        validation_error = self._validate_credentials_format(credentials, auth_type)
        if validation_error:
            return {"error": validation_error, "credentials": None}

        return {"error": None, "credentials": credentials}

    def _validate_credentials_format(
        self, credentials: dict[str, str], auth_type: AuthType
    ) -> dict[str, Any] | None:
        """Validate credentials format based on the authentication type."""
        if auth_type == AuthType.SSH_KEY:
            if "username" not in credentials or "private_key" not in credentials:
                return self._build_error_response(
                    alarm_type=SERVER_MISSING_CREDENTIALS_ALARM_TYPE,
                    reason="SSH key credentials incomplete - missing username or private_key",
                    default_repairs=[
                        "Ensure secret contains both 'username' and 'private_key' fields."
                    ],
                )

            # Validate SSH private key format
            private_key = credentials["private_key"]
            if not self._is_valid_ssh_private_key_format(private_key):
                return self._build_error_response(
                    alarm_type=SERVER_MISSING_CREDENTIALS_ALARM_TYPE,
                    reason="SSH private key format is invalid - must be in standard PEM format",
                    default_repairs=[
                        "Ensure private key is in valid PEM format starting with '-----BEGIN' header.",
                        "Check for proper line formatting and key encoding.",
                    ],
                )
        else:
            if "username" not in credentials or "password" not in credentials:
                return self._build_error_response(
                    alarm_type=SERVER_MISSING_CREDENTIALS_ALARM_TYPE,
                    reason="Password credentials incomplete - missing username or password",
                    default_repairs=[
                        "Ensure secret contains both 'username' and 'password' fields."
                    ],
                )
        return None

    async def _attempt_connection(
        self,
        *,
        ip_address: str,
        credentials: dict[str, str],
        auth_type: AuthType,
        connect_timeout: float,
        host_secret_id: str,
        prefix_log: str,
    ) -> tuple[asyncssh.SSHClientConnection | None, dict[str, Any]]:
        """Attempt SSH connection with comprehensive error handling."""
        try:
            connection = await self._establish_connection(
                ip_address=ip_address,
                credentials=credentials,
                auth_type=auth_type,
                connect_timeout=connect_timeout,
                prefix_log=prefix_log,
            )
            return connection, {
                "status": Status.OK.value,
                "reason": "SSH connection successful",
            }

        except (asyncssh.PermissionDenied, PermissionError):
            return self._handle_authentication_error(
                ip_address, auth_type, host_secret_id, prefix_log
            )

        except (asyncssh.TimeoutError, TimeoutError):
            return self._handle_timeout_error(ip_address, connect_timeout, prefix_log)

        except ConnectionRefusedError:
            return self._handle_connection_refused_error(ip_address, prefix_log)

        except OSError as e:
            return self._handle_network_error(ip_address, e, prefix_log)

        except ValueError as e:
            return self._handle_ssh_key_validation_error(e, prefix_log)

        except asyncssh.Error as e:
            return self._handle_unexpected_ssh_error(ip_address, e, prefix_log)

    def _handle_authentication_error(
        self, ip_address: str, auth_type: AuthType, host_secret_id: str, prefix_log: str
    ) -> tuple[None, dict[str, Any]]:
        """Handle SSH authentication failures."""
        auth_method = "SSH key" if auth_type == AuthType.SSH_KEY else "username/password"
        msg = f"SSH authentication failed for host {ip_address} - invalid {auth_method}"
        self.logger.error(f"{prefix_log}, {msg}")

        # Clear credentials on auth failure
        self.config.data[f"server-ssh-{host_secret_id}"] = {}

        return None, self._build_error_response(
            alarm_type=SERVER_PERMISSION_DENIED_ALARM_TYPE,
            reason=f"SSH authentication failed - invalid {auth_method}",
            default_repairs=[f"Verify SSH {auth_method} configuration."],
        )

    def _handle_timeout_error(
        self, ip_address: str, connect_timeout: float, prefix_log: str
    ) -> tuple[None, dict[str, Any]]:
        """Handle SSH connection timeout errors."""
        msg = f"SSH connection to host {ip_address} timed out after {connect_timeout} seconds"
        self.logger.error(f"{prefix_log}, {msg}")

        return None, self._build_error_response(
            alarm_type=SERVER_CONNECTION_TIMED_OUT_ALARM_TYPE,
            reason=f"SSH connection timeout after {connect_timeout}s",
            default_repairs=[
                "Check network path to server.",
                "Verify server SSH daemon status.",
                "Increase connection timeout if needed.",
            ],
        )

    def _handle_connection_refused_error(
        self, ip_address: str, prefix_log: str
    ) -> tuple[None, dict[str, Any]]:
        """Handle SSH connection refused errors."""
        msg = (
            f"SSH connection to host {ip_address} was refused - SSH service may not be running"
        )
        self.logger.error(f"{prefix_log}, {msg}")

        return None, self._build_error_response(
            alarm_type=SERVER_CONNECTION_REFUSED_ALARM_TYPE,
            reason="SSH connection refused - service unavailable",
            default_repairs=[
                "Verify server SSH daemon is running.",
                "Check firewall rules on server and network path.",
            ],
        )

    def _handle_network_error(
        self, ip_address: str, error: OSError, prefix_log: str
    ) -> tuple[None, dict[str, Any]]:
        """Handle network connectivity errors."""
        msg = f"Host {ip_address} is unreachable - network connectivity issue: {error!s}"
        self.logger.error(f"{prefix_log}, {msg}")

        return None, self._build_error_response(
            alarm_type=SERVER_HOST_UNREACHABLE_ALARM_TYPE,
            reason=f"Host unreachable - network error: {error!s}",
            default_repairs=[
                "Verify server power status and network connectivity.",
                "Check DNS and routing.",
            ],
        )

    def _handle_ssh_key_validation_error(
        self, error: ValueError, prefix_log: str
    ) -> tuple[None, dict[str, Any]]:
        """Handle SSH key validation errors."""
        self.logger.error(f"{prefix_log}, SSH key validation error: {error}")

        return None, self._build_error_response(
            alarm_type=SERVER_MISSING_CREDENTIALS_ALARM_TYPE,
            reason=f"SSH key validation failed: {error}",
            default_repairs=["Verify SSH private key format and encoding."],
        )

    def _handle_unexpected_ssh_error(
        self, ip_address: str, error: asyncssh.Error, prefix_log: str
    ) -> tuple[None, dict[str, Any]]:
        """Handle unexpected SSH errors."""
        self.logger.error(
            f"{prefix_log}, Exception: [ Unexpected SSH connection error ] to host {ip_address}: {type(error).__name__} - {error!s}",
            exc_info=True,
        )

        return None, self._build_error_response(
            alarm_type=SERVER_UNEXPECTED_CONNECTION_ERROR_ALARM_TYPE,
            reason=f"Unexpected SSH error: {type(error).__name__} - {error!s}",
            default_repairs=["Investigate application logs for details."],
        )

    # @staticmethod
    # def _is_valid_ssh_private_key_format(private_key: str) -> bool:
    #     """
    #     Validate that the SSH private key has the basic structure of a PEM-formatted key.
    #
    #     Args:
    #         private_key: SSH private key string
    #
    #     Returns:
    #         bool: True if key has valid format, False otherwise
    #     """
    #
    #     # Check for PEM format headers and footers
    #     pem_headers = [
    #         "-----BEGIN PRIVATE KEY-----",
    #         "-----BEGIN RSA PRIVATE KEY-----",
    #         "-----BEGIN DSA PRIVATE KEY-----",
    #         "-----BEGIN EC PRIVATE KEY-----",
    #         "-----BEGIN OPENSSH PRIVATE KEY-----",
    #     ]
    #
    #     pem_footers = [
    #         "-----END PRIVATE KEY-----",
    #         "-----END RSA PRIVATE KEY-----",
    #         "-----END DSA PRIVATE KEY-----",
    #         "-----END EC PRIVATE KEY-----",
    #         "-----END OPENSSH PRIVATE KEY-----",
    #     ]
    #
    #     # Check if key starts with any valid header
    #     has_valid_header = any(private_key.strip().startswith(header) for header in pem_headers)
    #
    #     # Check if key ends with any valid footer
    #     has_valid_footer = any(private_key.strip().endswith(footer) for footer in pem_footers)
    #
    #     return has_valid_header and has_valid_footer

    @staticmethod
    def _is_valid_ssh_private_key_format(private_key: str) -> bool:
        """
        Validate that the SSH private key has the basic structure of a PEM/OpenSSH key,
        without embedding literal private-key headers in the source (avoids hook matches).

        Args:
            private_key: SSH private key string

        Returns:
            bool: True if key has valid format, False otherwise
        """
        s = (private_key or "").strip()
        if not s:
            return False

        # Supported key kinds (blank = generic "PRIVATE KEY")
        key_kinds = ("", "RSA", "DSA", "EC", "OPENSSH")

        def make_header(kind: str) -> str:
            # Builds the equivalent of: "-----BEGIN <KIND> PRIVATE KEY-----"
            parts = ["-----", "BEGIN", " "]
            if kind:
                parts.append(kind)
                parts.append(" ")
            parts.extend(["PRIVATE", " KEY", "-----"])
            return "".join(parts)

        def make_footer(kind: str) -> str:
            # Builds the equivalent of: "-----END <KIND> PRIVATE KEY-----"
            parts = ["-----", "END", " "]
            if kind:
                parts.append(kind)
                parts.append(" ")
            parts.extend(["PRIVATE", " KEY", "-----"])
            return "".join(parts)

        has_valid_header = any(s.startswith(make_header(k)) for k in key_kinds)
        has_valid_footer = any(s.endswith(make_footer(k)) for k in key_kinds)
        return has_valid_header and has_valid_footer

    @staticmethod
    def close_connection(
        connection: SSHClientConnection, manager_instance: str, prefix_log: str = ""
    ):
        """Close SSH connection safely."""
        logger = logging.getLogger("airspan_acp_metrics.SSHConnectionManager.close_connection")

        try:
            if connection is None:
                return
            connection.close()
            logger.info(f"{prefix_log}, Closed SSH connection for {manager_instance}")
        except Exception as e:
            logger.warning(
                f"{prefix_log}, Error closing SSH connection for {manager_instance}: {e}"
            )
