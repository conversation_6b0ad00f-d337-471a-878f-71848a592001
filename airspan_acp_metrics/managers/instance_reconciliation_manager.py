import logging

from da_common.config import Config
from da_common.models import Status
from dal_pubsub.pubsub import PubSub

from airspan_acp_metrics.database.services.instance_db_service import InstanceDbService
from airspan_acp_metrics.database.session_management import get_db_session
from airspan_acp_metrics.managers.alarm_manager import AlarmManager
from airspan_acp_metrics.pyd_models.alarm_enums import AlarmType
from airspan_acp_metrics.pyd_models.instance_pyd_model import InstancePydModel
from airspan_acp_metrics.pyd_models.instance_pyd_model import InstanceUpdatePydModel


class InstanceReconciliationManager:
    """Manages reconciliation of instances between external services and database."""

    def __init__(self, config: Config, async_session_maker):
        self.config = config
        self.async_session_maker = async_session_maker
        self.clients = self.config.build_clients()
        self.logger = logging.getLogger(
            "airspan_acp_metrics.managers.instance_reconciliation_manager"
        )

        # Initialize PubSub for alarms
        self.pubsub = PubSub(config=config)
        self.alarm_topic = config.data["pubsub"].get("alarm", {}).get("topic", "nms-alarms")

    async def _raise_service_connectivity_alarm(
        self,
        db_session,
        alarm_type: AlarmType,
        service_name: str,
        error_message: str,
        prefix_log: str = "",
    ):
        """Raise an alarm for service connectivity issues."""
        try:
            alarm_manager = AlarmManager(db_session, self.pubsub, self.alarm_topic)

            # Create a dummy instance for system-level alarms
            from airspan_acp_metrics.pyd_models.instance_pyd_model import InstancePydModel

            system_instance = InstancePydModel(
                manager_instance="SYSTEM",
                url="system://internal",
                host_ip_address="127.0.0.1",
                host_secret_id="system",
                metrics_collection_status=Status.ERROR,
                reason=error_message,
            )

            await alarm_manager.raise_alarm(
                alarm_type=alarm_type,
                name=f"{service_name} connectivity failure",
                source="instance_reconciliation_manager",
                instance=system_instance,
                description=error_message,
                prefix_log=prefix_log,
            )
        except Exception as e:
            self.logger.error(f"{prefix_log}, Error raising service connectivity alarm: {e}")

    async def _handle_component_missing_alarm(
        self,
        db_session,
        manager_instance: str,
        prefix_log: str = "",
    ):
        """Handle alarm for when an ACP instance has no corresponding component in inventory."""
        try:
            alarm_manager = AlarmManager(db_session, self.pubsub, self.alarm_topic)

            instance = InstancePydModel(
                manager_instance=manager_instance,
                url=f"https://{manager_instance}/api/20.5",
                host_ip_address=None,
                host_secret_id=None,
                metrics_collection_status=Status.ERROR,
                reason=f"Component {manager_instance} not found in inventory",
            )

            error_message = f"ACP component '{manager_instance}' not found in inventory-manager"
            self.logger.warning(f"{prefix_log}, {error_message}")

            await alarm_manager.raise_alarm(
                alarm_type=AlarmType.INVENTORY_DATA_MISSING,
                name="Component missing in inventory",
                source="instance_reconciliation_manager",
                instance=instance,
                description=error_message,
                prefix_log=prefix_log,
            )

        except Exception as e:
            self.logger.error(f"{prefix_log}, Error handling component missing alarm: {e}")

    async def _handle_server_mapping_missing_alarm(
        self,
        db_session,
        manager_instance: str,
        node_id: str,
        reason: str,
        prefix_log: str = "",
    ):
        """Handle alarm for when a component exists but server mapping is missing."""
        try:
            alarm_manager = AlarmManager(db_session, self.pubsub, self.alarm_topic)

            instance = InstancePydModel(
                manager_instance=manager_instance,
                url=f"https://{manager_instance}/api/20.5",
                host_ip_address=None,
                host_secret_id=None,
                metrics_collection_status=Status.ERROR,
                reason=reason,
            )

            error_message = f"Server mapping missing for component '{manager_instance}' (node: {node_id}) - {reason}"
            self.logger.warning(f"{prefix_log}, {error_message}")

            await alarm_manager.raise_alarm(
                alarm_type=AlarmType.ORCHESTRATOR_DATA_MISSING,
                name="Server mapping missing",
                source="instance_reconciliation_manager",
                instance=instance,
                description=error_message,
                prefix_log=prefix_log,
            )

        except Exception as e:
            self.logger.error(f"{prefix_log}, Error handling server mapping missing alarm: {e}")

    async def _handle_partial_server_data_alarm(
        self,
        db_session,
        manager_instance: str,
        node_id: str,
        server_details: dict[str, str],
        prefix_log: str = "",
    ):
        """Handle alarm for when server exists but has missing host_secret_id or host_ip_address."""
        try:
            alarm_manager = AlarmManager(db_session, self.pubsub, self.alarm_topic)

            missing_fields = []
            if not server_details.get("host_secret_id"):
                missing_fields.append("host_secret_id")
            if not server_details.get("host_ip_address"):
                missing_fields.append("host_ip_address")

            reason = f"Missing {' and '.join(missing_fields)} for server in nms-orchestrator"

            instance = InstancePydModel(
                manager_instance=manager_instance,
                url=f"https://{manager_instance}/api/20.5",
                host_ip_address=server_details.get("host_ip_address"),
                host_secret_id=server_details.get("host_secret_id"),
                metrics_collection_status=Status.ERROR,
                reason=reason,
            )

            error_message = f"Partial server data for component '{manager_instance}' (node: {node_id}) - {reason}"
            self.logger.warning(f"{prefix_log}, {error_message}")

            await alarm_manager.raise_alarm(
                alarm_type=AlarmType.ORCHESTRATOR_DATA_MISSING,
                name="Partial server data",
                source="instance_reconciliation_manager",
                instance=instance,
                description=error_message,
                prefix_log=prefix_log,
            )

        except Exception as e:
            self.logger.error(f"{prefix_log}, Error handling partial server data alarm: {e}")

    async def _clear_data_missing_alarm(
        self,
        db_session,
        manager_instance: str,
        alarm_type: AlarmType,
        prefix_log: str = "",
    ):
        """Clear data missing alarm for a specific instance."""
        try:
            alarm_manager = AlarmManager(db_session, self.pubsub, self.alarm_topic)

            instance = InstancePydModel(
                manager_instance=manager_instance,
                url=f"https://{manager_instance}/api/20.5",
                host_ip_address="127.0.0.1",  # Dummy values for clearing
                host_secret_id="dummy",
                metrics_collection_status=Status.OK,
                reason="Data now available",
            )

            self.logger.info(
                f"{prefix_log}, Clearing {alarm_type} alarm for {manager_instance}"
            )
            await alarm_manager.clear_alarm(
                alarm_type=alarm_type,
                instance=instance,
                resolver="instance_reconciliation_manager",
                prefix_log=prefix_log,
            )

        except Exception as e:
            self.logger.error(f"{prefix_log}, Error clearing data missing alarm: {e}")

    async def _handle_instance_data_alarms(
        self,
        db_session,
        external_instances: list[dict],
        server_id_mappings: dict[str, tuple[dict[str, str], dict[str, str]]],
        instance_node_mappings: dict[str, str],
        prefix_log: str = "",
    ):
        """Handle granular alarms for each instance based on missing data components."""
        try:
            for external_instance in external_instances:
                manager_instance = external_instance["manager_instance"]
                instance_prefix_log = self._get_log_prefix(manager_instance)

                # Get node_id for this instance (if it has a component in inventory)
                node_id = instance_node_mappings.get(manager_instance)

                # Check if this instance has server mapping data
                server_details, reason = server_id_mappings.get(manager_instance, ({}, {}))

                if not node_id:
                    # No component found in inventory for this ACP instance
                    await self._handle_component_missing_alarm(
                        db_session,
                        manager_instance,
                        instance_prefix_log,
                    )
                elif not server_details:
                    # Component exists but no server mapping at all
                    if reason.get("reason"):
                        # We have a specific reason from orchestrator
                        await self._handle_server_mapping_missing_alarm(
                            db_session,
                            manager_instance,
                            node_id,
                            reason.get("reason"),
                            instance_prefix_log,
                        )
                    else:
                        # This shouldn't happen if component exists, but handle gracefully
                        await self._handle_server_mapping_missing_alarm(
                            db_session,
                            manager_instance,
                            node_id,
                            f"No server mapping found for node {node_id}",
                            instance_prefix_log,
                        )
                elif not server_details.get("host_secret_id") or not server_details.get(
                    "host_ip_address"
                ):
                    # Partial server data - server exists but missing fields
                    await self._handle_partial_server_data_alarm(
                        db_session,
                        manager_instance,
                        node_id,
                        server_details,
                        instance_prefix_log,
                    )
                else:
                    # Complete server data - clear any existing alarms for this instance
                    await self._clear_data_missing_alarm(
                        db_session,
                        manager_instance,
                        AlarmType.INVENTORY_DATA_MISSING,
                        instance_prefix_log,
                    )
                    await self._clear_data_missing_alarm(
                        db_session,
                        manager_instance,
                        AlarmType.ORCHESTRATOR_DATA_MISSING,
                        instance_prefix_log,
                    )

        except Exception as e:
            self.logger.error(
                f"{prefix_log}, Error handling instance data alarms: {e}", exc_info=True
            )

    def _get_log_prefix(self, instance_name: str | None = None) -> str:
        """Generate standardized log prefix for tracking."""
        if instance_name:
            return f"InstanceReconciliation: {instance_name},"
        else:
            return "InstanceReconciliation:"

    def _get_auth_headers(self) -> dict[str, str]:
        """Get authentication headers for inter-service API calls."""
        try:
            api_key = self.config.data["security"]["apikeys"]["nms"]["key"]
            return {"x-api-key": api_key}
        except KeyError as e:
            self.logger.error(f"Missing API key configuration: {e}")
            return {}

    def _get_inventory_client(self):
        """Get the inventory manager client."""
        return self.clients["inventory-manager"]

    def _get_orchestrator_client(self):
        """Get the orchestrator client."""
        return self.clients["nms-orchestrator"]

    def _get_airspan_acp_agent_client(self):
        """Get the Airspan ACP agent client."""
        return self.clients["airspan-acp-agent"]

    async def _fetch_components_from_inventory(self, prefix_log: str = "") -> list[dict]:
        """Fetch components from inventory service."""
        try:
            self.logger.info(f"{prefix_log}, Fetching components from inventory service")
            inventory_client = self._get_inventory_client()
            headers = self._get_auth_headers()
            response = await inventory_client.get("/components", headers=headers)
            response.raise_for_status()
            components = response.json()

            if not components:
                self.logger.warning(f"{prefix_log}, No components found in inventory service")
                return []

            self.logger.info(
                f"{prefix_log}, Found {len(components)} components in inventory service"
            )
            return components

        except Exception as e:
            self.logger.error(
                f"{prefix_log}, Error fetching components from inventory service: {e}",
                exc_info=True,
            )

            # Raise alarm for inventory service connectivity failure
            async with get_db_session(self.async_session_maker) as db_session:
                await self._raise_service_connectivity_alarm(
                    db_session,
                    AlarmType.INVENTORY_MANAGER_UNREACHABLE,
                    "Inventory Manager",
                    f"Failed to fetch components from inventory service: {e}",
                    prefix_log,
                )

            return []

    @staticmethod
    def _extract_instance_node_mappings(
        components: list[dict], prefix_log: str = ""
    ) -> dict[str, str]:
        """Extract instance to node ID mappings from components."""
        instance_node_mappings = {
            component["component_id"]: component["node_id"]
            for component in components
            if component["component_type"] == "ACP"
        }

        logging.getLogger("airspan_acp_metrics.managers.instance_reconciliation_manager").info(
            f"{prefix_log}, Extracted {len(instance_node_mappings)} ACP instance-to-node mappings from inventory"
        )

        return instance_node_mappings

    async def _fetch_server_id_for_node(
        self, node_id: str, prefix_log: str = ""
    ) -> tuple[dict[str, str], dict[str, str]]:
        """Fetch server ID for a specific node ID from orchestrator."""
        server_details = {}
        try:
            self.logger.info(
                f"{prefix_log}, Fetching server ID for node {node_id} from orchestrator"
            )
            orchestrator_client = self._get_orchestrator_client()
            headers = self._get_auth_headers()
            response = await orchestrator_client.get(
                f"/server/nodes/{node_id}", headers=headers
            )
            response.raise_for_status()
            response_json = response.json()

            if not response_json:
                reason = f"Node {node_id} not found in orchestrator"
                self.logger.warning(f"{prefix_log}, {reason}")
                reason_dict = {"reason": reason}

                return server_details, reason_dict

            server = response_json.get("server")
            if server:
                host_secret_id = server.get("server_id")
                host_ip_address = server.get("ip_address")
                server_details = {
                    "host_secret_id": host_secret_id,
                    "host_ip_address": host_ip_address,
                }

                self.logger.info(
                    f"{prefix_log}, Found server with details {server_details} for node {node_id}"
                )
                return server_details, {}
            else:
                reason = f"No server found for node {node_id} in orchestrator"
                self.logger.warning(
                    f"{prefix_log}, No server found for node {node_id} in orchestrator"
                )
                reason_dict = {"reason": reason}

                return server_details, reason_dict

        except Exception as e:
            reason = f"Error fetching server ID for node {node_id} from orchestrator: {e}"
            self.logger.error(
                f"{prefix_log}, {reason}",
                exc_info=True,
            )

            # Raise alarm for orchestrator service connectivity failure
            async with get_db_session(self.async_session_maker) as db_session:
                await self._raise_service_connectivity_alarm(
                    db_session,
                    AlarmType.NMS_ORCHESTRATOR_UNREACHABLE,
                    "NMS Orchestrator",
                    reason,
                    prefix_log,
                )

            return server_details, {"reason": reason}

    async def _build_node_server_mappings(
        self, node_ids: list[str], prefix_log: str = ""
    ) -> dict[str, tuple[dict[str, str], dict[str, str]]]:
        """Build mappings from node IDs to server IDs."""
        self.logger.info(f"{prefix_log}, Building server ID mappings for {len(node_ids)} nodes")
        node_server_mappings = {}

        for node_id in node_ids:
            server_details, reason = await self._fetch_server_id_for_node(node_id, prefix_log)
            if server_details:
                node_server_mappings[node_id] = (server_details, {})
            else:
                node_server_mappings[node_id] = (server_details, reason)

        self.logger.info(
            f"{prefix_log}, Successfully mapped {len(node_server_mappings)} nodes to server IDs"
        )
        return node_server_mappings

    def _build_instance_server_mappings(
        self,
        instance_node_mappings: dict[str, str],
        node_server_mappings: dict[str, tuple[dict[str, str], dict[str, str]]],
        prefix_log: str = "",
    ) -> dict[str, tuple[dict[str, str], dict[str, str]]]:
        """Build mappings from instance IDs to server IDs."""
        instance_server_mappings = {}

        for instance, node_id in instance_node_mappings.items():
            instance_server_mappings[instance] = node_server_mappings.get(node_id, ({}, {}))

        self.logger.info(
            f"{prefix_log}, Built server ID mappings for {len(instance_server_mappings)} instances"
        )

        return instance_server_mappings

    async def get_instance_server_id_mappings(
        self, prefix_log: str = ""
    ) -> dict[str, tuple[dict[str, str], dict[str, str]]]:
        """Get the server ID mappings for all instances."""
        try:
            self.logger.info(f"{prefix_log}, Starting instance server ID mapping process")

            # Fetch components from inventory
            components = await self._fetch_components_from_inventory(prefix_log)
            if not components:
                self.logger.warning(
                    f"{prefix_log}, No components available, returning empty server ID mappings"
                )
                return {}

            # Extract instance to node mappings
            instance_node_mappings = self._extract_instance_node_mappings(
                components, prefix_log
            )
            if not instance_node_mappings:
                self.logger.warning(f"{prefix_log}, No ACP components found in inventory")
                return {}

            # Build node to server mappings
            node_ids = list(instance_node_mappings.values())
            node_server_mappings = await self._build_node_server_mappings(node_ids, prefix_log)

            # Build final instance to server mappings
            instance_server_mappings = self._build_instance_server_mappings(
                instance_node_mappings, node_server_mappings, prefix_log
            )

            self.logger.info(
                f"{prefix_log}, Instance server ID mapping process completed successfully"
            )
            return instance_server_mappings

        except Exception as e:
            self.logger.error(
                f"{prefix_log}, Error in instance server ID mapping process: {e}", exc_info=True
            )
            return {}

    async def get_instance_server_id_mappings_with_nodes(
        self, prefix_log: str = ""
    ) -> tuple[dict[str, tuple[dict[str, str], dict[str, str]]], dict[str, str]]:
        """Get the server ID mappings and instance->node mappings for all instances."""
        try:
            self.logger.info(
                f"{prefix_log}, Starting instance server ID mapping process with node tracking"
            )

            # Fetch components from inventory
            components = await self._fetch_components_from_inventory(prefix_log)
            if not components:
                self.logger.warning(
                    f"{prefix_log}, No components available, returning empty server ID mappings"
                )
                return {}, {}

            # Extract instance to node mappings
            instance_node_mappings = self._extract_instance_node_mappings(
                components, prefix_log
            )
            if not instance_node_mappings:
                self.logger.warning(f"{prefix_log}, No ACP components found in inventory")
                return {}, {}

            # Build node to server mappings
            node_ids = list(instance_node_mappings.values())
            node_server_mappings = await self._build_node_server_mappings(node_ids, prefix_log)

            # Build final instance to server mappings
            instance_server_mappings = self._build_instance_server_mappings(
                instance_node_mappings, node_server_mappings, prefix_log
            )

            self.logger.info(
                f"{prefix_log}, Instance server ID mapping process with node tracking completed successfully"
            )
            return instance_server_mappings, instance_node_mappings

        except Exception as e:
            self.logger.error(
                f"{prefix_log}, Error in instance server ID mapping process with node tracking: {e}",
                exc_info=True,
            )
            return {}, {}

    async def _fetch_airspan_acp_instances(self, prefix_log: str = "") -> list[dict]:
        """Fetch instances from Airspan ACP Agent service."""
        try:
            self.logger.info(f"{prefix_log}, Fetching instances from Airspan ACP Agent service")
            airspan_acp_client = self._get_airspan_acp_agent_client()
            headers = self._get_auth_headers()
            response = await airspan_acp_client.get("/acp_instances", headers=headers)
            response.raise_for_status()
            instances = response.json()

            self.logger.info(
                f"{prefix_log}, Found {len(instances)} instances in Airspan ACP Agent service"
            )
            return instances

        except Exception as e:
            self.logger.error(
                f"{prefix_log}, Error fetching Airspan ACP instances: {e}", exc_info=True
            )

            # Raise alarm for Airspan ACP agent connectivity failure
            async with get_db_session(self.async_session_maker) as db_session:
                await self._raise_service_connectivity_alarm(
                    db_session,
                    AlarmType.AIRSPAN_ACP_AGENT_UNREACHABLE,
                    "Airspan ACP Agent",
                    f"Failed to fetch Airspan ACP instances: {e}",
                    prefix_log,
                )

            return []

    async def _fetch_database_instances(self, db_session, prefix_log: str = ""):
        """Fetch instances from the database."""
        try:
            instance_db_service = InstanceDbService(db_session)
            instances = await instance_db_service.get_all_acp_instances()

            self.logger.info(f"{prefix_log}, Found {len(instances)} instances in database")
            return instances

        except Exception as e:
            self.logger.error(
                f"{prefix_log}, Error fetching database instances: {e}", exc_info=True
            )
            return []

    def _identify_missing_instances(
        self, external_instances: list[dict], database_instances, prefix_log: str = ""
    ) -> list[dict]:
        """Identify instances that exist externally but not in the database."""
        local_instance_names = {instance.manager_instance for instance in database_instances}

        missing_instances = []
        for external_instance in external_instances:
            manager_instance = external_instance["manager_instance"]
            if manager_instance not in local_instance_names:
                self.logger.info(
                    f"{prefix_log}, Instance {manager_instance} found in external service but missing from database"
                )
                missing_instances.append(external_instance)

        if missing_instances:
            self.logger.info(
                f"{prefix_log}, Found {len(missing_instances)} instances missing from database"
            )
        else:
            self.logger.info(f"{prefix_log}, No missing instances found")

        return missing_instances

    def _identify_extra_instances(
        self, external_instances: list[dict], database_instances, prefix_log: str = ""
    ) -> list:
        """Identify instances that exist in database but not in external service."""
        external_instance_names = {
            instance["manager_instance"] for instance in external_instances
        }

        extra_instances = []
        for db_instance in database_instances:
            if db_instance.manager_instance not in external_instance_names:
                self.logger.info(
                    f"{prefix_log}, Instance {db_instance.manager_instance} found in database but missing from external service"
                )
                extra_instances.append(db_instance)

        if extra_instances:
            self.logger.info(
                f"{prefix_log}, Found {len(extra_instances)} instances in database that no longer exist externally"
            )
        else:
            self.logger.info(f"{prefix_log}, No extra instances found")

        return extra_instances

    def _identify_instances_needing_update(
        self,
        external_instances: list[dict],
        database_instances,
        server_id_mappings: dict[str, tuple[dict[str, str], dict[str, str]]],
        prefix_log: str = "",
    ) -> list[tuple]:
        """Identify instances that exist in both but have different data."""
        db_instances_by_name = {
            instance.manager_instance: instance for instance in database_instances
        }
        instances_needing_update = []

        for external_instance in external_instances:
            manager_instance = external_instance["manager_instance"]
            db_instance = db_instances_by_name.get(manager_instance)

            if db_instance:
                # Check if an update is needed
                server_details, reason = server_id_mappings.get(manager_instance, ({}, {}))
                expected_host_secret_id = server_details.get("host_secret_id")
                expected_host_ip_address = server_details.get("host_ip_address")
                expected_url = external_instance["url"]

                # Compare relevant fields
                needs_update = (
                    db_instance.url != expected_url
                    or db_instance.host_secret_id != expected_host_secret_id
                    or db_instance.host_ip_address != expected_host_ip_address
                    #     no need to check if the reason field is changed because this field is also set by ssh_connectivity_worker
                )

                if needs_update:
                    self.logger.info(
                        f"{prefix_log}, Instance {manager_instance} requires updates"
                    )
                    instances_needing_update.append(
                        (
                            external_instance,
                            db_instance,
                            expected_host_secret_id,
                            expected_host_ip_address,
                            reason,
                        )
                    )

        if instances_needing_update:
            self.logger.info(
                f"{prefix_log}, Found {len(instances_needing_update)} instances requiring updates"
            )
        else:
            self.logger.info(f"{prefix_log}, No instances requiring updates")

        return instances_needing_update

    def _create_instance_pyd_model(
        self,
        acp_agent_instance: dict,
        server_id_mappings: dict[str, tuple[dict[str, str], dict[str, str]]],
        prefix_log: str = "",
    ) -> InstancePydModel:
        """Create an InstancePydModel from ACP agent instance data."""
        manager_instance = acp_agent_instance["manager_instance"]
        server_details, reason = server_id_mappings.get(manager_instance, ({}, {}))
        host_secret_id = server_details.get("host_secret_id")
        host_ip_address = server_details.get("host_ip_address")

        # Determine status and reason based on missing dependencies
        if host_secret_id is None or host_ip_address is None:
            status = Status.ERROR
            reason = reason.get("reason")
            if not reason:
                # Should not happen as we check for missing server details before calling this method
                reason = self.get_reason_for_empty_server_mapping(
                    host_secret_id, host_ip_address
                )
            self.logger.warning(f"{prefix_log}, {reason}")
        else:
            status = Status.OK
            reason = "Ready for metrics collection"
            self.logger.info(
                f"{prefix_log}, Instance {manager_instance} created with complete server mapping"
            )

        return InstancePydModel(
            manager_instance=manager_instance,
            url=acp_agent_instance["url"],
            host_ip_address=host_ip_address,
            host_secret_id=host_secret_id,
            metrics_collection_status=status,
            reason=reason,
        )

    async def _create_missing_instances(
        self,
        missing_instances: list[dict],
        instance_db_service: InstanceDbService,
        server_id_mappings: dict[str, tuple[dict[str, str], dict[str, str]]],
        prefix_log: str = "",
    ) -> int:
        """Create missing instances in the database."""
        created_count = 0

        self.logger.info(f"{prefix_log}, Creating {len(missing_instances)} missing instances")

        for acp_agent_instance in missing_instances:
            manager_instance = acp_agent_instance["manager_instance"]
            instance_prefix_log = self._get_log_prefix(manager_instance)

            try:
                instance_pyd_model = self._create_instance_pyd_model(
                    acp_agent_instance, server_id_mappings, instance_prefix_log
                )
                await instance_db_service.create_acp_instance(instance_pyd_model)
                created_count += 1
                self.logger.info(
                    f"{instance_prefix_log}, Successfully created instance with status {instance_pyd_model.metrics_collection_status}"
                )

            except Exception as e:
                self.logger.error(
                    f"{instance_prefix_log}, Error creating instance: {e}", exc_info=True
                )

        self.logger.info(
            f"{prefix_log}, Successfully created {created_count}/{len(missing_instances)} instances"
        )
        return created_count

    @staticmethod
    def get_reason_for_empty_server_mapping(
        expected_host_secret_id: str, expected_host_ip_address: str
    ) -> str:
        """Get the reason for an empty server mapping."""
        new_reason = "Missing "
        if not expected_host_ip_address:
            new_reason += "host IP address"
        if not expected_host_secret_id:
            new_reason += " and host secret id in nms-orchestrator"
        return new_reason

    async def _update_existing_instances(
        self,
        instances_needing_update: list[tuple],
        instance_db_service: InstanceDbService,
        prefix_log: str = "",
    ) -> int:
        """Update existing instances with new data."""
        updated_count = 0

        self.logger.info(f"{prefix_log}, Updating {len(instances_needing_update)} instances")

        for (
            external_instance,
            _,
            expected_host_secret_id,
            expected_host_ip_address,
            reason,
        ) in instances_needing_update:
            manager_instance = external_instance["manager_instance"]
            instance_prefix_log = self._get_log_prefix(manager_instance)

            try:
                # Determine new status and reason based on required fields
                if expected_host_secret_id is None or expected_host_ip_address is None:
                    new_status = Status.ERROR
                    new_reason = reason.get("reason")
                    if not reason:
                        new_reason = self.get_reason_for_empty_server_mapping(
                            expected_host_secret_id, expected_host_ip_address
                        )
                else:
                    # Both required fields are present - set to OK for SSH connectivity testing
                    new_status = Status.OK
                    new_reason = "Ready for metrics collection"

                update_instance_pyd_model = InstanceUpdatePydModel(
                    manager_instance=manager_instance,
                    url=external_instance["url"],
                    host_secret_id=expected_host_secret_id,
                    host_ip_address=expected_host_ip_address,
                    metrics_collection_status=new_status,
                    reason=new_reason,
                )

                acp_instance = await instance_db_service.update_acp_instance(
                    update_instance_pyd_model
                )
                if not acp_instance:
                    continue
                updated_count += 1
                self.logger.info(f"{instance_prefix_log}, Successfully updated instance")

            except Exception as e:
                self.logger.error(
                    f"{instance_prefix_log}, Error updating instance: {e}", exc_info=True
                )

        self.logger.info(
            f"{prefix_log}, Successfully updated {updated_count}/{len(instances_needing_update)} instances"
        )
        return updated_count

    async def _delete_extra_instances(
        self,
        extra_instances: list,
        instance_db_service: InstanceDbService,
        prefix_log: str = "",
    ) -> int:
        """Delete instances that no longer exist externally."""
        deleted_count = 0

        self.logger.info(
            f"{prefix_log}, Deleting {len(extra_instances)} instances no longer in external service"
        )

        for db_instance in extra_instances:
            manager_instance = db_instance.manager_instance
            instance_prefix_log = self._get_log_prefix(manager_instance)

            try:
                await instance_db_service.delete_acp_instance(manager_instance)
                deleted_count += 1
                self.logger.info(f"{instance_prefix_log}, Successfully deleted instance")

            except Exception as e:
                self.logger.error(
                    f"{instance_prefix_log}, Error deleting instance: {e}", exc_info=True
                )

        self.logger.info(
            f"{prefix_log}, Successfully deleted {deleted_count}/{len(extra_instances)} instances"
        )
        return deleted_count

    async def reconcile_airspan_acp_agent_instances(self, prefix_log: str = ""):
        """Reconcile Airspan ACP Agent instances with the database."""
        try:
            self.logger.info(
                f"{prefix_log}, Starting Airspan ACP Agent instance reconciliation"
            )

            # Fetch instances from external service
            external_instances = await self._fetch_airspan_acp_instances(prefix_log)
            if not external_instances:
                self.logger.warning(
                    f"{prefix_log}, No external instances found, skipping reconciliation"
                )
                return

            # Work with database
            async with get_db_session(self.async_session_maker) as db_session:
                try:
                    # Fetch instances from database
                    database_instances = await self._fetch_database_instances(
                        db_session, prefix_log
                    )

                    # Get server ID mappings for all instances and preserve instance->node mapping
                    (
                        server_id_mappings,
                        instance_node_mappings,
                    ) = await self.get_instance_server_id_mappings_with_nodes(prefix_log)

                    # Handle granular alarms for each instance based on data availability
                    await self._handle_instance_data_alarms(
                        db_session,
                        external_instances,
                        server_id_mappings,
                        instance_node_mappings,
                        prefix_log,
                    )

                    # Identify differences
                    missing_instances = self._identify_missing_instances(
                        external_instances, database_instances, prefix_log
                    )
                    extra_instances = self._identify_extra_instances(
                        external_instances, database_instances, prefix_log
                    )
                    instances_needing_update = self._identify_instances_needing_update(
                        external_instances, database_instances, server_id_mappings, prefix_log
                    )

                    # Perform reconciliation operations
                    operations_count = 0

                    # Create missing instances
                    if missing_instances:
                        # do not share session as it may prematurely close it
                        async with get_db_session(self.async_session_maker) as _db_session:
                            instance_db_service = InstanceDbService(_db_session)
                            created_count = await self._create_missing_instances(
                                missing_instances,
                                instance_db_service,
                                server_id_mappings,
                                prefix_log,
                            )
                            operations_count += created_count

                    # Update existing instances
                    if instances_needing_update:
                        async with get_db_session(self.async_session_maker) as _db_session:
                            instance_db_service = InstanceDbService(_db_session)
                            updated_count = await self._update_existing_instances(
                                instances_needing_update, instance_db_service, prefix_log
                            )
                            operations_count += updated_count

                    # Delete extra instances
                    if extra_instances:
                        async with get_db_session(self.async_session_maker) as _db_session:
                            instance_db_service = InstanceDbService(_db_session)
                            deleted_count = await self._delete_extra_instances(
                                extra_instances, instance_db_service, prefix_log
                            )
                            operations_count += deleted_count

                    # Commit changes if any operations were performed
                    if operations_count > 0:
                        await db_session.commit()
                        self.logger.info(
                            f"{prefix_log}, Reconciliation completed - {operations_count} total operations committed"
                        )
                    else:
                        self.logger.info(
                            f"{prefix_log}, No reconciliation operations needed - database is in sync"
                        )

                except Exception as e:
                    self.logger.error(
                        f"{prefix_log}, Error during database operations: {e}", exc_info=True
                    )
                    await db_session.rollback()
                    raise

        except Exception as e:
            self.logger.error(
                f"{prefix_log}, Error during instance reconciliation: {e}", exc_info=True
            )
