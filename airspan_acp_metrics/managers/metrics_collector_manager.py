import datetime
import logging

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from asyncssh import SSHClientConnection
from da_common.config import Config
from dal_pubsub.pubsub import PubSub

from airspan_acp_metrics.database.connection import get_async_session_maker
from airspan_acp_metrics.database.models.metrics_audits_db_model import MetricsAuditDbModel
from airspan_acp_metrics.database.services.metric_audit_db_service import MetricsAuditDbService
from airspan_acp_metrics.database.session_management import get_db_session
from airspan_acp_metrics.managers.database_operations_manager import DatabaseOperationsManager
from airspan_acp_metrics.managers.file_operations_manager import FileOperationsManager
from airspan_acp_metrics.managers.gcs_operations_manager import GCSOperationsManager
from airspan_acp_metrics.managers.ssh_connection_manager import SSHConnectionManager
from airspan_acp_metrics.publishing import publish_metrics
from airspan_acp_metrics.pyd_models.instance_pyd_model import InstancePydModel
from airspan_acp_metrics.pyd_models.metrics_audit_pyd_model import UpdateMetricsAuditPydModel
from airspan_acp_metrics.pyd_models.utils import MetricsCollectionStatus
from airspan_acp_metrics.utils import merge_and_format_intervals


class MetricsCollector:
    """
    Main orchestrator for metrics collection.
    Uses various manager classes to handle different aspects of the collection process.
    """

    def __init__(self, config: Config):
        self.config = config
        self.metrics_topic = self.config.data["pubsub"]["metrics"]["topic"]
        self.pub_sub = PubSub(self.config)
        self.async_session_maker = get_async_session_maker(self.config)
        self.scheduler: AsyncIOScheduler = AsyncIOScheduler()
        self._poll_count = 0

        # Initialize manager classes
        self.ssh_manager = SSHConnectionManager(config)
        self.file_ops = FileOperationsManager()
        self.gcs_ops = GCSOperationsManager(config)
        self.db_ops = DatabaseOperationsManager()

        self.logger = logging.getLogger("airspan_acp_metrics.managers.managers")

    def _get_log_prefix(
        self,
        instance_name: str | None = None,
        metric_name: str | None = None,
        interval_start: datetime.datetime | None = None,
        interval_end: datetime.datetime | None = None,
    ) -> str:
        """Generate standardized log prefix for tracking."""
        prefix_log = "MetricsCollector"
        if instance_name:
            prefix_log += f" {instance_name}"
        if metric_name:
            prefix_log += f" {metric_name}"
        if interval_start and interval_end:
            prefix_log += f" {merge_and_format_intervals([(interval_start, interval_end)])}"
        return prefix_log

    async def collect_instance_metrics_task(
        self,
        *,
        instance_pyd_model: InstancePydModel,
        connection: SSHClientConnection,
        metric_config: dict,
        metrics_audit_db_model: MetricsAuditDbModel,
        interval_start: datetime.datetime,
        interval_end: datetime.datetime,
        prefix_log: str = "",
    ):
        """Collect metrics for a single instance."""
        self.logger.info(f"{prefix_log}, started collecting metrics")
        now = datetime.datetime.now(datetime.UTC)

        async with get_db_session(self.async_session_maker) as db_session:
            audit_db_service = MetricsAuditDbService(db_session)

            metrics_audit_db_model = await db_session.merge(metrics_audit_db_model)
            await db_session.refresh(metrics_audit_db_model)

            try:
                # Phase 1: Download the file from the remote server
                (
                    file_data,
                    reason,
                    remote_file_metadata,
                ) = await self.file_ops.download_file_from_remote(
                    connection,
                    instance_pyd_model,
                    metric_config,
                    interval_start,
                    interval_end,
                    prefix_log,
                )

                if not file_data:
                    # Log file download failure to metrics audit only
                    update_audit_metrics_pyd_model = UpdateMetricsAuditPydModel(
                        metrics_id=metrics_audit_db_model.metrics_id,
                        collection_status=MetricsCollectionStatus.FAILED,
                        reason=reason,
                        updated_at=now,
                    )

                    await audit_db_service.update_audit(
                        audit_db_model=metrics_audit_db_model,
                        update_metrics_audit_pyd_model=update_audit_metrics_pyd_model,
                        prefix_log=prefix_log,
                    )
                    return

                try:
                    # Phase 2: Upload the file to GCS
                    file_metadata, gcs_file_path = await self.gcs_ops.upload_file_to_gcs(
                        file_data=file_data,
                        instance_pyd_model=instance_pyd_model,
                        metric_config=metric_config,
                        prefix_log=prefix_log,
                        retry_attempts=metrics_audit_db_model.attempt_count,
                        successful_attempts=1,
                        remote_file_metadata=remote_file_metadata,
                    )

                    if file_metadata is None or gcs_file_path is None:
                        self.logger.error(f"{prefix_log} : failed to upload file to GCS")
                        update_audit_metrics_pyd_model = UpdateMetricsAuditPydModel(
                            metrics_id=metrics_audit_db_model.metrics_id,
                            collection_status=MetricsCollectionStatus.FAILED,
                            reason="GCS upload failed",
                            updated_at=now,
                        )

                        # Log GCS upload failure to metrics audit only
                        await audit_db_service.update_audit(
                            audit_db_model=metrics_audit_db_model,
                            update_metrics_audit_pyd_model=update_audit_metrics_pyd_model,
                            prefix_log=prefix_log,
                        )
                        return

                    # Phase 3: Success - Create both metrics record with GCS path and metrics_audit entries
                    metrics_db_model = await self.db_ops.create_metrics_record(
                        db_session=db_session,
                        instance_pyd_model=instance_pyd_model,
                        metric_config=metric_config,
                        gcs_file_path=gcs_file_path,
                        interval_start=interval_start,
                        interval_end=interval_end,
                        prefix_log=prefix_log,
                    )

                    if metrics_db_model is None:
                        metrics_id = None
                        reason = "Failed to create metrics record"
                    else:
                        metrics_id = metrics_db_model.id
                        reason = "Metrics collection completed successfully"

                    update_audit_metrics_pyd_model = UpdateMetricsAuditPydModel(
                        metrics_id=metrics_id,
                        file_metadata=file_metadata,
                        collection_status=MetricsCollectionStatus.COLLECTED,
                        reason=reason,
                        updated_at=now,
                    )

                    # Log a successful collection to metrics audit
                    await audit_db_service.update_audit(
                        audit_db_model=metrics_audit_db_model,
                        update_metrics_audit_pyd_model=update_audit_metrics_pyd_model,
                        prefix_log=prefix_log,
                    )
                    await db_session.commit()
                    self.logger.info(
                        f"{prefix_log}, created metrics record: {metrics_id} and successfully updated metrics audit"
                    )

                    publish_metrics(
                        pub_sub=self.pub_sub,
                        topic=self.metrics_topic,
                        instance_pyd_model=instance_pyd_model,
                        interval_start=interval_start,
                        interval_end=interval_end,
                        gcs_file_path=gcs_file_path,
                        file_metadata=file_metadata,
                        prefix_log=prefix_log,
                    )
                    self.logger.info(f"{prefix_log}, metrics collection completed successfully")

                finally:
                    # Clean up file_data from memory to prevent memory leaks
                    if file_data:
                        try:
                            file_data.close()
                            self.logger.debug(f"{prefix_log}, Cleaned up file_data from memory")
                        except Exception as cleanup_error:
                            self.logger.warning(
                                f"{prefix_log}, Error cleaning up file_data: {cleanup_error}"
                            )
                        finally:
                            # Explicitly delete the reference to help garbage collection
                            del file_data

            except Exception as e:
                # Unexpected error - log to metrics audit
                self.logger.error(
                    f"{prefix_log}, unexpected error: {e}",
                    exc_info=True,
                )
                update_audit_metrics_pyd_model = UpdateMetricsAuditPydModel(
                    collection_status=MetricsCollectionStatus.FAILED,
                    reason=f"Unexpected error: {e!s}",
                    updated_at=now,
                )
                await audit_db_service.update_audit(
                    audit_db_model=metrics_audit_db_model,
                    update_metrics_audit_pyd_model=update_audit_metrics_pyd_model,
                    prefix_log=prefix_log,
                )
