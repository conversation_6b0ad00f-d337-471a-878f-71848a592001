import hashlib
import io
import logging
import os
import re
import zipfile

from da_common.config import Config
from google.cloud import storage as gcs

from airspan_acp_metrics.pyd_models.instance_pyd_model import InstancePydModel


class GCSOperationsManager:
    """Manages Google Cloud Storage operations for metrics collection."""

    def __init__(self, config: Config):
        self.config = config
        self.project_id = self.config.data.get("gcs", {}).get(
            "project"
        ) or self.config.data.get("google", {}).get("project")
        self.bucket_name = self.config.data.get("gcs", {}).get("bucketname", "acp-metrics")
        self.logger = logging.getLogger("airspan_acp_metrics.managers.gcs_operations_manager")
        self._bucket: gcs.Bucket | None = None
        self._storage_client: gcs.Client | None = None

    def _initialize_storage_client(self):
        """Initialize GCS client and bucket."""
        if self._storage_client is None:
            self._storage_client = gcs.Client(self.project_id)

        if self._bucket is None:
            self._bucket = self._initialize_bucket()

    def _initialize_bucket(self) -> gcs.Bucket:
        """Initialize GCS bucket with fallback naming strategy."""
        project_env_prefix = "-".join(self.project_id.split("-")[1:3])
        bucket_names_to_try = [
            self.bucket_name,
            f"{project_env_prefix}-{self.bucket_name}",
        ]

        for bucket_name in bucket_names_to_try:
            try:
                bucket = self._get_or_create_bucket(bucket_name)
                # Update the bucket_name if we used a fallback name
                if bucket_name != self.bucket_name:
                    self.bucket_name = bucket_name
                return bucket
            except Exception as e:
                self.logger.warning(f"GCS bucket: {bucket_name}, creation/check issue: {e}")

        # Final fallback - just get bucket reference without creation
        self.logger.warning(
            f"Unable to create or verify bucket existence. "
            f"Using bucket reference: {self.bucket_name}"
        )
        return self._storage_client.bucket(self.bucket_name)

    def _get_or_create_bucket(self, bucket_name: str) -> gcs.Bucket:
        """Get existing bucket or create new one if it doesn't exist."""
        bucket = self._storage_client.bucket(bucket_name)

        if not bucket.exists():
            bucket = self._storage_client.create_bucket(bucket_name)
            self.logger.info(f"GCS bucket: {bucket_name}, Created")
        else:
            self.logger.info(f"GCS bucket: {bucket_name} already exists")

        return bucket

    @staticmethod
    def _get_remote_file_timestamp(
        remote_file_path: str,
    ) -> str | None:
        """Get the timestamp of the remote file."""
        pattern = r"(\d{8}_\d{4}_\d{4}\.zip)"
        match = re.search(pattern, remote_file_path)

        if match:
            return match.group(1)  # Returns: YYYYMMDD_HHMM_HHMM.zip
        return None

    def _build_gcs_path(
        self,
        *,
        instance_pyd_model: InstancePydModel,
        metric_config: dict,
        remote_file_path: str,
    ) -> str:
        """Build the GCS path for the file."""
        metric_name = metric_config["metric_name"]
        remote_file_timestamp = self._get_remote_file_timestamp(remote_file_path)

        # Create the GCS path: {manager_instance}/{metric_name}/filename.zip
        return f"{instance_pyd_model.manager_instance}/{metric_name}/{remote_file_timestamp}"

    @staticmethod
    def _get_file_size(file_data: io.BytesIO) -> int:
        """Get file size from BytesIO object."""
        current_position = file_data.tell()
        file_data.seek(0, os.SEEK_END)
        file_size_bytes = file_data.tell()
        file_data.seek(current_position)  # Reset to original position
        return file_size_bytes

    @staticmethod
    def _get_zip_file_count(file_data: io.BytesIO) -> int:
        """Get the number of files in a zip archive.

        Args:
            file_data: BytesIO object containing zip file data

        Returns:
            Number of files in the zip archive

        Raises:
            zipfile.BadZipFile: If the file is not a valid zip file
        """
        current_position = file_data.tell()
        file_data.seek(0)  # Go to beginning of file

        try:
            with zipfile.ZipFile(file_data, "r") as zip_ref:
                # Count only files, not directories
                file_count = len(
                    [name for name in zip_ref.namelist() if not name.endswith("/")]
                )
        finally:
            file_data.seek(current_position)  # Reset to original position

        return file_count

    @staticmethod
    def _calculate_file_hash(file_data: io.BytesIO) -> str:
        """Calculate SHA256 hash of the file data.

        Args:
            file_data: BytesIO object containing file data

        Returns:
            SHA256 hash as hexadecimal string
        """
        current_position = file_data.tell()
        file_data.seek(0)  # Go to beginning of file

        try:
            sha256_hash = hashlib.sha256()
            # Read file in chunks to handle large files efficiently
            for chunk in iter(lambda: file_data.read(8192), b""):
                sha256_hash.update(chunk)
            return sha256_hash.hexdigest()
        finally:
            file_data.seek(current_position)  # Reset to original position

    async def upload_file_to_gcs(
        self,
        *,
        file_data: io.BytesIO,
        instance_pyd_model: InstancePydModel,
        metric_config: dict,
        retry_attempts: int,
        successful_attempts: int,
        remote_file_metadata: dict,
        prefix_log: str = "",
    ) -> tuple[dict | None, str | None]:
        """Upload a file to Google Cloud Storage.

        Args:
            file_data: File data as BytesIO object
            instance_pyd_model: Instance details
            metric_config: Metric configuration
            prefix_log: Prefix for log messages
            retry_attempts: Number of retry attempts for this collection
            successful_attempts: Number of successful attempts (should be 1 for successful collections)
            remote_file_metadata: Metadata about the file from the remote host

        Returns:
            tuple of (file_metadata, gcs_path) if successful, (None, None) otherwise
        """
        try:
            # Initialize storage client and bucket
            self._initialize_storage_client()

            remote_file_path = remote_file_metadata.get("remote_file_path")

            if not remote_file_path:
                raise ValueError(f"{prefix_log}, Remote file path not found")

            # Build GCS path
            gcs_path = self._build_gcs_path(
                instance_pyd_model=instance_pyd_model,
                metric_config=metric_config,
                remote_file_path=remote_file_path,
            )

            # Create blob and upload
            if self._bucket is None:
                raise RuntimeError(f"{prefix_log}, GCS bucket not initialized")

            blob = self._bucket.blob(gcs_path)
            blob.upload_from_file(file_data, rewind=True)

            self.logger.info(
                f"{prefix_log}, Successfully uploaded to GCS: gs://{self.bucket_name}/{gcs_path}"
            )

            # Get file metadata including hash and timestamp
            file_metadata = {
                "file_count": self._get_zip_file_count(file_data),
                "retry_attempts": retry_attempts,
                "successful_attempts": successful_attempts,
                "file_hash_sha256": self._calculate_file_hash(file_data),
            }

            # Add remote metadata if available
            if remote_file_metadata:
                # prioritize remote_file_metadata over file_metadata
                file_metadata = {**remote_file_metadata, **file_metadata}

            # append bucket_name to gcs_path
            gcs_path = self.bucket_name + "/" + gcs_path
            self.logger.info(
                f"{prefix_log}, File metadata: {file_metadata}, gcs_path: {gcs_path}"
            )
            return file_metadata, gcs_path

        except Exception as e:
            self.logger.error(
                f"{prefix_log}, Error uploading to GCS: {e}",
                exc_info=True,
            )
            return None, str(e)

    def get_bucket_name(self) -> str:
        """Get the bucket name."""
        return self.bucket_name

    def get_full_gcs_path(self, gcs_path: str) -> str:
        """Get the full GCS path including bucket name."""
        return f"gs://{self.bucket_name}/{gcs_path}"
