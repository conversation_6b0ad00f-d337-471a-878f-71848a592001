# Resolve the alarm
import datetime
import logging

from dal_pubsub.pubsub import PubSub
from metrics_collector.api_schema.models import EventData as AlarmEventData
from metrics_collector.api_schema.models import EventHeader as AlarmEventHeader
from metrics_collector.api_schema.models import MetricsEvent as AlarmEvent
from sqlalchemy.ext.asyncio import AsyncSession

from airspan_acp_metrics.database.models.alarm_db_model import AlarmDBModel
from airspan_acp_metrics.database.services.alarm_db_service import AlarmDbService
from airspan_acp_metrics.pyd_models.alarm_enums import AlarmType
from airspan_acp_metrics.pyd_models.alarm_pyd_model import AlarmCreatePydModel
from airspan_acp_metrics.pyd_models.alarm_pyd_model import AlarmResolvePydModel
from airspan_acp_metrics.pyd_models.alarm_pyd_model import AlarmResponsePydModel
from airspan_acp_metrics.pyd_models.instance_pyd_model import InstancePydModel


class AlarmManager:
    """Manager for handling alarm operations and pubsub publishing."""

    def __init__(self, db_session: AsyncSession, pubsub: PubSub, alarm_topic: str):
        self.db_session = db_session
        self.pubsub = pubsub
        self.alarm_topic = alarm_topic
        self.alarm_db_service = AlarmDbService(db_session)
        self.logger = logging.getLogger("airspan_acp_metrics.managers.alarm_manager")

    async def raise_alarm(
        self,
        alarm_type: AlarmType,
        name: str,
        source: str,
        instance: InstancePydModel,
        description: str | None = None,
        prefix_log: str = "",
    ) -> AlarmDBModel | None:
        """
        Raise an alarm for an instance.

        If an active alarm of the same type already exists for the instance,
        it will not create a duplicate.
        """
        try:
            # Check if an active alarm of this type already exists for this instance
            existing_alarm = await self.alarm_db_service.get_active_alarm_by_type_and_instance(
                alarm_type, instance.manager_instance
            )

            if existing_alarm:
                self.logger.info(
                    f"{prefix_log}, Active alarm of type {alarm_type} already exists for instance {instance.manager_instance}"
                )
                return existing_alarm

            # Create new alarm
            alarm_data = AlarmCreatePydModel(
                type=alarm_type,
                name=name,
                source=source,
                description=description,
                manager_instance=instance.manager_instance,
            )

            alarm = await self.alarm_db_service.create_alarm(alarm_data)

            # Convert to pydantic model for pubsub
            alarm_pyd_model = AlarmResponsePydModel.model_validate(alarm)

            # Generate and publish alarm event
            alarm_event = self._generate_alarm_event(instance, alarm_pyd_model)
            self._publish_alarm_event(alarm_event, prefix_log)

            self.logger.info(
                f"{prefix_log}, Successfully raised alarm {alarm.id} of type {alarm_type}"
            )
            return alarm

        except Exception as e:
            self.logger.error(f"{prefix_log}, Error raising alarm: {e}", exc_info=True)
            return None

    async def clear_alarm(
        self,
        alarm_type: AlarmType,
        instance: InstancePydModel,
        resolver: str,
        prefix_log: str = "",
    ) -> AlarmDBModel | None:
        """Clear an active alarm for an instance."""
        try:
            # Get active alarm of this type for this instance
            active_alarm = await self.alarm_db_service.get_active_alarm_by_type_and_instance(
                alarm_type, instance.manager_instance
            )

            if not active_alarm:
                self.logger.info(
                    f"{prefix_log}, No active alarm of type {alarm_type} found for instance {instance.manager_instance}"
                )
                return None

            resolve_data = AlarmResolvePydModel(
                resolver=resolver, resolved_at=datetime.datetime.now(datetime.UTC)
            )

            resolved_alarm = await self.alarm_db_service.resolve_alarm(
                active_alarm.id, resolve_data
            )

            if resolved_alarm:
                # Convert to pydantic model for pubsub
                alarm_pyd_model = AlarmResponsePydModel.model_validate(resolved_alarm)

                # Generate and publish alarm cleared event
                alarm_event = self._generate_alarm_event(instance, alarm_pyd_model)
                self._publish_alarm_event(alarm_event, prefix_log)

                self.logger.info(
                    f"{prefix_log}, Successfully cleared alarm {resolved_alarm.id} of type {alarm_type}"
                )

            return resolved_alarm

        except Exception as e:
            self.logger.error(f"{prefix_log}, Error clearing alarm: {e}", exc_info=True)
            return None

    @staticmethod
    def _generate_alarm_event(
        instance: InstancePydModel,
        alarm: AlarmResponsePydModel,
    ) -> AlarmEvent:
        """Generate appropriate alarm event from the alarm details."""
        cause = alarm.description or ""

        event_header = AlarmEventHeader(
            domain="nms",
            eventId=str(alarm.id),
            eventName=alarm.name,
            eventType="alarm",
            priority=alarm.priority,
            reportingEntityName="airspan-acp-metrics",
            sourceName=instance.manager_instance,
            sourceId=instance.manager_instance,
            eventTime=alarm.created_at,
            eventDuration=0,
            systemDN="airspan-acp-metrics",
        )

        event_data = AlarmEventData(
            objectId=instance.manager_instance,
            objectType="ACP",
            streetCellId="",
            uri=f"https://{instance.host_ip_address}",
            type=alarm.type,
            cause=cause,
            perceivedSeverity=alarm.severity,
            specificProblem=alarm.description or "",
            trendIndication=alarm.status,
            monitoredAttributes=[],
            proposedRepairActions=alarm.repairs or [],
            additionalText="",
            additionalInformation="",
        )

        return AlarmEvent(
            header=event_header,
            data=event_data,
        )

    def _publish_alarm_event(
        self,
        alarm_event: AlarmEvent,
        prefix_log: str,
    ) -> None:
        """Publish alarm event to pubsub."""
        try:
            self.pubsub.set_topic(self.alarm_topic)
            alarm_json = alarm_event.model_dump(mode="json")
            self.pubsub.push_payload(alarm_json)
            self.logger.info(f"{prefix_log}, Alarm published successfully: {alarm_json}")
        except Exception as e:
            self.logger.error(f"{prefix_log}, Error publishing alarm event: {e}", exc_info=True)
