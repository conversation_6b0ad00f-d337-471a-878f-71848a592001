import datetime
import logging

from sqlalchemy.ext.asyncio import AsyncSession

from airspan_acp_metrics.database.models.metrics_db_model import MetricsDbModel
from airspan_acp_metrics.database.services.instance_db_service import InstanceDbService
from airspan_acp_metrics.database.services.metrics_db_service import MetricsDbService
from airspan_acp_metrics.pyd_models.instance_pyd_model import InstancePydModel
from airspan_acp_metrics.pyd_models.metrics_pyd_model import MetricsPydModel


class DatabaseOperationsManager:
    """Manages database operations for metrics collection."""

    def __init__(self):
        self.logger = logging.getLogger(
            "airspan_acp_metrics.managers.database_operations_manager"
        )

    async def update_instance_metrics_collection_status(
        self,
        db_session: AsyncSession,
        manager_instance: str,
        status: str,
        error_reason: str | None = None,
        prefix_log: str = "",
    ):
        """Update the metrics collection status and error reason for an instance.

        Args:
            db_session: Database session
            manager_instance: Instance identifier
            status: New status (OK, ERROR, etc.)
            error_reason: Reason for error if status is ERROR
            prefix_log: Prefix for log messages
        """
        try:
            instance_db_service = InstanceDbService(db_session)
            await instance_db_service.update_metrics_collection_status(
                manager_instance, status, error_reason
            )

            status_msg = f"{prefix_log}, Updated metrics collection status to {status}"
            if error_reason:
                status_msg += f" with error: {error_reason}"
            self.logger.info(status_msg)

        except Exception as e:
            self.logger.error(
                f"{prefix_log}, Error updating metrics collection status: {e}",
                exc_info=True,
            )
            # Don't fail the main operation if status update fails

    async def create_metrics_record(
        self,
        *,
        db_session: AsyncSession,
        instance_pyd_model: InstancePydModel,
        metric_config: dict,
        gcs_file_path: str,
        interval_start: datetime.datetime,
        interval_end: datetime.datetime,
        prefix_log: str = "",
    ) -> MetricsDbModel | None:
        """Create a metrics record in the database.

        Args:
            db_session: Database session
            instance_pyd_model: Instance details
            metric_config: Metric configuration
            gcs_file_path: Path to the file in GCS
            interval_start: Start time for the metrics interval
            interval_end: End time for the metrics interval
            prefix_log: Prefix for log messages

        Returns:
            Created metrics database model if successful, None otherwise
        """
        try:
            metrics_service = MetricsDbService(db_session)
            metrics_pyd_model = MetricsPydModel(
                manager_instance=instance_pyd_model.manager_instance,
                metric_name=metric_config["metric_name"],
                gcs_file_path=gcs_file_path,
                interval_start=interval_start,
                interval_end=interval_end,
            )

            metrics_db_model = await metrics_service.create_metrics(
                metrics_pyd_model, prefix_log=prefix_log
            )
            return metrics_db_model

        except Exception as e:
            self.logger.error(
                f"{prefix_log}, Error creating metrics record: {e}",
                exc_info=True,
            )
            return None

    async def create_acp_instance(
        self, db_session: AsyncSession, instance_pyd_model: InstancePydModel
    ):
        """Create a new ACP instance in the database.

        Args:
            db_session: Database session
            instance_pyd_model: Instance details to create
        """
        try:
            instance_db_service = InstanceDbService(db_session)
            await instance_db_service.create_acp_instance(instance_pyd_model)
            self.logger.info(f"Created ACP instance: {instance_pyd_model.manager_instance}")
        except Exception as e:
            self.logger.error(
                f"Error creating ACP instance {instance_pyd_model.manager_instance}: {e}",
                exc_info=True,
            )
