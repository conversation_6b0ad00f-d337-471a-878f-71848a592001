# Test package
import datetime
import importlib.metadata
import sys
from pathlib import Path

# flake8: noqa
# For Python < 3.11, use tomli instead of tomllib
if sys.version_info >= (3, 11):
    import tomllib
else:
    try:
        import tomli as tomllib
    except ImportError:
        # If tomli is not installed, we'll fall back to other methods
        tomllib = None


def get_version():
    """Get the version of the package.

    Tries multiple methods in this order:
    1. From installed package metadata (only if version is not 0.0.0)
    2. From pyproject.toml
    3. Generate from current date
    """

    # Method 1: Try to get from installed package metadata
    try:
        version = importlib.metadata.version("airspan_acp_metrics")
        # Only use the metadata version if it's not 0.0.0
        if version != "0.0.0":
            return version
    except importlib.metadata.PackageNotFoundError:
        pass

    # Method 2: Try to get from pyproject.toml
    if tomllib is not None:
        try:
            # Find the pyproject.toml file (going up from current file)
            current_dir = Path(__file__).parent
            project_root = current_dir.parent
            pyproject_path = project_root / "pyproject.toml"

            if pyproject_path.exists():
                with open(pyproject_path, "rb") as f:
                    pyproject_data = tomllib.load(f)
                    if "project" in pyproject_data and "version" in pyproject_data["project"]:
                        return pyproject_data["project"]["version"]
        except Exception:
            pass

    # Method 3: Fall back to a date-based version
    return datetime.date.today().strftime("%Y.%m.%d") + "+local_repository"


# Set the version
__version__ = get_version()
