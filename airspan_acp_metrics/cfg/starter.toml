ENVIRONMENT = "local"

[collector]
url = "https://status.cloud.google.com/incidents.json"
interval = 12

[app]
# See https://pgjones.gitlab.io/hypercorn/how_to_guides/configuring.html
bind = "0.0.0.0:8080"

[google]
# Need the project id for GCP
project = "nms-dev-1-c7c0"
projectconfig = "nms-common-db"

[services.nms-orchestrator]
path = "nms/orc"

[services.inventory-manager]
path = "nms/inv"

[services.airspan-acp-agent]
path = "api/acp"

[db]
host = "localhost"
port = 5432
user = "postgres"
database = "airspan_acp_agent"
password = "postgres"

[pubsub.metrics]
topic = "nms-metrics"

[pubsub.alarms]
topic = "nms-alarms"

[gcs]
project = ""
bucketname = "dev-1-acp-metrics"

[metrics.Stats]
enabled = true
metric_name = "Stats"
frequency = 5         # minutes
# Backfill configuration for this metric type
backfill_enabled = true
max_days_lookback = 30 # Maximum days to look back for missing metrics (should match max_days_lookback)
recent_buffer_minutes = 1  # Don't backfill metrics from the last N minutes (handled by MetricsCollector)
max_retry_attempts = 3      # Maximum number of retry attempts for failed backfills
retry_delay_minutes = 30    # Minimum delay between retry attempts

[security]
domain = "https://ids-dev.denseware.net"

[logging]
version = 1

[logging.formatters.default]
format = "{asctime}| {levelname:>8}| {name:<24} | {message}"
datefmt = ""
style = "{"
validate = true

[logging.handlers.terminal]
class = "logging.StreamHandler"
formatter = "default"
stream = "ext://sys.stdout"
level = "INFO"

[logging.handlers.audit]
class = "logging.FileHandler"
formatter = "default"
filename = "audit.log"
level = "DEBUG"

[logging.loggers]
root = { level = "INFO", handlers = ["terminal"] }
airspan_acp_metrics = { level = "DEBUG", handlers = [
    "audit",
    "terminal",
], propagate = false }
util = { level = "INFO", propagate = true }


[sentry]
# Set this to your Sentry DSN to enable error tracking
dsn = "https://<EMAIL>/4509640628109312"
# Error sampling rate (0.0 to 1.0)
sample_rate = 0.1
# Traces sampling rate for performance monitoring (0.0 to 1.0)
traces_sample_rate = 0.1
