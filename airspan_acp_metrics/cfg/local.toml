ENVIRONMENT = "local"

[services.nms-orchestrator]
path = "nms/orc"

[services.inventory-manager]
path = "nms/inv"

[services.airspan-acp-agent]
path = "api/acp"

[db]
host = "localhost"
port = 5432
user = "postgres"
database = "test_airspan_acp_metrics"
password = "postgres"

[google]
project = "nms-dev-1-c7c0"
projectconfig = "nms-default-db"

[app]
bind = "localhost:8080"

[pubsub.metrics]
topic = "nms-metrics"

[pubsub.alarms]
topic = "nms-alarms"

[gcs]
project = ""
bucketname = "acp-metrics"

[metrics.Stats]
enabled = true
metric_name = "Stats"
frequency = 5         # minutes
# Backfill configuration for this metric type
backfill_enabled = true
max_days_lookback = 1      # Maximum days to look back for missing metrics (should match max_days_lookback)
max_retry_attempts = 3      # Maximum number of retry attempts for failed backfills


[logging]
version = 1

[logging.formatters.default]
format = "{asctime}| {levelname:>8}| {name:<24} | {message}"
datefmt = ""
style = "{"
validate = true

[logging.handlers.terminal]
class = "logging.StreamHandler"
formatter = "default"
stream = "ext://sys.stdout"
level = "INFO"

[logging.handlers.audit]
class = "logging.FileHandler"
formatter = "default"
filename = "audit.log"
level = "DEBUG"

[logging.loggers]
root = { level = "INFO", handlers = ["terminal"] }
airspan_acp_metrics = { level = "DEBUG", handlers = [
    "audit",
    "terminal",
], propagate = false }
util = { level = "INFO", propagate = true }


[security.apikeys.nms]
key = "test_key"
roles = "SystemAdmin,NmsAdministrator,NmsDev,NmsOperator"
