#!/usr/bin/env python

"""
This module implements an ASGI server.

Use it as a starting point for long-running services and REST API clients.
Provides an example of how to schedule work for asynchronous processing.

https://status.cloud.google.com/incidents.json

"""

import argparse
import asyncio
import importlib.resources
import json
import logging
import pathlib
import sys

import hypercorn
from da_common.config import Config
from hypercorn.asyncio import serve

import airspan_acp_metrics
from airspan_acp_metrics.app import create_app


def main(args):
    logger = logging.getLogger("airspan_acp_metrics.main")
    logger.info(
        f"Airspan Acp Metrics Collection application version {airspan_acp_metrics.__version__}"
    )

    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    config = loop.run_until_complete(Config.get_config(args.config))

    app = create_app(config)

    if args.api:
        schema = airspan_acp_metrics.schema.build_schema(app)
        logger.info(json.dumps(schema, indent=4))
        return 0

    # Run the application
    settings = hypercorn.Config.from_mapping(config.data.get("app", {}))
    loop.run_until_complete(serve(app, settings))

    return 0


def parser(default_config=None):
    rv = argparse.ArgumentParser(usage=__doc__)
    rv.add_argument(
        "--api",
        action="store_true",
        default=False,
        help="Generate an OpenAPI document for the service.",
    )
    rv.add_argument(
        "--config",
        required=False,
        type=pathlib.Path,
        default=default_config,
        help="Set the path to a configuration file (TOML).",
    )
    rv.add_argument(
        "--no_migrate",
        action="store_true",
        required=False,
        default=False,
        help="Do not automatically migrate to the latest DB revision.",
    )
    return rv


def run():
    cnf_file = importlib.resources.files("airspan_acp_metrics.cfg").joinpath("starter.toml")
    p = parser(cnf_file)
    args = p.parse_args()
    rv = main(args)
    sys.exit(rv)


if __name__ == "__main__":
    run()
