# Configuration file for the Sphinx documentation builder.
#
# For the full list of built-in configuration values, see the documentation:
# https://www.sphinx-doc.org/en/master/usage/configuration.html

import pathlib
import pkgutil
import sys


try:
    import airspan_acp_metrics  # noqa: F401
except ImportError:
    sys.path.append(str(pathlib.Path(__file__).parent.parent.parent))

# -- Project information -----------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#project-information

project = "airspan-acp-metrics"
copyright = "2025, Dense Air"  # flake8: noqa
author = "<PERSON><PERSON><PERSON>"
release = pkgutil.resolve_name("airspan_acp_metrics.__version__")

# -- General configuration ---------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#general-configuration

extensions = ["sphinx.ext.autodoc"]

templates_path = ["_templates"]
exclude_patterns = ["_build", "Thumbs.db", ".DS_Store"]


# -- Options for HTML output -------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#options-for-html-output

# https://sphinx-themes.org/sample-sites/sphinx-sizzle-theme/
html_theme = "sizzle"
html_static_path = ["_static"]
html_title = f"{project} {release}"

# -- Options for autodoc ----------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/extensions/autodoc.html#configuration

# Automatically extract typehints when specified and place them in
# descriptions of the relevant function/method.
autodoc_typehints = "description"

# Don't show class signature with the class' name.
autodoc_class_signature = "separated"
