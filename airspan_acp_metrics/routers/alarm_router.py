import logging
from typing import Annotated
from uuid import UUID

from da_common.security import get_current_user
from fastapi import APIRouter
from fastapi import Depends
from fastapi import HTTPException
from fastapi import Security
from sqlalchemy.ext.asyncio import AsyncSession

from airspan_acp_metrics.database.services.alarm_db_service import AlarmDbService
from airspan_acp_metrics.database.session_management import get_db_session
from airspan_acp_metrics.pyd_models.alarm_pyd_model import AlarmAcknowledgePydModel
from airspan_acp_metrics.pyd_models.alarm_pyd_model import AlarmFilterPydModel
from airspan_acp_metrics.pyd_models.alarm_pyd_model import AlarmResolvePydModel
from airspan_acp_metrics.pyd_models.alarm_pyd_model import AlarmResponsePydModel
from airspan_acp_metrics.routers.dependency import get_app_async_session_maker


alarms_router = APIRouter(
    prefix="/nms/api", tags=["alarms"], dependencies=[Security(get_current_user)]
)


@alarms_router.get(
    "/alarms",
    response_model=list[AlarmResponsePydModel],
    summary="Get alarms with optional filters",
    description="Retrieve alarms with optional filtering by type, severity, priority, status, instance, and source.",
)
async def get_alarms(
    alarm_filter: Annotated[AlarmFilterPydModel, Depends()],
    async_session_maker: Annotated[AsyncSession, Depends(get_app_async_session_maker)] = None,
) -> list[AlarmResponsePydModel]:
    """Get alarms with optional filters."""
    logger = logging.getLogger("airspan_acp_metrics.routers.alarm_router.get_alarms")

    async with get_db_session(async_session_maker) as db_session:
        try:
            alarm_service = AlarmDbService(db_session)
            alarms = await alarm_service.get_alarms_by_filter(alarm_filter)

            return [AlarmResponsePydModel.model_validate(alarm) for alarm in alarms]

        except Exception as e:
            logger.error(f"Error getting alarms: {e}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Internal server error while getting alarms: {e!s}",
            ) from e


@alarms_router.get(
    "/alarms/{alarm_id}",
    response_model=AlarmResponsePydModel,
    summary="Get alarm by ID",
    description="Retrieve a specific alarm by its ID.",
)
async def get_alarm_by_id(
    alarm_id: UUID,
    async_session_maker: Annotated[AsyncSession, Depends(get_app_async_session_maker)] = None,
) -> AlarmResponsePydModel:
    """Get alarm by ID."""
    logger = logging.getLogger("airspan_acp_metrics.routers.alarm_router.get_alarm_by_id")

    async with get_db_session(async_session_maker) as db_session:
        try:
            alarm_service = AlarmDbService(db_session)
            alarm = await alarm_service.get_alarm_by_id(alarm_id)

            if not alarm:
                raise HTTPException(status_code=404, detail="Alarm not found")

            return AlarmResponsePydModel.model_validate(alarm)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting alarm by ID {alarm_id}: {e}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Internal server error while getting alarm: {e!s}",
            ) from e


@alarms_router.put(
    "/alarms/{alarm_id}/acknowledge",
    response_model=AlarmResponsePydModel,
    summary="Acknowledge an alarm",
    description="Mark an alarm as acknowledged.",
)
async def acknowledge_alarm(
    alarm_id: UUID,
    acknowledge_data: AlarmAcknowledgePydModel,
    async_session_maker: Annotated[AsyncSession, Depends(get_app_async_session_maker)] = None,
) -> AlarmResponsePydModel:
    """Acknowledge an alarm."""
    logger = logging.getLogger("airspan_acp_metrics.routers.alarm_router.acknowledge_alarm")

    async with get_db_session(async_session_maker) as db_session:
        try:
            alarm_service = AlarmDbService(db_session)
            alarm = await alarm_service.acknowledge_alarm(alarm_id, acknowledge_data)

            if not alarm:
                raise HTTPException(status_code=404, detail="Alarm not found")

            return AlarmResponsePydModel.model_validate(alarm)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error acknowledging alarm {alarm_id}: {e}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Internal server error while acknowledging alarm: {e!s}",
            ) from e


@alarms_router.put(
    "/alarms/{alarm_id}/resolve",
    response_model=AlarmResponsePydModel,
    summary="Resolve an alarm",
    description="Mark an alarm as resolved.",
)
async def resolve_alarm(
    alarm_id: UUID,
    resolve_data: AlarmResolvePydModel,
    async_session_maker: Annotated[AsyncSession, Depends(get_app_async_session_maker)] = None,
) -> AlarmResponsePydModel:
    """Resolve an alarm."""
    logger = logging.getLogger("airspan_acp_metrics.routers.alarm_router.resolve_alarm")

    async with get_db_session(async_session_maker) as db_session:
        try:
            alarm_service = AlarmDbService(db_session)
            alarm = await alarm_service.resolve_alarm(alarm_id, resolve_data)

            if not alarm:
                raise HTTPException(status_code=404, detail="Alarm not found")

            return AlarmResponsePydModel.model_validate(alarm)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error resolving alarm {alarm_id}: {e}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Internal server error while resolving alarm: {e!s}",
            ) from e
