import datetime
import logging
from typing import Annotated

from da_common.security import get_current_user
from fastapi import APIRouter
from fastapi import Depends
from fastapi import HTTPException
from fastapi import Security
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession

from airspan_acp_metrics.database.services.metric_audit_db_service import MetricsAuditDbService
from airspan_acp_metrics.database.session_management import get_db_session
from airspan_acp_metrics.pyd_models.metrics_audit_pyd_model import DeleteMetricsAuditRequest
from airspan_acp_metrics.routers.dependency import get_app_async_session_maker


router = APIRouter(
    prefix="/nms/api/metrics-audit",
    tags=["metrics-audit"],
    dependencies=[Security(get_current_user)],
)


@router.post(
    "/republish",
    summary="Republish metrics audit records by time range",
    description="""
    Republish metrics audit records and their associated metrics within a specified time range.
    This allows those intervals to be picked up again in the next collection cycle.
    """,
)
async def republish_metrics_audit_by_time_range(
    request: DeleteMetricsAuditRequest,
    async_session_maker: Annotated[AsyncSession, Depends(get_app_async_session_maker)],
) -> JSONResponse:
    """Republish metrics audit records and associated metrics by time range."""
    logger = logging.getLogger(
        "airspan_acp_metrics.routers.metrics_audit_router.republish_metrics_audit_by_time_range"
    )

    logger.info(
        f"Republish request received for time range: {request.start_time} to {request.end_time}"
    )

    if request.start_time < datetime.datetime.now(datetime.UTC) - datetime.timedelta(days=30):
        raise HTTPException(
            status_code=400,
            detail="Cannot republish metrics audit records older than 30 days",
        )

    async with get_db_session(async_session_maker) as db_session:
        try:
            audit_service = MetricsAuditDbService(db_session)

            (
                deleted_audits,
                deleted_metrics,
            ) = await audit_service.republish_metrics(
                manager_instance=request.manager_instance,
                metric_name=request.metric_name,
                start_time=request.start_time,
                end_time=request.end_time,
            )

            # Commit the transaction
            await db_session.commit()

            if len(deleted_audits) == 0 and len(deleted_metrics) == 0:
                message = f"No audit records or metrics records found in the time range {request.start_time.isoformat()} to {request.end_time.isoformat()}"
                logger.info(message)
                return JSONResponse(content={"message": message})

            message = (
                f"Successfully deleted {len(deleted_audits)} audit records and "
                f"{len(deleted_metrics)} metrics records from {request.start_time.isoformat()} to {request.end_time.isoformat()}, "
                "the metrics will be republished in the next collection cycle"
            )
            logger.info(message)
            return JSONResponse(content={"message": message})

        except Exception as e:
            logger.error(f"Error republishing metrics audit records: {e}", exc_info=True)
            await db_session.rollback()
            raise HTTPException(
                status_code=500,
                detail=f"Internal server error while republishing metrics audit records: {e!s}",
            ) from e
