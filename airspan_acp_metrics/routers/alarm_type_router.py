import logging
from typing import Annotated

from da_common.security import get_current_user
from fastapi import APIRouter
from fastapi import HTT<PERSON>Exception
from fastapi import Security
from fastapi.params import Depends
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio.session import AsyncSession

from airspan_acp_metrics.database.services.alarm_type_db_service import AlarmTypeDbService
from airspan_acp_metrics.database.session_management import get_db_session
from airspan_acp_metrics.pyd_models import AlarmType
from airspan_acp_metrics.pyd_models import AlarmTypePydModel
from airspan_acp_metrics.pyd_models import AlarmTypeUpdatePydModel
from airspan_acp_metrics.routers.dependency import get_app_async_session_maker


alarm_types_router = APIRouter(
    prefix="/nms/api", tags=["alarm_types"], dependencies=[Security(get_current_user)]
)


# Alarm Type Management Endpoints
@alarm_types_router.get(
    "/alarm_types",
    response_model=list[AlarmTypePydModel],
    summary="Get all alarm types",
    description="Retrieve all available alarm types.",
)
async def get_alarm_types(
    async_session_maker: Annotated[AsyncSession, Depends(get_app_async_session_maker)],
) -> list[AlarmTypePydModel]:
    """Get all alarm types."""
    logger = logging.getLogger("airspan_acp_metrics.routers.alarm_router.get_alarm_types")

    async with get_db_session(async_session_maker) as db_session:
        try:
            alarm_type_service = AlarmTypeDbService(db_session)
            alarm_types = await alarm_type_service.get_all_alarm_types()

            return [AlarmTypePydModel.model_validate(alarm_type) for alarm_type in alarm_types]

        except Exception as e:
            logger.error(f"Error getting alarm types: {e}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Internal server error while getting alarm types: {e!s}",
            ) from e


@alarm_types_router.get(
    "/alarm_types/{alarm_type}",
    response_model=AlarmTypePydModel,
    summary="Get alarm type by type",
    description="Retrieve a specific alarm type by its type.",
)
async def get_alarm_type(
    alarm_type: AlarmType,
    async_session_maker: Annotated[AsyncSession, Depends(get_app_async_session_maker)] = None,
) -> AlarmTypePydModel:
    """Get alarm type by type."""
    logger = logging.getLogger("airspan_acp_metrics.routers.alarm_router.get_alarm_type")

    async with get_db_session(async_session_maker) as db_session:
        try:
            alarm_type_service = AlarmTypeDbService(db_session)
            alarm_type_record = await alarm_type_service.get_alarm_type_by_type(alarm_type)

            if not alarm_type_record:
                raise HTTPException(status_code=404, detail="Alarm type not found")

            return AlarmTypePydModel.model_validate(alarm_type_record)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting alarm type {alarm_type}: {e}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Internal server error while getting alarm type: {e!s}",
            ) from e


@alarm_types_router.put(
    "/alarm_types/{alarm_type}",
    response_model=AlarmTypePydModel,
    summary="Update alarm type",
    description="Update an existing alarm type's severity, priority, and repair actions.",
)
async def update_alarm_type(
    alarm_type: AlarmType,
    update_data: AlarmTypeUpdatePydModel,
    async_session_maker: Annotated[AsyncSession, Depends(get_app_async_session_maker)] = None,
) -> AlarmTypePydModel:
    """Update an alarm type."""
    logger = logging.getLogger("airspan_acp_metrics.routers.alarm_router.update_alarm_type")

    async with get_db_session(async_session_maker) as db_session:
        try:
            alarm_type_service = AlarmTypeDbService(db_session)
            updated_alarm_type = await alarm_type_service.update_alarm_type(
                alarm_type, update_data
            )

            if not updated_alarm_type:
                raise HTTPException(status_code=404, detail="Alarm type not found")

            return AlarmTypePydModel.model_validate(updated_alarm_type)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating alarm type {alarm_type}: {e}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Internal server error while updating alarm type: {e!s}",
            ) from e


@alarm_types_router.delete(
    "/alarm_types/{alarm_type}",
    summary="Delete alarm type",
    description="Delete an alarm type if no existing alarms reference it.",
)
async def delete_alarm_type(
    alarm_type: AlarmType,
    async_session_maker: Annotated[AsyncSession, Depends(get_app_async_session_maker)] = None,
) -> JSONResponse:
    """Delete an alarm type."""
    logger = logging.getLogger("airspan_acp_metrics.routers.alarm_router.delete_alarm_type")

    async with get_db_session(async_session_maker) as db_session:
        try:
            alarm_type_service = AlarmTypeDbService(db_session)
            deleted = await alarm_type_service.delete_alarm_type(alarm_type)

            if not deleted:
                usage_count = await alarm_type_service.get_alarm_type_usage_count(alarm_type)
                if usage_count > 0:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Cannot delete alarm type {alarm_type}: {usage_count} alarms still reference it",
                    )
                else:
                    raise HTTPException(status_code=404, detail="Alarm type not found")

            message = f"Successfully deleted alarm type {alarm_type}"
            logger.info(message)
            return JSONResponse(content={"message": message})

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting alarm type {alarm_type}: {e}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Internal server error while deleting alarm type: {e!s}",
            ) from e
