from typing import Annotated

from da_common.config import Config
from dal_pubsub.pubsub import PubSub
from fastapi import Depends
from fastapi import FastAP<PERSON>
from fastapi import Request
from sqlalchemy.ext.asyncio import AsyncSession


async def get_app(request: Request) -> FastAPI:
    return request.app


async def get_app_async_session_maker(
    app: Annotated[FastAPI, Depends(get_app)],
) -> AsyncSession:
    return app.state.async_session_maker


async def get_app_config(
    app: Annotated[FastAPI, Depends(get_app)],
) -> Config:
    return app.state.config


async def get_app_pubsub(
    app: Annotated[FastAPI, Depends(get_app)],
) -> PubSub:
    return app.state.pub_sub
