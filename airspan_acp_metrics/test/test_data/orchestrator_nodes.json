{"GB-DEV2-VM-0002": {"id": "GB-DEV2-VM-0002", "server": {"server_id": "GP081D3-dauk-mrl-green-acp", "name": "dauk-mrl-green-acp", "ip_address": "*************", "node_type": "Server-VM", "timing_type": "NTP", "cluster_name": null, "access_method": "ssh", "last_attempted": "2025-06-12T17:43:10.156903Z", "last_contacted": "2025-06-12T17:43:10.156903Z", "subsystems": [], "status": "OK", "reason": "Server in good health - All monitored subsystems within normal parameters", "cause": "Healthy", "repairs": [], "version": "Red Hat Enterprise Linux 8.10 (Ootpa)"}, "druid": null, "vsr": null, "acp": {"manager_instance": "acp-dauk-mrl-green-acp", "url": "https://*************/api/20.5", "health_status_logs": {}, "acp_platform_agent_available": false, "id": "220b0b3e-a085-4428-b5c7-146476cf061a", "health_status": "ERROR", "reason": "open alarms from NmsServer Found", "created_at": "2025-04-27T17:58:19.601647Z", "updated_at": "2025-06-12T17:43:29.605828Z", "last_attempted_at": "2025-06-12T17:43:29.605828Z", "last_contacted_at": "2025-06-12T17:43:29.605828Z"}, "cm": null}, "ha-secondary-from-golden-ha-acp-snapshot-of-ha-primary": {"utc_stamp": "2025-06-12T09:48:32.142080Z", "origin": "nms-orchestrator", "message": "Invalid reply from inventory-manager - Client error '404 ' for url 'http://inventory-manager:8080/nms/inv/nodes/ha-secondary-from-golden-ha-acp-snapshot-of-ha-primary/components'\nFor more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/404", "status": "FAILED", "object": null}}