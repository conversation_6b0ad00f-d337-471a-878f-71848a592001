import importlib
import importlib.resources
from pathlib import Path
from unittest.mock import AsyncMock

import pytest_asyncio
from da_common.config import Config
from da_common.models import Status
from httpx import ASGITransport
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.ext.asyncio import async_sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy.sql import text

from airspan_acp_metrics.app import create_app
from airspan_acp_metrics.database.connection import Base as base
from airspan_acp_metrics.database.connection import create_database
from airspan_acp_metrics.database.connection import get_db_url
from airspan_acp_metrics.database.session_management import get_db_session
from airspan_acp_metrics.pyd_models.instance_pyd_model import InstancePydModel
from airspan_acp_metrics.test.factories.instance_db_factory import InstanceDbFactory


@pytest_asyncio.fixture(scope="function", autouse=True)
def reset_httpx_mock(httpx_mock):
    """
    Automatically reset httpx_mock before each test to ensure no residual responses affect test outcomes.
    """
    httpx_mock.reset()
    yield
    httpx_mock.reset()


@pytest_asyncio.fixture(scope="function")
async def test_config():
    config_file = importlib.resources.files("airspan_acp_metrics.cfg").joinpath("local.toml")
    config = await Config.get_config(path=Path(str(config_file)))
    return config


@pytest_asyncio.fixture(scope="function")
async def test_async_engine(test_config):
    async_db_url = get_db_url(test_config)
    if test_config.data["db"]["database"] != "test_airspan_acp_metrics":
        raise ValueError(
            "Test database name must be 'test_airspan_acp_metrics' to avoid accidental data loss."
        )
    await create_database(test_config)
    engine = create_async_engine(
        async_db_url,
        connect_args={"server_settings": {"search_path": "metrics"}},
    )
    async with engine.connect() as conn:
        trans = await conn.begin()
        try:
            yield engine
        except Exception as e:
            await trans.rollback()
            raise e
        else:
            await trans.commit()
    await engine.dispose()


@pytest_asyncio.fixture(scope="function", autouse=True)
async def db_cleanup(test_async_engine):
    async with test_async_engine.begin() as conn:
        await conn.execute(text("SET search_path TO metrics"))
        await conn.run_sync(base.metadata.drop_all)
        # Create the metrics schema if it doesn't exist
        await conn.execute(text("CREATE SCHEMA IF NOT EXISTS metrics"))
        # Set the search path to the metrics schema
        await conn.execute(text("SET search_path TO metrics"))
        await conn.run_sync(base.metadata.create_all)

    yield

    async with test_async_engine.begin() as conn:
        # Set the search path to the metrics schema for cleanup
        await conn.execute(text("SET search_path TO metrics"))
        await conn.run_sync(base.metadata.drop_all)


@pytest_asyncio.fixture(scope="function")
async def test_async_session_maker(test_async_engine):
    async_session_ = async_sessionmaker(
        test_async_engine, class_=AsyncSession, expire_on_commit=True
    )
    yield async_session_


@pytest_asyncio.fixture(scope="function")
async def test_async_session(test_async_session_maker):
    async with get_db_session(test_async_session_maker) as async_session:
        yield async_session


@pytest_asyncio.fixture(scope="function")
async def test_app(test_async_session_maker, test_config):
    app = create_app(test_config)
    app.state.async_session_maker = test_async_session_maker
    # Initialize PubSub and other components
    app.state.pub_sub = AsyncMock()
    app.state.metrics_topic = test_config.data["pubsub"]["metrics"]["topic"]
    app.state.alarm_topic = (
        test_config.data["pubsub"].get("alarm", {}).get("topic", "test-alarm-topic")
    )
    yield app


@pytest_asyncio.fixture(scope="function")
async def test_async_client(test_async_session_maker, test_config, test_app):
    # TODO: figure it out how to dynamically set the base_url
    transport = ASGITransport(app=test_app, client=("127.0.0.1", 8080))
    headers = {"x-api-key": test_config.data["security"]["apikeys"]["nms"]["key"]}
    async with AsyncClient(
        transport=transport, base_url="http://localhost:8080/", headers=headers
    ) as ac:
        yield ac


def get_test_credentials():
    return {"username": "test-user", "password": "test-password"}


@pytest_asyncio.fixture(scope="function")
async def sample_instance_db_model(test_async_session):
    """Create a sample instance database model."""
    instance_db_model = await InstanceDbFactory(
        async_session=test_async_session,
        manager_instance="test-instance-1",
        url="http://test-url",
        host_ip_address="*************",
        host_secret_id="test-secret-id",
        metrics_collection_status=Status.OK,
    )
    await test_async_session.commit()
    await test_async_session.refresh(instance_db_model)
    return instance_db_model


@pytest_asyncio.fixture(scope="function")
async def sample_instance_pyd_model(sample_instance_db_model):
    """Create a sample instance Pydantic model."""
    return InstancePydModel.model_validate(sample_instance_db_model)
