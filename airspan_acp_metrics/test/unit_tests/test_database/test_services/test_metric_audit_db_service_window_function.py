"""
Unit tests specifically for the window function logic in MetricsAuditDbService.get_instance_audit_metrics_for_processing.
Tests edge cases and ensures only the highest attempt_count audit per interval is returned.
"""

import datetime
import sys
from unittest.mock import MagicMock

import pytest

from airspan_acp_metrics.database.services.metric_audit_db_service import MetricsAuditDbService
from airspan_acp_metrics.pyd_models.utils import MetricsCollectionStatus
from airspan_acp_metrics.test.factories.metrics_audit_db_factory import MetricsAuditDbFactory


# Mock dal_pubsub before any other imports
sys.modules["dal_pubsub"] = MagicMock()
sys.modules["dal_pubsub.pubsub"] = MagicMock()


class TestMetricsAuditDbServiceWindowFunction:
    """Test the window function logic in get_instance_audit_metrics_for_processing."""

    @pytest.mark.asyncio
    async def test_window_function_returns_highest_attempt_count_per_interval(
        self, test_async_session
    ):
        """Test that window function returns only the audit with highest attempt_count per interval."""
        audit_service = MetricsAuditDbService(test_async_session)

        # Create multiple intervals with multiple attempts each
        base_time = datetime.datetime(2025, 7, 10, 10, 0, 0, tzinfo=datetime.UTC)

        # Interval 1: 10:00-10:05 with attempts 1, 2, 3
        interval_1_start = base_time
        interval_1_end = base_time + datetime.timedelta(minutes=5)

        await MetricsAuditDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metric_name="Stats",
            interval_start=interval_1_start,
            interval_end=interval_1_end,
            collection_status=MetricsCollectionStatus.FAILED,
            attempt_count=1,
        )

        await MetricsAuditDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metric_name="Stats",
            interval_start=interval_1_start,
            interval_end=interval_1_end,
            collection_status=MetricsCollectionStatus.FAILED,
            attempt_count=2,
        )

        audit_1_attempt_3 = await MetricsAuditDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metric_name="Stats",
            interval_start=interval_1_start,
            interval_end=interval_1_end,
            collection_status=MetricsCollectionStatus.NOT_STARTED,
            attempt_count=3,
        )

        # Interval 2: 10:05-10:10 with attempts 1, 2
        interval_2_start = base_time + datetime.timedelta(minutes=5)
        interval_2_end = base_time + datetime.timedelta(minutes=10)

        await MetricsAuditDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metric_name="Stats",
            interval_start=interval_2_start,
            interval_end=interval_2_end,
            collection_status=MetricsCollectionStatus.FAILED,
            attempt_count=1,
        )

        audit_2_attempt_2 = await MetricsAuditDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metric_name="Stats",
            interval_start=interval_2_start,
            interval_end=interval_2_end,
            collection_status=MetricsCollectionStatus.NOT_STARTED,
            attempt_count=2,
        )

        await test_async_session.commit()

        # Call the window function method
        audits_for_processing = await audit_service.get_instance_audit_metrics_for_processing(
            instance_name="test-instance", limit=10, max_retry_attempts=3
        )

        # Should return exactly 2 audits (one per interval)
        assert len(audits_for_processing) == 2

        # Sort by interval_start for consistent testing
        audits_for_processing.sort(key=lambda x: x.interval_start)

        # First audit should be from interval 1 with attempt_count=3
        assert audits_for_processing[0].id == audit_1_attempt_3.id
        assert audits_for_processing[0].attempt_count == 3
        assert audits_for_processing[0].interval_start == interval_1_start
        assert audits_for_processing[0].interval_end == interval_1_end

        # Second audit should be from interval 2 with attempt_count=2
        assert audits_for_processing[1].id == audit_2_attempt_2.id
        assert audits_for_processing[1].attempt_count == 2
        assert audits_for_processing[1].interval_start == interval_2_start
        assert audits_for_processing[1].interval_end == interval_2_end

    @pytest.mark.asyncio
    async def test_window_function_excludes_collected_status(self, test_async_session):
        """Test that COLLECTED audits are excluded even if they have highest attempt_count."""
        audit_service = MetricsAuditDbService(test_async_session)

        base_time = datetime.datetime(2025, 7, 10, 10, 0, 0, tzinfo=datetime.UTC)
        interval_start = base_time
        interval_end = base_time + datetime.timedelta(minutes=5)

        # Create audit with attempt_count=1, FAILED status
        await MetricsAuditDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metric_name="Stats",
            interval_start=interval_start,
            interval_end=interval_end,
            collection_status=MetricsCollectionStatus.FAILED,
            attempt_count=1,
        )

        # Create audit with attempt_count=2, COLLECTED status (should be excluded)
        await MetricsAuditDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metric_name="Stats",
            interval_start=interval_start,
            interval_end=interval_end,
            collection_status=MetricsCollectionStatus.COLLECTED,
            attempt_count=2,
        )

        await test_async_session.commit()

        # Call the window function method
        audits_for_processing = await audit_service.get_instance_audit_metrics_for_processing(
            instance_name="test-instance", limit=10, max_retry_attempts=3
        )

        # Should return NO audits because highest attempt_count is COLLECTED
        assert len(audits_for_processing) == 0

    @pytest.mark.asyncio
    async def test_window_function_excludes_running_status(self, test_async_session):
        """Test that RUNNING audits are excluded to prevent race conditions."""
        audit_service = MetricsAuditDbService(test_async_session)

        base_time = datetime.datetime(2025, 7, 10, 10, 0, 0, tzinfo=datetime.UTC)
        interval_start = base_time
        interval_end = base_time + datetime.timedelta(minutes=5)

        # Create audit with attempt_count=1, FAILED status
        await MetricsAuditDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metric_name="Stats",
            interval_start=interval_start,
            interval_end=interval_end,
            collection_status=MetricsCollectionStatus.FAILED,
            attempt_count=1,
        )

        # Create audit with attempt_count=2, RUNNING status (should be excluded)
        await MetricsAuditDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metric_name="Stats",
            interval_start=interval_start,
            interval_end=interval_end,
            collection_status=MetricsCollectionStatus.RUNNING,
            attempt_count=2,
        )

        await test_async_session.commit()

        # Call the window function method
        audits_for_processing = await audit_service.get_instance_audit_metrics_for_processing(
            instance_name="test-instance", limit=10, max_retry_attempts=3
        )

        # Should return NO audits because highest attempt_count is RUNNING
        assert len(audits_for_processing) == 0

    @pytest.mark.asyncio
    async def test_window_function_respects_max_retry_attempts_for_not_started(
        self, test_async_session
    ):
        """Test max_retry_attempts logic for NOT_STARTED audits."""
        audit_service = MetricsAuditDbService(test_async_session)

        base_time = datetime.datetime(2025, 7, 10, 10, 0, 0, tzinfo=datetime.UTC)
        interval_start = base_time
        interval_end = base_time + datetime.timedelta(minutes=5)

        # Create audit with attempt_count=3, NOT_STARTED (at max_retry_attempts=3)
        audit_at_limit = await MetricsAuditDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metric_name="Stats",
            interval_start=interval_start,
            interval_end=interval_end,
            collection_status=MetricsCollectionStatus.NOT_STARTED,
            attempt_count=3,
        )

        await test_async_session.commit()

        # With max_retry_attempts=3, should include the audit (attempt_count <= 3)
        audits_for_processing = await audit_service.get_instance_audit_metrics_for_processing(
            instance_name="test-instance", limit=10, max_retry_attempts=3
        )
        assert len(audits_for_processing) == 1
        assert audits_for_processing[0].id == audit_at_limit.id

        # With max_retry_attempts=2, should exclude the audit (attempt_count > 2)
        audits_for_processing = await audit_service.get_instance_audit_metrics_for_processing(
            instance_name="test-instance", limit=10, max_retry_attempts=2
        )
        assert len(audits_for_processing) == 0

    @pytest.mark.asyncio
    async def test_window_function_respects_max_retry_attempts_for_failed(
        self, test_async_session
    ):
        """Test max_retry_attempts logic for FAILED audits."""
        audit_service = MetricsAuditDbService(test_async_session)

        base_time = datetime.datetime(2025, 7, 10, 10, 0, 0, tzinfo=datetime.UTC)
        interval_start = base_time
        interval_end = base_time + datetime.timedelta(minutes=5)

        # Create audit with attempt_count=3, FAILED
        await MetricsAuditDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metric_name="Stats",
            interval_start=interval_start,
            interval_end=interval_end,
            collection_status=MetricsCollectionStatus.FAILED,
            attempt_count=3,
        )

        await test_async_session.commit()

        # With max_retry_attempts=3, should EXCLUDE FAILED audit (attempt_count not < 3)
        audits_for_processing = await audit_service.get_instance_audit_metrics_for_processing(
            instance_name="test-instance", limit=10, max_retry_attempts=3
        )
        assert len(audits_for_processing) == 0

        # With max_retry_attempts=4, should INCLUDE FAILED audit (attempt_count < 4)
        audits_for_processing = await audit_service.get_instance_audit_metrics_for_processing(
            instance_name="test-instance", limit=10, max_retry_attempts=4
        )
        assert len(audits_for_processing) == 1

    @pytest.mark.asyncio
    async def test_window_function_filters_by_instance_name(self, test_async_session):
        """Test that window function correctly filters by instance name."""
        audit_service = MetricsAuditDbService(test_async_session)

        base_time = datetime.datetime(2025, 7, 10, 10, 0, 0, tzinfo=datetime.UTC)
        interval_start = base_time
        interval_end = base_time + datetime.timedelta(minutes=5)

        # Create audit for instance-1
        audit_instance_1 = await MetricsAuditDbFactory(
            async_session=test_async_session,
            manager_instance="instance-1",
            metric_name="Stats",
            interval_start=interval_start,
            interval_end=interval_end,
            collection_status=MetricsCollectionStatus.NOT_STARTED,
            attempt_count=1,
        )

        # Create audit for instance-2
        await MetricsAuditDbFactory(
            async_session=test_async_session,
            manager_instance="instance-2",
            metric_name="Stats",
            interval_start=interval_start,
            interval_end=interval_end,
            collection_status=MetricsCollectionStatus.NOT_STARTED,
            attempt_count=1,
        )

        await test_async_session.commit()

        # Query for instance-1 only
        audits_for_processing = await audit_service.get_instance_audit_metrics_for_processing(
            instance_name="instance-1", limit=10, max_retry_attempts=3
        )

        assert len(audits_for_processing) == 1
        assert audits_for_processing[0].id == audit_instance_1.id
        assert audits_for_processing[0].manager_instance == "instance-1"

    @pytest.mark.asyncio
    async def test_window_function_respects_limit_parameter(self, test_async_session):
        """Test that window function respects the limit parameter."""
        audit_service = MetricsAuditDbService(test_async_session)

        base_time = datetime.datetime(2025, 7, 10, 10, 0, 0, tzinfo=datetime.UTC)

        # Create 5 different intervals, each with one audit
        for i in range(5):
            interval_start = base_time + datetime.timedelta(minutes=i * 5)
            interval_end = interval_start + datetime.timedelta(minutes=5)

            await MetricsAuditDbFactory(
                async_session=test_async_session,
                manager_instance="test-instance",
                metric_name="Stats",
                interval_start=interval_start,
                interval_end=interval_end,
                collection_status=MetricsCollectionStatus.NOT_STARTED,
                attempt_count=1,
            )

        await test_async_session.commit()

        # Test limit=3
        audits_for_processing = await audit_service.get_instance_audit_metrics_for_processing(
            instance_name="test-instance", limit=3, max_retry_attempts=3
        )

        assert len(audits_for_processing) == 3

        # Test limit=10 (should return all 5)
        audits_for_processing = await audit_service.get_instance_audit_metrics_for_processing(
            instance_name="test-instance", limit=10, max_retry_attempts=3
        )

        assert len(audits_for_processing) == 5

    @pytest.mark.asyncio
    async def test_window_function_orders_by_interval_start_desc(self, test_async_session):
        """Test that window function orders results by interval_start DESC (most recent first)."""
        audit_service = MetricsAuditDbService(test_async_session)

        base_time = datetime.datetime(2025, 7, 10, 10, 0, 0, tzinfo=datetime.UTC)

        # Create 3 intervals in chronological order
        audits = []
        for i in range(3):
            interval_start = base_time + datetime.timedelta(
                minutes=i * 10
            )  # 10:00, 10:10, 10:20
            interval_end = interval_start + datetime.timedelta(minutes=5)

            audit = await MetricsAuditDbFactory(
                async_session=test_async_session,
                manager_instance="test-instance",
                metric_name="Stats",
                interval_start=interval_start,
                interval_end=interval_end,
                collection_status=MetricsCollectionStatus.NOT_STARTED,
                attempt_count=1,
            )
            audits.append(audit)

        await test_async_session.commit()

        # Query audits
        audits_for_processing = await audit_service.get_instance_audit_metrics_for_processing(
            instance_name="test-instance", limit=10, max_retry_attempts=3
        )

        assert len(audits_for_processing) == 3

        # Should be ordered by interval_start DESC (most recent first)
        # So: 10:20, 10:10, 10:00
        assert audits_for_processing[0].interval_start == base_time + datetime.timedelta(
            minutes=20
        )
        assert audits_for_processing[1].interval_start == base_time + datetime.timedelta(
            minutes=10
        )
        assert audits_for_processing[2].interval_start == base_time + datetime.timedelta(
            minutes=0
        )

    @pytest.mark.asyncio
    async def test_window_function_complex_scenario(self, test_async_session):
        """Test a complex scenario with multiple intervals, attempts, and statuses."""
        audit_service = MetricsAuditDbService(test_async_session)

        base_time = datetime.datetime(2025, 7, 10, 10, 0, 0, tzinfo=datetime.UTC)

        # Interval 1: Has COLLECTED (highest attempt), should be excluded
        interval_1_start = base_time
        interval_1_end = base_time + datetime.timedelta(minutes=5)

        await MetricsAuditDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metric_name="Stats",
            interval_start=interval_1_start,
            interval_end=interval_1_end,
            collection_status=MetricsCollectionStatus.FAILED,
            attempt_count=1,
        )

        await MetricsAuditDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metric_name="Stats",
            interval_start=interval_1_start,
            interval_end=interval_1_end,
            collection_status=MetricsCollectionStatus.COLLECTED,  # This excludes the interval
            attempt_count=2,
        )

        # Interval 2: Has RUNNING (highest attempt), should be excluded
        interval_2_start = base_time + datetime.timedelta(minutes=5)
        interval_2_end = base_time + datetime.timedelta(minutes=10)

        await MetricsAuditDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metric_name="Stats",
            interval_start=interval_2_start,
            interval_end=interval_2_end,
            collection_status=MetricsCollectionStatus.FAILED,
            attempt_count=1,
        )

        await MetricsAuditDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metric_name="Stats",
            interval_start=interval_2_start,
            interval_end=interval_2_end,
            collection_status=MetricsCollectionStatus.RUNNING,  # This excludes the interval
            attempt_count=2,
        )

        # Interval 3: Has NOT_STARTED (highest attempt), should be included
        interval_3_start = base_time + datetime.timedelta(minutes=10)
        interval_3_end = base_time + datetime.timedelta(minutes=15)

        await MetricsAuditDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metric_name="Stats",
            interval_start=interval_3_start,
            interval_end=interval_3_end,
            collection_status=MetricsCollectionStatus.FAILED,
            attempt_count=1,
        )

        audit_3_attempt_2 = await MetricsAuditDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metric_name="Stats",
            interval_start=interval_3_start,
            interval_end=interval_3_end,
            collection_status=MetricsCollectionStatus.NOT_STARTED,  # This should be included
            attempt_count=2,
        )

        # Interval 4: Has FAILED at max limit, should be excluded
        interval_4_start = base_time + datetime.timedelta(minutes=15)
        interval_4_end = base_time + datetime.timedelta(minutes=20)

        await MetricsAuditDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metric_name="Stats",
            interval_start=interval_4_start,
            interval_end=interval_4_end,
            collection_status=MetricsCollectionStatus.FAILED,
            attempt_count=3,  # At max_retry_attempts=3, FAILED audits need attempt_count < max_retry
        )

        await test_async_session.commit()

        # Query with max_retry_attempts=3
        audits_for_processing = await audit_service.get_instance_audit_metrics_for_processing(
            instance_name="test-instance", limit=10, max_retry_attempts=3
        )

        # Should return only interval 3 (the NOT_STARTED one)
        assert len(audits_for_processing) == 1
        assert audits_for_processing[0].id == audit_3_attempt_2.id
        assert audits_for_processing[0].attempt_count == 2
        assert audits_for_processing[0].collection_status == MetricsCollectionStatus.NOT_STARTED
