import datetime

import pytest
from metrics_collector.api_schema.models import Severity as AlarmSeverityEnum

from airspan_acp_metrics.database.services.alarm_db_service import AlarmDbService
from airspan_acp_metrics.pyd_models.alarm_enums import AlarmPriority
from airspan_acp_metrics.pyd_models.alarm_enums import AlarmStatus
from airspan_acp_metrics.pyd_models.alarm_enums import AlarmType
from airspan_acp_metrics.pyd_models.alarm_pyd_model import AlarmAcknowledgePydModel
from airspan_acp_metrics.pyd_models.alarm_pyd_model import AlarmCreatePydModel
from airspan_acp_metrics.pyd_models.alarm_pyd_model import AlarmFilterPydModel
from airspan_acp_metrics.pyd_models.alarm_pyd_model import AlarmResolvePydModel
from airspan_acp_metrics.pyd_models.alarm_pyd_model import AlarmUpdatePydModel
from airspan_acp_metrics.test.factories.alarm_db_factory import AlarmDbFactory
from airspan_acp_metrics.test.factories.alarm_type_db_factory import AlarmTypeDbFactory
from airspan_acp_metrics.test.factories.instance_db_factory import InstanceDbFactory


@pytest.mark.asyncio
class TestAlarmDbService:
    """Test cases for AlarmDbService."""

    async def test_create_alarm_success(self, test_async_session):
        """Test creating an alarm successfully."""
        # Create test instance and alarm type
        instance_db_model = await InstanceDbFactory(async_session=test_async_session)
        manager_instance = instance_db_model.manager_instance
        await AlarmTypeDbFactory(async_session=test_async_session)

        # Create alarm data
        alarm_data = AlarmCreatePydModel(
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
            name="Test SSH connectivity failed",
            source="test",
            description="Test description",
            manager_instance=manager_instance,
        )

        # Create alarm
        alarm_service = AlarmDbService(test_async_session)
        alarm = await alarm_service.create_alarm(alarm_data)

        # Verify alarm was created
        assert alarm is not None
        assert alarm.type == AlarmType.SSH_CONNECTIVITY_FAILED
        assert alarm.name == "Test SSH connectivity failed"
        assert alarm.source == "test"
        assert alarm.description == "Test description"
        assert alarm.manager_instance == manager_instance
        assert alarm.status == AlarmStatus.new
        assert alarm.severity == AlarmSeverityEnum.critical
        assert alarm.priority == AlarmPriority.high

    async def test_create_alarm_invalid_type(self, test_async_session):
        """Test creating an alarm with invalid type."""
        # Create test instance
        instance_db_model = await InstanceDbFactory(async_session=test_async_session)

        # Create alarm data with invalid type
        alarm_data = AlarmCreatePydModel(
            type=AlarmType.INVENTORY_MANAGER_UNREACHABLE,  # This type doesn't exist in test DB
            name="Test alarm",
            source="test",
            manager_instance=instance_db_model.manager_instance,
        )

        # Create alarm should fail
        alarm_service = AlarmDbService(test_async_session)
        with pytest.raises(ValueError, match="Alarm type .* not found"):
            await alarm_service.create_alarm(alarm_data)

    async def test_get_alarm_by_id_success(self, test_async_session):
        """Test getting an alarm by ID successfully."""
        # Create test alarm
        alarm_db_model = await AlarmDbFactory(async_session=test_async_session)

        # Get alarm by ID
        alarm_service = AlarmDbService(test_async_session)
        retrieved_alarm = await alarm_service.get_alarm_by_id(alarm_db_model.id)

        # Verify alarm was retrieved
        assert retrieved_alarm is not None
        assert retrieved_alarm.id == alarm_db_model.id
        assert retrieved_alarm.type == alarm_db_model.type
        assert retrieved_alarm.name == alarm_db_model.name

    async def test_get_alarm_by_id_not_found(self, test_async_session):
        """Test getting an alarm by ID when it doesn't exist."""
        import uuid

        # Get non-existent alarm
        alarm_service = AlarmDbService(test_async_session)
        retrieved_alarm = await alarm_service.get_alarm_by_id(uuid.uuid4())

        # Verify no alarm was retrieved
        assert retrieved_alarm is None

    async def test_get_alarms_by_filter(self, test_async_session):
        """Test getting alarms with filters."""
        # Create test alarms
        await AlarmDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
            severity=AlarmSeverityEnum.major,
            status=AlarmStatus.new,
        )
        await AlarmDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
            severity=AlarmSeverityEnum.critical,
            status=AlarmStatus.acknowledged,
        )

        # Filter by type
        alarm_service = AlarmDbService(test_async_session)
        filter_data = AlarmFilterPydModel(type=AlarmType.SSH_CONNECTIVITY_FAILED)
        alarms = await alarm_service.get_alarms_by_filter(filter_data)

        # Verify results
        assert len(alarms) == 2
        assert all(alarm.type == AlarmType.SSH_CONNECTIVITY_FAILED for alarm in alarms)

        # Filter by severity
        filter_data = AlarmFilterPydModel(severity=AlarmSeverityEnum.major)
        alarms = await alarm_service.get_alarms_by_filter(filter_data)

        # Verify results
        assert len(alarms) == 1
        assert alarms[0].severity == AlarmSeverityEnum.major

    async def test_update_alarm_success(self, test_async_session):
        """Test updating an alarm successfully."""
        # Create test alarm
        alarm_db_model = await AlarmDbFactory(async_session=test_async_session)

        # Update alarm
        alarm_service = AlarmDbService(test_async_session)
        update_data = AlarmUpdatePydModel(
            description="Updated description",
            updated_at=datetime.datetime.now(datetime.UTC),
        )
        updated_alarm = await alarm_service.update_alarm(alarm_db_model.id, update_data)

        # Verify alarm was updated
        assert updated_alarm is not None
        assert updated_alarm.description == "Updated description"
        assert updated_alarm.status == AlarmStatus.updated

    async def test_acknowledge_alarm_success(self, test_async_session):
        """Test acknowledging an alarm successfully."""
        # Create test alarm
        alarm_db_model = await AlarmDbFactory(async_session=test_async_session)

        # Acknowledge alarm
        alarm_service = AlarmDbService(test_async_session)
        acknowledge_data = AlarmAcknowledgePydModel(
            acknowledger="test_user",
            acknowledged=datetime.datetime.now(datetime.UTC),
        )
        acknowledged_alarm = await alarm_service.acknowledge_alarm(
            alarm_db_model.id, acknowledge_data
        )

        # Verify alarm was acknowledged
        assert acknowledged_alarm is not None
        assert acknowledged_alarm.acknowledger == "test_user"
        assert acknowledged_alarm.acknowledged is not None
        assert acknowledged_alarm.status == AlarmStatus.acknowledged

    async def test_resolve_alarm_success(self, test_async_session):
        """Test resolving an alarm successfully."""
        # Create test alarm
        alarm_db_model = await AlarmDbFactory(async_session=test_async_session)

        # Resolve alarm
        alarm_service = AlarmDbService(test_async_session)
        resolve_data = AlarmResolvePydModel(
            resolver="test_user",
            resolved_at=datetime.datetime.now(datetime.UTC),
        )
        resolved_alarm = await alarm_service.resolve_alarm(alarm_db_model.id, resolve_data)

        # Verify alarm was resolved
        assert resolved_alarm is not None
        assert resolved_alarm.resolver == "test_user"
        assert resolved_alarm.resolved_at is not None
        assert resolved_alarm.status == AlarmStatus.resolved

    async def test_get_active_alarms_by_instance(self, test_async_session):
        """Test getting active alarms for a specific instance."""
        # Create test alarms
        alarm1_db_model = await AlarmDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance-1",
            status=AlarmStatus.new,
        )
        await AlarmDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance-1",
            status=AlarmStatus.resolved,
        )
        await AlarmDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance-2",
            status=AlarmStatus.new,
        )

        # Get active alarms for instance 1
        alarm_service = AlarmDbService(test_async_session)
        active_alarms = await alarm_service.get_active_alarms_by_instance("test-instance-1")

        # Verify results
        assert len(active_alarms) == 1
        assert active_alarms[0].id == alarm1_db_model.id
        assert active_alarms[0].status == AlarmStatus.new

    async def test_get_active_alarm_by_type_and_instance(self, test_async_session):
        """Test getting active alarm by type and instance."""
        # Create test alarms
        alarm1_db_model = await AlarmDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
            manager_instance="test-instance-1",
            status=AlarmStatus.new,
        )
        await AlarmDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
            manager_instance="test-instance-1",
            status=AlarmStatus.resolved,
        )

        # Get active alarm
        alarm_service = AlarmDbService(test_async_session)
        active_alarm = await alarm_service.get_active_alarm_by_type_and_instance(
            AlarmType.SSH_CONNECTIVITY_FAILED, "test-instance-1"
        )

        # Verify results
        assert active_alarm is not None
        assert active_alarm.id == alarm1_db_model.id
        assert active_alarm.status == AlarmStatus.new

    async def test_get_alarm_count_by_severity(self, test_async_session):
        """Test getting alarm count by severity."""
        # Create test alarms
        await AlarmDbFactory(
            async_session=test_async_session,
            severity=AlarmSeverityEnum.critical,
            status=AlarmStatus.new,
        )
        await AlarmDbFactory(
            async_session=test_async_session,
            severity=AlarmSeverityEnum.major,
            status=AlarmStatus.new,
        )
        await AlarmDbFactory(
            async_session=test_async_session,
            severity=AlarmSeverityEnum.major,
            status=AlarmStatus.acknowledged,
        )
        await AlarmDbFactory(
            async_session=test_async_session,
            severity=AlarmSeverityEnum.major,
            status=AlarmStatus.resolved,
        )

        # Get alarm count by severity
        alarm_service = AlarmDbService(test_async_session)
        severity_counts = await alarm_service.get_alarm_count_by_severity()

        # Verify results
        assert severity_counts[AlarmSeverityEnum.critical] == 1
        assert severity_counts[AlarmSeverityEnum.major] == 2  # Only active ones
