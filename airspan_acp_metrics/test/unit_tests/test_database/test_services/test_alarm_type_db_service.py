import pytest
from metrics_collector.api_schema.models import Severity as AlarmSeverityEnum

from airspan_acp_metrics.database.services.alarm_type_db_service import AlarmTypeDbService
from airspan_acp_metrics.pyd_models.alarm_enums import AlarmPriority
from airspan_acp_metrics.pyd_models.alarm_enums import AlarmType
from airspan_acp_metrics.pyd_models.alarm_pyd_model import AlarmTypePydModel
from airspan_acp_metrics.pyd_models.alarm_pyd_model import AlarmTypeUpdatePydModel
from airspan_acp_metrics.test.factories.alarm_db_factory import AlarmDbFactory
from airspan_acp_metrics.test.factories.alarm_type_db_factory import AlarmTypeDbFactory


@pytest.mark.asyncio
class TestAlarmTypeDbService:
    """Test cases for AlarmTypeDbService."""

    async def test_get_all_alarm_types(self, test_async_session):
        """Test getting all alarm types."""
        # Create test alarm types
        await AlarmTypeDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
        )
        await AlarmTypeDbFactory(
            async_session=test_async_session,
            type=AlarmType.INVENTORY_MANAGER_UNREACHABLE,
        )

        # Get all alarm types
        alarm_type_service = AlarmTypeDbService(test_async_session)
        alarm_types = await alarm_type_service.get_all_alarm_types()

        # Verify results
        assert len(alarm_types) == 2
        type_names = [at.type for at in alarm_types]
        assert AlarmType.SSH_CONNECTIVITY_FAILED in type_names
        assert AlarmType.INVENTORY_MANAGER_UNREACHABLE in type_names

    async def test_get_alarm_type_by_type_success(self, test_async_session):
        """Test getting an alarm type by type successfully."""
        # Create test alarm type
        alarm_type_db_model = await AlarmTypeDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
        )

        # Get alarm type by type
        alarm_type_service = AlarmTypeDbService(test_async_session)
        retrieved_type = await alarm_type_service.get_alarm_type_by_type(
            AlarmType.SSH_CONNECTIVITY_FAILED
        )

        # Verify results
        assert retrieved_type is not None
        assert retrieved_type.type == AlarmType.SSH_CONNECTIVITY_FAILED
        assert retrieved_type.severity == alarm_type_db_model.severity
        assert retrieved_type.priority == alarm_type_db_model.priority

    async def test_get_alarm_type_by_type_not_found(self, test_async_session):
        """Test getting an alarm type by type when it doesn't exist."""
        # Get non-existent alarm type
        alarm_type_service = AlarmTypeDbService(test_async_session)
        retrieved_type = await alarm_type_service.get_alarm_type_by_type(
            AlarmType.INVENTORY_MANAGER_UNREACHABLE
        )

        # Verify no alarm type was retrieved
        assert retrieved_type is None

    async def test_create_alarm_type_success(self, test_async_session):
        """Test creating an alarm type successfully."""
        # Create alarm type data
        alarm_type_data = AlarmTypePydModel(
            type=AlarmType.INVENTORY_MANAGER_UNREACHABLE,
            severity=AlarmSeverityEnum.critical,
            priority=AlarmPriority.high,
            repairs=["Check inventory service", "Restart service"],
        )

        # Create alarm type
        alarm_type_service = AlarmTypeDbService(test_async_session)
        created_type = await alarm_type_service.create_alarm_type(alarm_type_data)

        # Verify alarm type was created
        assert created_type is not None
        assert created_type.type == AlarmType.INVENTORY_MANAGER_UNREACHABLE
        assert created_type.severity == AlarmSeverityEnum.critical
        assert created_type.priority == AlarmPriority.high
        assert created_type.repairs == ["Check inventory service", "Restart service"]

    async def test_update_alarm_type_success(self, test_async_session):
        """Test updating an alarm type successfully."""
        # Create test alarm type
        await AlarmTypeDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
        )

        # Update alarm type
        alarm_type_service = AlarmTypeDbService(test_async_session)
        update_data = AlarmTypeUpdatePydModel(
            severity=AlarmSeverityEnum.critical,
            priority=AlarmPriority.medium,
            repairs=["Updated repair actions"],
        )
        updated_type = await alarm_type_service.update_alarm_type(
            AlarmType.SSH_CONNECTIVITY_FAILED, update_data
        )

        # Verify alarm type was updated
        assert updated_type is not None
        assert updated_type.severity == AlarmSeverityEnum.critical
        assert updated_type.priority == AlarmPriority.medium
        assert updated_type.repairs == ["Updated repair actions"]

    async def test_update_alarm_type_not_found(self, test_async_session):
        """Test updating an alarm type when it doesn't exist."""
        # Update non-existent alarm type
        alarm_type_service = AlarmTypeDbService(test_async_session)
        update_data = AlarmTypeUpdatePydModel(
            severity=AlarmSeverityEnum.critical,
            priority=AlarmPriority.high,
            repairs=[],
        )
        updated_type = await alarm_type_service.update_alarm_type(
            AlarmType.INVENTORY_MANAGER_UNREACHABLE, update_data
        )

        # Verify no alarm type was updated
        assert updated_type is None

    async def test_delete_alarm_type_success(self, test_async_session):
        """Test deleting an alarm type successfully."""
        # Create test alarm type
        await AlarmTypeDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
        )

        # Delete alarm type
        alarm_type_service = AlarmTypeDbService(test_async_session)
        deleted = await alarm_type_service.delete_alarm_type(AlarmType.SSH_CONNECTIVITY_FAILED)

        # Verify alarm type was deleted
        assert deleted is True

        # Verify alarm type no longer exists
        retrieved_type = await alarm_type_service.get_alarm_type_by_type(
            AlarmType.SSH_CONNECTIVITY_FAILED
        )
        assert retrieved_type is None

    async def test_delete_alarm_type_with_alarms(self, test_async_session):
        """Test deleting an alarm type that has existing alarms."""
        # Create test alarm type and alarm
        await AlarmTypeDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
        )
        await AlarmDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
        )

        # Try to delete alarm type
        alarm_type_service = AlarmTypeDbService(test_async_session)
        deleted = await alarm_type_service.delete_alarm_type(AlarmType.SSH_CONNECTIVITY_FAILED)

        # Verify alarm type was not deleted
        assert deleted is False

        # Verify alarm type still exists
        retrieved_type = await alarm_type_service.get_alarm_type_by_type(
            AlarmType.SSH_CONNECTIVITY_FAILED
        )
        assert retrieved_type is not None

    async def test_delete_alarm_type_not_found(self, test_async_session):
        """Test deleting an alarm type when it doesn't exist."""
        # Delete non-existent alarm type
        alarm_type_service = AlarmTypeDbService(test_async_session)
        deleted = await alarm_type_service.delete_alarm_type(
            AlarmType.INVENTORY_MANAGER_UNREACHABLE
        )

        # Verify no alarm type was deleted
        assert deleted is False

    async def test_get_alarm_type_usage_count(self, test_async_session):
        """Test getting usage count for an alarm type."""
        # Create test alarm type and alarms
        await AlarmTypeDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
        )
        await AlarmDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
        )
        await AlarmDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
        )

        # Get usage count
        alarm_type_service = AlarmTypeDbService(test_async_session)
        usage_count = await alarm_type_service.get_alarm_type_usage_count(
            AlarmType.SSH_CONNECTIVITY_FAILED
        )

        # Verify usage count
        assert usage_count == 2
