import datetime
from unittest.mock import MagicMock

import pytest
from metrics_collector.api_schema.models import Severity as AlarmSeverityEnum

from airspan_acp_metrics.managers.alarm_manager import AlarmManager
from airspan_acp_metrics.pyd_models import AlarmEvent
from airspan_acp_metrics.pyd_models import AlarmEventData
from airspan_acp_metrics.pyd_models import AlarmEventHeader
from airspan_acp_metrics.pyd_models import AlarmResponsePydModel
from airspan_acp_metrics.pyd_models.alarm_enums import AlarmStatus
from airspan_acp_metrics.pyd_models.alarm_enums import AlarmType
from airspan_acp_metrics.pyd_models.instance_pyd_model import InstancePydModel
from airspan_acp_metrics.test.factories.alarm_db_factory import AlarmDbFactory
from airspan_acp_metrics.test.factories.alarm_type_db_factory import AlarmTypeDbFactory
from airspan_acp_metrics.test.factories.instance_db_factory import InstanceDbFactory


@pytest.mark.asyncio
class TestAlarmManager:
    """Test cases for AlarmManager."""

    @pytest.fixture
    def mock_pubsub(self):
        """Create a mock pubsub instance."""
        pubsub = MagicMock()
        pubsub.set_topic = MagicMock()
        pubsub.push_payload = MagicMock()
        return pubsub

    @pytest.fixture
    def alarm_manager(self, test_async_session, mock_pubsub):
        """Create an alarm manager instance."""
        return AlarmManager(test_async_session, mock_pubsub, "test-alarm-topic")

    async def test_raise_alarm_success(self, test_async_session, alarm_manager, mock_pubsub):
        """Test raising an alarm successfully."""
        # Create test instance and alarm type
        instance_db_model = await InstanceDbFactory(async_session=test_async_session)
        alarm_type_db_model = await AlarmTypeDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
        )
        await test_async_session.commit()
        await test_async_session.refresh(instance_db_model)
        await test_async_session.refresh(alarm_type_db_model)

        instance_pyd = InstancePydModel.model_validate(instance_db_model)

        # Raise alarm
        raised_alarm = await alarm_manager.raise_alarm(
            alarm_type=AlarmType.SSH_CONNECTIVITY_FAILED,
            name="Test SSH connectivity failed",
            source="test",
            instance=instance_pyd,
            description="Test description",
            prefix_log="TestPrefix",
        )

        # Verify alarm was raised
        assert raised_alarm is not None
        assert raised_alarm.type == AlarmType.SSH_CONNECTIVITY_FAILED
        assert raised_alarm.name == "Test SSH connectivity failed"
        assert raised_alarm.source == "test"
        assert raised_alarm.description == "Test description"
        assert raised_alarm.status == AlarmStatus.new

        # Verify pubsub was called
        mock_pubsub.set_topic.assert_called_once_with("test-alarm-topic")
        mock_pubsub.push_payload.assert_called_once()

    async def test_raise_alarm_duplicate_prevention(
        self, test_async_session, alarm_manager, mock_pubsub
    ):
        """Test that duplicate alarms are not created."""
        # Create test instance and alarm type
        instance_db_model = await InstanceDbFactory(async_session=test_async_session)
        instance_pyd = InstancePydModel.model_validate(instance_db_model)
        await AlarmTypeDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
        )

        # Create existing alarm
        existing_alarm_db_model = await AlarmDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
            manager_instance=instance_pyd.manager_instance,
            status=AlarmStatus.new,
        )

        await test_async_session.commit()

        # Try to raise duplicate alarm
        raised_alarm = await alarm_manager.raise_alarm(
            alarm_type=AlarmType.SSH_CONNECTIVITY_FAILED,
            name="Test SSH connectivity failed",
            source="test",
            instance=instance_pyd,
            description="Test description",
            prefix_log="TestPrefix",
        )

        # Verify existing alarm was returned
        assert raised_alarm is not None
        assert raised_alarm.id == existing_alarm_db_model.id

        # Verify pubsub was not called (no new alarm)
        mock_pubsub.set_topic.assert_not_called()
        mock_pubsub.push_payload.assert_not_called()

    async def test_clear_alarm_success(self, test_async_session, alarm_manager, mock_pubsub):
        """Test clearing an alarm successfully."""
        # Create test instance and alarm type
        instance_db_model = await InstanceDbFactory(async_session=test_async_session)
        instance_pyd = InstancePydModel.model_validate(instance_db_model)
        await AlarmTypeDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
        )

        # Create active alarm
        active_alarm_db_model = await AlarmDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
            manager_instance=instance_pyd.manager_instance,
            status=AlarmStatus.new,
        )

        # Clear alarm
        cleared_alarm = await alarm_manager.clear_alarm(
            alarm_type=AlarmType.SSH_CONNECTIVITY_FAILED,
            instance=instance_pyd,
            resolver="test_resolver",
            prefix_log="TestPrefix",
        )

        # Verify alarm was cleared
        assert cleared_alarm is not None
        assert cleared_alarm.id == active_alarm_db_model.id
        assert cleared_alarm.status == AlarmStatus.resolved
        assert cleared_alarm.resolver == "test_resolver"
        assert cleared_alarm.resolved_at is not None

        # Verify pubsub was called
        mock_pubsub.set_topic.assert_called_once_with("test-alarm-topic")
        mock_pubsub.push_payload.assert_called_once()

    async def test_clear_alarm_not_found(self, test_async_session, alarm_manager, mock_pubsub):
        """Test clearing an alarm when no active alarm exists."""
        # Create test instance and alarm type
        instance_db_model = await InstanceDbFactory(async_session=test_async_session)
        instance_pyd = InstancePydModel.model_validate(instance_db_model)

        await AlarmTypeDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
        )

        # Try to clear non-existent alarm
        cleared_alarm = await alarm_manager.clear_alarm(
            alarm_type=AlarmType.SSH_CONNECTIVITY_FAILED,
            instance=instance_pyd,
            resolver="test_resolver",
            prefix_log="TestPrefix",
        )

        # Verify no alarm was cleared
        assert cleared_alarm is None

        # Verify pubsub was not called
        mock_pubsub.set_topic.assert_not_called()
        mock_pubsub.push_payload.assert_not_called()

    async def test_generate_alarm_event(self, test_async_session, alarm_manager):
        """Test generating an alarm event."""
        # Create test instance and alarm
        instance_db_model = await InstanceDbFactory(async_session=test_async_session)
        instance_pyd = InstancePydModel.model_validate(instance_db_model)

        alarm_db_model = await AlarmDbFactory(
            async_session=test_async_session,
            manager_instance=instance_db_model.manager_instance,
        )
        alarm_pyd = AlarmResponsePydModel.model_validate(alarm_db_model)

        # Generate alarm event
        alarm_event = alarm_manager._generate_alarm_event(instance_pyd, alarm_pyd)

        # Verify event structure
        assert alarm_event.header.domain == "nms"
        assert alarm_event.header.eventId == str(alarm_db_model.id)
        assert alarm_event.header.eventName == alarm_db_model.name
        assert alarm_event.header.eventType == "alarm"
        assert alarm_event.header.sourceName == instance_pyd.manager_instance
        assert alarm_event.header.reportingEntityName == "airspan-acp-metrics"

        assert alarm_event.data.objectId == instance_pyd.manager_instance
        assert alarm_event.data.objectType == "ACP"
        assert alarm_event.data.type == alarm_db_model.type
        assert alarm_event.data.perceivedSeverity == alarm_db_model.severity
        assert alarm_event.data.trendIndication == alarm_db_model.status

    async def test_publish_alarm_event(self, test_async_session, alarm_manager, mock_pubsub):
        """Test publishing an alarm event."""

        event_header = AlarmEventHeader(
            domain="nms",
            eventId="test-event-id",
            eventName="Test Event",
            eventType="alarm",
            priority="HIGH",
            reportingEntityName="test",
            sourceName="test-instance",
            sourceId="test-instance",
            eventTime=datetime.datetime.now(),
            eventDuration=0,
            systemDN="test",
        )

        event_data = AlarmEventData(
            objectId="test-instance",
            objectType="ACP",
            streetCellId="",
            uri="https://test.com",
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
            cause="Test cause",
            perceivedSeverity=AlarmSeverityEnum.major,
            specificProblem="Test problem",
            trendIndication=AlarmStatus.new,
            monitoredAttributes=[],
            proposedRepairActions=[],
            additionalText="",
            additionalInformation="",
        )

        alarm_event = AlarmEvent(header=event_header, data=event_data)

        # Publish alarm event
        alarm_manager._publish_alarm_event(alarm_event, "TestPrefix")

        # Verify pubsub was called
        mock_pubsub.set_topic.assert_called_once_with("test-alarm-topic")
        mock_pubsub.push_payload.assert_called_once()

    async def test_raise_alarm_with_exception(
        self, test_async_session, alarm_manager, mock_pubsub
    ):
        """Test raising an alarm when an exception occurs."""
        # Create test instance (no alarm type created to cause exception)
        instance_db_model = await InstanceDbFactory(async_session=test_async_session)

        instance_pyd = InstancePydModel.model_validate(instance_db_model)

        # Try to raise alarm with non-existent alarm type
        raised_alarm = await alarm_manager.raise_alarm(
            alarm_type=AlarmType.INVENTORY_MANAGER_UNREACHABLE,
            name="Test alarm",
            source="test",
            instance=instance_pyd,
            description="Test description",
            prefix_log="TestPrefix",
        )

        # Verify no alarm was raised
        assert raised_alarm is None

        # Verify pubsub was not called
        mock_pubsub.set_topic.assert_not_called()
        mock_pubsub.push_payload.assert_not_called()
