import datetime
import io
import zipfile
from pathlib import Path
from unittest.mock import AsyncMock
from unittest.mock import <PERSON>Mock
from unittest.mock import patch

import pytest
from da_common.models import Status
from google.cloud.exceptions import Forbidden
from google.cloud.exceptions import GoogleCloudError
from google.cloud.exceptions import NotFound

from airspan_acp_metrics.database.services.metric_audit_db_service import MetricsAuditDbService
from airspan_acp_metrics.managers.gcs_operations_manager import GCSOperationsManager
from airspan_acp_metrics.managers.metrics_collector_manager import MetricsCollector
from airspan_acp_metrics.pyd_models.utils import MetricsCollectionStatus
from airspan_acp_metrics.test.factories.metrics_audit_db_factory import MetricsAuditDbFactory


def read_zip_file_to_bytesio(file_path: str | Path) -> io.BytesIO:
    """Read a zip file from local path and return as BytesIO object.

    Args:
        file_path: Path to the zip file

    Returns:
        BytesIO object containing the zip file data

    Raises:
        FileNotFoundError: If the file doesn't exist
        IOError: If there's an error reading the file
    """
    file_path = Path(file_path)
    if not file_path.exists():
        raise FileNotFoundError(f"Zip file not found: {file_path}")

    if not file_path.is_file():
        raise OSError(f"Path is not a file: {file_path}")

    with open(file_path, "rb") as f:
        zip_data = f.read()

    return io.BytesIO(zip_data)


class TestGCSOperationsManager:
    """Tests for the GCSOperationsManager class including both success and exception scenarios.

    The gcs_ops_manager fixture automatically mocks google.cloud.storage.Client with default behavior.
    For most tests, you can simply use the fixture without additional mocking.

    For exception tests that need to override mock behavior:
    1. Use the attached mock objects (e.g., gcs_ops_manager._mock_bucket.exists.side_effect = Exception())
    2. For complex overrides, you can still use @patch decorator in individual test methods
    """

    @pytest.fixture
    def gcs_ops_manager(self, test_config):
        """Create a GCSOperationsManager instance with mocked GCS client."""
        with patch("google.cloud.storage.Client") as mock_client_class:
            # Create mock objects that tests expect
            mock_client = MagicMock()
            mock_bucket = MagicMock()
            mock_blob = MagicMock()

            # Set up default mock behavior
            mock_client_class.return_value = mock_client
            mock_client.bucket.return_value = mock_bucket
            mock_bucket.blob.return_value = mock_blob
            mock_bucket.exists.return_value = True

            # Create the GCS manager with mocked client
            gcs_manager = GCSOperationsManager(test_config)

            # Attach mock objects to the manager for test access
            gcs_manager._mock_client = mock_client
            gcs_manager._mock_bucket = mock_bucket
            gcs_manager._mock_blob = mock_blob
            gcs_manager._mock_client_class = mock_client_class

            yield gcs_manager

    @pytest.fixture
    def sample_metric_config(self):
        """Create a sample metric configuration."""
        return {
            "metric_name": "Stats",
            "enabled": True,
            "frequency": 5,
        }

    @pytest.fixture
    def sample_zip_file_data(self):
        """Create sample zip file data with multiple files."""
        test_zip_path = (
            Path(__file__).parent.parent.parent
            / "test_data"
            / "acp-dauk-mrl-green-acp-Stats-20250607_0030_0035.zip"
        )
        return read_zip_file_to_bytesio(test_zip_path)

    @pytest.fixture
    def remote_file_metadata(self):
        """Create sample remote file metadata."""
        return {
            # Remote host metadata
            "file_size_bytes": 172335,
            "remote_file_mode": 33188,  # File permissions
            "remote_file_mtime_iso": "2025-06-07T00:35:11+00:00",  # ISO format
            "remote_file_atime_iso": "2025-06-07T00:35:11+00:00",  # ISO format
            "remote_file_path": "/var/log/nms/acp_instance_Stats_20250607_0030_0035.zip",
        }

    @pytest.fixture
    def metrics_collector(self, test_config, test_async_session_maker):
        """Create a MetricsCollector instance for integration tests."""
        metrics_collector = MetricsCollector(test_config)
        metrics_collector.pub_sub = MagicMock()
        return metrics_collector

    def test_get_zip_file_count(self, gcs_ops_manager, sample_zip_file_data):
        """Test _get_zip_file_count method with a real zip file."""
        # Test the file count method
        file_count = gcs_ops_manager._get_zip_file_count(sample_zip_file_data)

        # Should count 3 files (file1.txt, file2.log, data/file3.csv)
        # but not the directory (empty_dir/)
        assert file_count == 233

        # Verify the BytesIO position is preserved
        original_position = sample_zip_file_data.tell()
        gcs_ops_manager._get_zip_file_count(sample_zip_file_data)
        assert sample_zip_file_data.tell() == original_position

    def test_calculate_file_hash(self, gcs_ops_manager, sample_zip_file_data):
        """Test _calculate_file_hash method with a real zip file."""
        # Test the hash calculation method
        file_hash = gcs_ops_manager._calculate_file_hash(sample_zip_file_data)

        # Should return a valid SHA256 hash (64 hex characters)
        assert isinstance(file_hash, str)
        assert len(file_hash) == 64
        assert all(c in "0123456789abcdef" for c in file_hash)

        # Verify the BytesIO position is preserved
        original_position = sample_zip_file_data.tell()
        gcs_ops_manager._calculate_file_hash(sample_zip_file_data)
        assert sample_zip_file_data.tell() == original_position

        # Verify hash is consistent for the same file
        hash1 = gcs_ops_manager._calculate_file_hash(sample_zip_file_data)
        hash2 = gcs_ops_manager._calculate_file_hash(sample_zip_file_data)
        assert hash1 == hash2

    def test_get_zip_file_count_with_local_file(self):
        """Test reading a zip file from local path (if it exists)."""
        # Use relative path from the project root to the test data
        test_zip_path = (
            Path(__file__).parent.parent
            / "test_data"
            / "acp-dauk-mrl-green-acp-Stats-20250607_0030_0035.zip"
        )

        try:
            zip_data = read_zip_file_to_bytesio(test_zip_path)
            file_count = GCSOperationsManager._get_zip_file_count(zip_data)

            # The actual zip file should contain some files
            assert file_count > 0

        except FileNotFoundError:
            # If the file doesn't exist, test with a created zip
            zip_buffer = io.BytesIO()
            with zipfile.ZipFile(zip_buffer, "w") as zf:
                zf.writestr("test.txt", "test content")
            zip_buffer.seek(0)

            file_count = GCSOperationsManager._get_zip_file_count(zip_buffer)
            assert file_count == 1

    # SUCCESS TESTS
    @pytest.mark.asyncio
    async def test_upload_file_to_gcs_success(
        self,
        gcs_ops_manager,
        sample_instance_pyd_model,
        sample_metric_config,
        sample_zip_file_data,
        remote_file_metadata,
    ):
        """Test GCS operations manager successful upload."""
        # GCS client is already mocked in the fixture
        # Mock objects are available at gcs_ops_manager._mock_client, _mock_bucket, _mock_blob if needed

        file_metadata, gcs_path = await gcs_ops_manager.upload_file_to_gcs(
            file_data=sample_zip_file_data,
            instance_pyd_model=sample_instance_pyd_model,
            metric_config=sample_metric_config,
            retry_attempts=1,
            successful_attempts=1,
            remote_file_metadata=remote_file_metadata,
        )

        # Verify upload was successful
        assert file_metadata["file_size_bytes"] == 172335
        assert file_metadata["file_count"] == 233
        assert "file_hash_sha256" in file_metadata
        # TODO: find another way to get when the file was created on the host
        #  as sftp client is incapable of getting the file creation time
        # assert "file_timestamp" in file_metadata
        assert file_metadata["retry_attempts"] == 1
        assert file_metadata["successful_attempts"] == 1
        assert "remote_file_mtime_iso" in file_metadata
        assert "remote_file_atime_iso" in file_metadata
        assert (
            gcs_path
            == f"{gcs_ops_manager.bucket_name}/test-instance-1/Stats/20250607_0030_0035.zip"
        )

        # TODO: this is very flaky, it looks like unit test are polluted and hence  mock_blob.upload_from_file.assert_called_once returns False
        # assert mock_blob.upload_from_file.assert_called_once()

    @pytest.mark.asyncio
    async def test_upload_file_to_gcs_with_retry_attempts(
        self,
        gcs_ops_manager,
        sample_instance_pyd_model,
        sample_metric_config,
        sample_zip_file_data,
        remote_file_metadata,
    ):
        """Test GCS upload with custom retry and success attempt counts."""
        mock_client = MagicMock()
        mock_bucket = MagicMock()
        mock_blob = MagicMock()

        mock_bucket.exists.return_value = True
        mock_client.bucket.return_value = mock_bucket
        mock_bucket.blob.return_value = mock_blob

        with patch(
            "google.cloud.storage.Client",
            return_value=mock_client,
        ):
            mock_client.bucket.return_value = mock_bucket
            mock_bucket.blob.return_value = mock_blob

            file_metadata, gcs_path = await gcs_ops_manager.upload_file_to_gcs(
                file_data=sample_zip_file_data,
                instance_pyd_model=sample_instance_pyd_model,
                metric_config=sample_metric_config,
                retry_attempts=3,
                successful_attempts=1,
                remote_file_metadata=remote_file_metadata,
            )

            # Verify upload was successful with custom attempt counts
            assert file_metadata["file_size_bytes"] == 172335
            assert file_metadata["file_count"] == 233
            assert "file_hash_sha256" in file_metadata
            assert file_metadata["retry_attempts"] == 3
            assert file_metadata["successful_attempts"] == 1
            assert "remote_file_mtime_iso" in file_metadata
            assert "remote_file_atime_iso" in file_metadata
            assert (
                gcs_path
                == f"{gcs_ops_manager.bucket_name}/test-instance-1/Stats/20250607_0030_0035.zip"
            )

    @pytest.mark.asyncio
    async def test_upload_file_to_gcs_bucket_already_exists(
        self,
        gcs_ops_manager,
        sample_instance_pyd_model,
        sample_metric_config,
        sample_zip_file_data,
        remote_file_metadata,
    ):
        """Test GCS upload when bucket already exists."""
        mock_client = MagicMock()
        mock_bucket = MagicMock()
        mock_blob = MagicMock()

        # Mock bucket exists check
        mock_bucket.exists.return_value = True
        mock_client.bucket.return_value = mock_bucket
        mock_bucket.blob.return_value = mock_blob

        with patch(
            "airspan_acp_metrics.managers.gcs_operations_manager.gcs.Client",
            return_value=mock_client,
        ):
            file_metadata, gcs_path = await gcs_ops_manager.upload_file_to_gcs(
                file_data=sample_zip_file_data,
                instance_pyd_model=sample_instance_pyd_model,
                metric_config=sample_metric_config,
                retry_attempts=1,
                successful_attempts=1,
                remote_file_metadata=remote_file_metadata,
            )

            # Should succeed even when bucket already exists
            assert file_metadata["file_size_bytes"] == 172335
            assert file_metadata["file_count"] == 233
            assert "file_hash_sha256" in file_metadata
            assert "remote_file_mtime_iso" in file_metadata
            assert "remote_file_atime_iso" in file_metadata
            assert file_metadata["retry_attempts"] == 1
            assert file_metadata["successful_attempts"] == 1
            assert (
                gcs_path
                == f"{gcs_ops_manager.bucket_name}/test-instance-1/Stats/20250607_0030_0035.zip"
            )

    # EXCEPTION TESTS
    @pytest.mark.asyncio
    async def test_upload_file_to_gcs_storage_client_initialization_failure(
        self,
        gcs_ops_manager,
        sample_instance_pyd_model,
        sample_metric_config,
        sample_zip_file_data,
        remote_file_metadata,
    ):
        """Test upload_file_to_gcs when storage client initialization fails."""

        with patch(
            "airspan_acp_metrics.managers.gcs_operations_manager.gcs.Client",
            side_effect=GoogleCloudError("Failed to initialize storage client"),
        ):
            file_metadata, gcs_path = await gcs_ops_manager.upload_file_to_gcs(
                file_data=sample_zip_file_data,
                instance_pyd_model=sample_instance_pyd_model,
                metric_config=sample_metric_config,
                retry_attempts=1,
                successful_attempts=1,
                remote_file_metadata=remote_file_metadata,
            )

            # Should return None, None on exception
            assert file_metadata is None
            assert "Failed to initialize storage client" in gcs_path

    @pytest.mark.asyncio
    async def test_upload_file_to_gcs_bucket_creation_forbidden_error(
        self,
        gcs_ops_manager,
        sample_instance_pyd_model,
        sample_metric_config,
        sample_zip_file_data,
        remote_file_metadata,
    ):
        """Test upload_file_to_gcs when bucket creation is forbidden."""
        mock_client = MagicMock()
        mock_bucket = MagicMock()
        mock_blob = MagicMock()
        mock_bucket.exists.return_value = False
        mock_client.bucket.return_value = mock_bucket
        mock_client.create_bucket.side_effect = Forbidden("Insufficient permissions")
        mock_bucket.blob.return_value = mock_blob

        with patch(
            "airspan_acp_metrics.managers.gcs_operations_manager.gcs.Client",
            return_value=mock_client,
        ):
            file_metadata, gcs_path = await gcs_ops_manager.upload_file_to_gcs(
                file_data=sample_zip_file_data,
                instance_pyd_model=sample_instance_pyd_model,
                metric_config=sample_metric_config,
                retry_attempts=1,
                successful_attempts=1,
                remote_file_metadata=remote_file_metadata,
            )

            # Should still proceed with upload despite bucket creation failure
            assert file_metadata["file_size_bytes"] == 172335
            assert file_metadata["file_count"] == 233
            assert "file_hash_sha256" in file_metadata
            assert "remote_file_mtime_iso" in file_metadata
            assert "remote_file_atime_iso" in file_metadata
            assert file_metadata["retry_attempts"] == 1
            assert file_metadata["successful_attempts"] == 1
            assert (
                gcs_path
                == f"{gcs_ops_manager.bucket_name}/test-instance-1/Stats/20250607_0030_0035.zip"
            )

    @pytest.mark.asyncio
    async def test_upload_file_to_gcs_bucket_access_not_found_error(
        self,
        gcs_ops_manager,
        sample_instance_pyd_model,
        sample_metric_config,
        sample_zip_file_data,
        remote_file_metadata,
    ):
        """Test upload_file_to_gcs when bucket access returns not found."""
        # Override the default mock behavior for this exception test
        mock_bucket = gcs_ops_manager._mock_bucket
        mock_bucket.exists.side_effect = NotFound("Bucket not found")

        file_metadata, gcs_path = await gcs_ops_manager.upload_file_to_gcs(
            file_data=sample_zip_file_data,
            instance_pyd_model=sample_instance_pyd_model,
            metric_config=sample_metric_config,
            retry_attempts=1,
            successful_attempts=1,
            remote_file_metadata=remote_file_metadata,
        )

        # Should still proceed with upload despite bucket access failure
        assert file_metadata["file_size_bytes"] == 172335
        assert file_metadata["file_count"] == 233
        assert "file_hash_sha256" in file_metadata
        assert "remote_file_mtime_iso" in file_metadata
        assert "remote_file_atime_iso" in file_metadata
        assert file_metadata["retry_attempts"] == 1
        assert file_metadata["successful_attempts"] == 1
        assert (
            gcs_path
            == f"{gcs_ops_manager.bucket_name}/test-instance-1/Stats/20250607_0030_0035.zip"
        )

    @pytest.mark.asyncio
    async def test_upload_file_to_gcs_blob_upload_failure(
        self,
        gcs_ops_manager,
        sample_instance_pyd_model,
        sample_metric_config,
        sample_zip_file_data,
        remote_file_metadata,
    ):
        """Test upload_file_to_gcs when blob upload fails."""
        mock_client = MagicMock()
        mock_bucket = MagicMock()
        mock_blob = MagicMock()
        mock_bucket.exists.return_value = True
        mock_client.bucket.return_value = mock_bucket
        mock_bucket.blob.return_value = mock_blob
        mock_blob.upload_from_file.side_effect = Exception("Upload failed")

        with patch(
            "airspan_acp_metrics.managers.gcs_operations_manager.gcs.Client",
            return_value=mock_client,
        ):
            file_metadata, gcs_path = await gcs_ops_manager.upload_file_to_gcs(
                file_data=sample_zip_file_data,
                instance_pyd_model=sample_instance_pyd_model,
                metric_config=sample_metric_config,
                retry_attempts=1,
                successful_attempts=1,
                remote_file_metadata=remote_file_metadata,
            )

            # Should return None, None on upload failure
            assert file_metadata is None
            assert gcs_path == "Upload failed"

    @pytest.mark.asyncio
    async def test_upload_file_to_gcs_file_count_calculation_error(
        self,
        gcs_ops_manager,
        sample_instance_pyd_model,
        sample_metric_config,
        remote_file_metadata,
    ):
        """Test upload_file_to_gcs when file size calculation fails."""

        # Create a mock file that raises an exception when seeking
        mock_file_data = MagicMock()
        mock_file_data.seek.side_effect = Exception("Seek failed")

        mock_client = MagicMock()
        mock_bucket = MagicMock()
        mock_blob = MagicMock()
        mock_bucket.exists.return_value = True
        mock_client.bucket.return_value = mock_bucket
        mock_bucket.blob.return_value = mock_blob

        with patch(
            "airspan_acp_metrics.managers.gcs_operations_manager.gcs.Client",
            return_value=mock_client,
        ):
            file_metadata, gcs_path = await gcs_ops_manager.upload_file_to_gcs(
                file_data=mock_file_data,
                instance_pyd_model=sample_instance_pyd_model,
                metric_config=sample_metric_config,
                retry_attempts=1,
                successful_attempts=1,
                remote_file_metadata=remote_file_metadata,
            )

            # Should return None, None on file size calculation failure
            assert file_metadata is None
            assert gcs_path == "Seek failed"

    @pytest.mark.asyncio
    async def test_upload_file_to_gcs_generic_exception(
        self,
        gcs_ops_manager,
        sample_instance_pyd_model,
        sample_metric_config,
        sample_zip_file_data,
        remote_file_metadata,
    ):
        """Test upload_file_to_gcs when a generic exception occurs."""

        with patch(
            "airspan_acp_metrics.managers.gcs_operations_manager.gcs.Client",
            side_effect=Exception("GCS error"),
        ):
            file_metadata, gcs_path = await gcs_ops_manager.upload_file_to_gcs(
                file_data=sample_zip_file_data,
                instance_pyd_model=sample_instance_pyd_model,
                metric_config=sample_metric_config,
                retry_attempts=1,
                successful_attempts=1,
                remote_file_metadata=remote_file_metadata,
            )

            # Should return None, None on generic exception
            assert file_metadata is None
            assert gcs_path == "GCS error"

    # INTEGRATION TESTS
    @pytest.mark.asyncio
    async def test_metrics_collector_integration_gcs_upload_failure(
        self,
        metrics_collector,
        sample_instance_pyd_model,
        sample_metric_config,
        sample_zip_file_data,
        remote_file_metadata,
        test_async_session,
    ):
        """Test how GCS upload failures propagate through the metrics collector flow."""
        interval_start = datetime.datetime(2025, 1, 1, 10, 0, 0, tzinfo=datetime.UTC)
        interval_end = datetime.datetime(2025, 1, 1, 10, 5, 0, tzinfo=datetime.UTC)

        # Mock successful SSH connection and file download
        mock_connection = AsyncMock()
        mock_ssh_response = (mock_connection, {"status": Status.OK})

        with (
            patch.object(
                metrics_collector.ssh_manager, "get_connection", return_value=mock_ssh_response
            ),
            patch.object(
                metrics_collector.file_ops,
                "download_file_from_remote",
                return_value=(sample_zip_file_data, "", remote_file_metadata),
            ),
            # Mock GCS upload failure
            patch.object(
                metrics_collector.gcs_ops, "upload_file_to_gcs", return_value=(None, None)
            ) as mock_upload,
            patch.object(metrics_collector.ssh_manager, "close_connection"),
        ):
            metrics_audit_db_model = await MetricsAuditDbFactory(
                async_session=test_async_session,
                manager_instance="test-instance-1",
                metric_name="Stats",
                interval_start=interval_start,
                interval_end=interval_end,
                collection_status=MetricsCollectionStatus.NOT_STARTED,
                reason="",
                last_attempt=None,
            )
            await test_async_session.commit()
            await test_async_session.refresh(metrics_audit_db_model)
            audit_id = metrics_audit_db_model.id

            # Should complete without raising exceptions
            await metrics_collector.collect_instance_metrics_task(
                instance_pyd_model=sample_instance_pyd_model,
                connection=mock_connection,
                metric_config=sample_metric_config,
                metrics_audit_db_model=metrics_audit_db_model,
                interval_start=interval_start,
                interval_end=interval_end,
            )

            # Verify GCS upload was attempted
            mock_upload.assert_called_once()

            # Verify audit was logged with failure status
            audit_service = MetricsAuditDbService(test_async_session)
            metric_name = sample_metric_config["metric_name"]
            audits = await audit_service.get_instance_metrics_audits(
                metric_name=metric_name,
                manager_instance="test-instance-1",
                interval_start=interval_start,
                interval_end=interval_end,
            )
            assert len(audits) == 1
            audit = audits[0]
            await test_async_session.refresh(audit)
            assert audit.id == audit_id
            assert audit.collection_status == MetricsCollectionStatus.FAILED
            assert "GCS upload failed" in audit.reason

    @pytest.mark.asyncio
    async def test_metrics_collector_integration_gcs_operations_exception(
        self,
        metrics_collector,
        sample_instance_pyd_model,
        sample_metric_config,
        sample_zip_file_data,
        remote_file_metadata,
        test_async_session,
    ):
        """Test metrics collector behavior when GCS operations raise unexpected exceptions."""
        interval_start = datetime.datetime(2025, 1, 1, 10, 0, 0, tzinfo=datetime.UTC)
        interval_end = datetime.datetime(2025, 1, 1, 10, 5, 0, tzinfo=datetime.UTC)

        # Mock successful SSH connection and file download
        mock_connection = AsyncMock()
        mock_ssh_response = (mock_connection, {"status": Status.OK})

        with (
            patch.object(
                metrics_collector.ssh_manager, "get_connection", return_value=mock_ssh_response
            ),
            patch.object(
                metrics_collector.file_ops,
                "download_file_from_remote",
                return_value=(sample_zip_file_data, "", remote_file_metadata),
            ),
            # Mock GCS operations to raise an exception
            patch.object(
                metrics_collector.gcs_ops,
                "upload_file_to_gcs",
                side_effect=Exception("403 Insufficient permissions"),
            ),
            patch.object(metrics_collector.ssh_manager, "close_connection"),
        ):
            metrics_audit_db_model = await MetricsAuditDbFactory(
                async_session=test_async_session,
                manager_instance="test-instance-1",
                metric_name="Stats",
                interval_start=interval_start,
                interval_end=interval_end,
                collection_status=MetricsCollectionStatus.NOT_STARTED,
                reason="",
                last_attempt=None,
            )
            await test_async_session.commit()
            await test_async_session.refresh(metrics_audit_db_model)
            audit_id = metrics_audit_db_model.id

            # Should complete without raising exceptions
            await metrics_collector.collect_instance_metrics_task(
                instance_pyd_model=sample_instance_pyd_model,
                connection=mock_connection,
                metric_config=sample_metric_config,
                metrics_audit_db_model=metrics_audit_db_model,
                interval_start=interval_start,
                interval_end=interval_end,
            )

            # Verify audit was logged with failure status
            audit_service = MetricsAuditDbService(test_async_session)
            metric_name = sample_metric_config["metric_name"]
            audits = await audit_service.get_instance_metrics_audits(
                metric_name=metric_name,
                manager_instance="test-instance-1",
                interval_start=interval_start,
                interval_end=interval_end,
            )
            assert len(audits) >= 1
            audit = audits[0]
            await test_async_session.refresh(audit)
            assert audit.id == audit_id
            assert audit.collection_status == MetricsCollectionStatus.FAILED
            assert "403 Insufficient permissions" in audit.reason
