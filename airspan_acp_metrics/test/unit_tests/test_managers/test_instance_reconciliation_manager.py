import json
from pathlib import Path
from unittest.mock import AsyncMock
from unittest.mock import MagicMock
from unittest.mock import patch

import pytest
from da_common.models import Status
from httpx import HTTPStatusError
from httpx import RequestError

from airspan_acp_metrics.database.services.instance_db_service import InstanceDbService
from airspan_acp_metrics.managers.instance_reconciliation_manager import (
    InstanceReconciliationManager,
)
from airspan_acp_metrics.pyd_models.alarm_enums import AlarmType
from airspan_acp_metrics.pyd_models.instance_pyd_model import InstancePydModel
from airspan_acp_metrics.test.factories.instance_db_factory import InstanceDbFactory


class TestInstanceReconciliationManager:
    """Tests for the InstanceReconciliationManager class including both success and exception scenarios."""

    @pytest.fixture
    def test_config(self, test_config):
        """Create a test configuration."""
        test_config.build_clients = MagicMock()
        test_config.build_clients.return_value = {
            "inventory-manager": AsyncMock(),
            "nms-orchestator": AsyncMock(),
            "airspan-acp-agent": AsyncMock(),
        }
        return test_config

    @pytest.fixture
    def instance_reconciliation_manager(self, test_config, test_async_session_maker):
        """Create an InstanceReconciliationManager instance."""
        return InstanceReconciliationManager(test_config, test_async_session_maker)

    @pytest.fixture
    def sample_airspan_acp_response(self):
        """Create sample Airspan ACP Agent API response."""
        test_data_path = (
            Path(__file__).parent.parent.parent / "test_data" / "airspan_acp_instances.json"
        )
        with open(test_data_path) as f:
            return json.load(f)

    @pytest.fixture
    def sample_inventory_response(self):
        """Create sample inventory API response."""
        test_data_path = (
            Path(__file__).parent.parent.parent / "test_data" / "inventory_components.json"
        )
        with open(test_data_path) as f:
            return json.load(f)

    @pytest.fixture
    def sample_success_orchestrator_response(self):
        """Create sample orchestrator API response."""
        test_data_path = (
            Path(__file__).parent.parent.parent / "test_data" / "orchestrator_nodes.json"
        )
        with open(test_data_path) as f:
            data = json.load(f)
            return data["GB-DEV2-VM-0002"]

    @pytest.fixture
    def sample_failed_orchestrator_response(self):
        """Create sample orchestrator API response."""
        test_data_path = (
            Path(__file__).parent.parent.parent / "test_data" / "orchestrator_nodes.json"
        )
        with open(test_data_path) as f:
            data = json.load(f)
            return data["ha-secondary-from-golden-ha-acp-snapshot-of-ha-primary"]

    # SUCCESS TESTS
    @pytest.mark.asyncio
    async def test_reconcile_instances_success(
        self,
        instance_reconciliation_manager,
        sample_airspan_acp_response,
        sample_inventory_response,
        sample_success_orchestrator_response,
    ):
        """Test successful instance reconciliation."""
        mock_airspan_client = AsyncMock()
        mock_inventory_client = AsyncMock()
        mock_orchestrator_client = AsyncMock()

        # Mock successful Airspan ACP Agent response
        mock_airspan_response = MagicMock()
        mock_airspan_response.json.return_value = sample_airspan_acp_response
        mock_airspan_response.raise_for_status.return_value = None
        mock_airspan_client.get.return_value = mock_airspan_response

        # Mock successful inventory response
        mock_inventory_response = MagicMock()
        mock_inventory_response.json.return_value = sample_inventory_response
        mock_inventory_response.raise_for_status.return_value = None
        mock_inventory_client.get.return_value = mock_inventory_response

        # Mock successful orchestrator response
        mock_orchestrator_response = MagicMock()
        mock_orchestrator_response.json.return_value = sample_success_orchestrator_response
        mock_orchestrator_response.raise_for_status.return_value = None
        mock_orchestrator_client.get.return_value = mock_orchestrator_response

        with (
            patch.object(
                instance_reconciliation_manager,
                "_get_airspan_acp_agent_client",
                return_value=mock_airspan_client,
            ),
            patch.object(
                instance_reconciliation_manager,
                "_get_inventory_client",
                return_value=mock_inventory_client,
            ),
            patch.object(
                instance_reconciliation_manager,
                "_get_orchestrator_client",
                return_value=mock_orchestrator_client,
            ),
        ):
            await instance_reconciliation_manager.reconcile_airspan_acp_agent_instances()

            # Verify API calls were made
            mock_airspan_client.get.assert_called_once()
            mock_inventory_client.get.assert_called_once()

    @pytest.mark.asyncio
    async def test_reconcile_instances_no_new_instances(
        self,
        instance_reconciliation_manager,
        sample_airspan_acp_response,
        test_async_session_maker,
    ):
        """Test reconciliation when no new instances need to be created."""
        mock_airspan_client = AsyncMock()

        # Mock successful Airspan ACP Agent response
        mock_airspan_response = MagicMock()
        mock_airspan_response.json.return_value = sample_airspan_acp_response
        mock_airspan_response.raise_for_status.return_value = None
        mock_airspan_client.get.return_value = mock_airspan_response

        # Create existing instances in database
        async with test_async_session_maker() as session:
            instance_service = InstanceDbService(session)

            for acp_instance in sample_airspan_acp_response:
                instance_pyd = InstancePydModel(
                    manager_instance=acp_instance["manager_instance"],
                    url=acp_instance["url"],
                    host_ip_address="*************",
                    host_secret_id="test-secret",
                    metrics_collection_status=Status.OK,
                )
                await instance_service.create_acp_instance(instance_pyd)

            await session.commit()

        with patch.object(
            instance_reconciliation_manager,
            "_get_airspan_acp_agent_client",
            return_value=mock_airspan_client,
        ):
            await instance_reconciliation_manager.reconcile_airspan_acp_agent_instances()

            # Verify API call was made
            mock_airspan_client.get.assert_called_once()

    # EXCEPTION TESTS
    @pytest.mark.asyncio
    async def test_reconcile_instances_airspan_client_failure(
        self, instance_reconciliation_manager
    ):
        """Test reconcile_instances when Airspan ACP Agent client fails."""
        mock_airspan_client = AsyncMock()
        mock_airspan_client.get.side_effect = RequestError("Connection failed")

        with patch.object(
            instance_reconciliation_manager,
            "_get_airspan_acp_agent_client",
            return_value=mock_airspan_client,
        ):
            # Should not raise exception, should handle gracefully
            await instance_reconciliation_manager.reconcile_airspan_acp_agent_instances()

    @pytest.mark.asyncio
    async def test_reconcile_instances_airspan_http_error(
        self, instance_reconciliation_manager
    ):
        """Test reconcile_instances when Airspan ACP Agent API returns HTTP error."""
        mock_airspan_client = AsyncMock()
        mock_response = MagicMock()
        mock_response.raise_for_status.side_effect = HTTPStatusError(
            "404 Not Found", request=MagicMock(), response=MagicMock()
        )
        mock_airspan_client.get.return_value = mock_response

        with patch.object(
            instance_reconciliation_manager,
            "_get_airspan_acp_agent_client",
            return_value=mock_airspan_client,
        ):
            # Should not raise exception, should handle gracefully
            await instance_reconciliation_manager.reconcile_airspan_acp_agent_instances()

    @pytest.mark.asyncio
    async def test_reconcile_instances_inventory_client_failure(
        self, instance_reconciliation_manager, sample_airspan_acp_response
    ):
        """Test reconcile_instances when inventory client fails."""
        mock_airspan_client = AsyncMock()
        mock_inventory_client = AsyncMock()

        # Mock successful Airspan response
        mock_airspan_response = MagicMock()
        mock_airspan_response.json.return_value = sample_airspan_acp_response
        mock_airspan_response.raise_for_status.return_value = None
        mock_airspan_client.get.return_value = mock_airspan_response

        # Mock inventory client failure
        mock_inventory_client.get.side_effect = RequestError("Connection failed")

        with (
            patch.object(
                instance_reconciliation_manager,
                "_get_airspan_acp_agent_client",
                return_value=mock_airspan_client,
            ),
            patch.object(
                instance_reconciliation_manager,
                "_get_inventory_client",
                return_value=mock_inventory_client,
            ),
        ):
            # Should not raise exception, should handle gracefully
            await instance_reconciliation_manager.reconcile_airspan_acp_agent_instances()

    @pytest.mark.asyncio
    async def test_reconcile_instances_inventory_http_error(
        self, instance_reconciliation_manager, sample_airspan_acp_response
    ):
        """Test reconcile_instances when inventory API returns HTTP error."""
        mock_airspan_client = AsyncMock()
        mock_inventory_client = AsyncMock()

        # Mock successful Airspan response
        mock_airspan_response = MagicMock()
        mock_airspan_response.json.return_value = sample_airspan_acp_response
        mock_airspan_response.raise_for_status.return_value = None
        mock_airspan_client.get.return_value = mock_airspan_response

        # Mock inventory HTTP error
        mock_inventory_response = MagicMock()
        mock_inventory_response.raise_for_status.side_effect = HTTPStatusError(
            "500 Internal Server Error", request=MagicMock(), response=MagicMock()
        )
        mock_inventory_client.get.return_value = mock_inventory_response

        with (
            patch.object(
                instance_reconciliation_manager,
                "_get_airspan_acp_agent_client",
                return_value=mock_airspan_client,
            ),
            patch.object(
                instance_reconciliation_manager,
                "_get_inventory_client",
                return_value=mock_inventory_client,
            ),
        ):
            # Should not raise exception, should handle gracefully
            await instance_reconciliation_manager.reconcile_airspan_acp_agent_instances()

    @pytest.mark.asyncio
    async def test_reconcile_instances_orchestrator_client_failure(
        self,
        instance_reconciliation_manager,
        sample_airspan_acp_response,
        sample_inventory_response,
    ):
        """Test reconcile_instances when orchestrator client fails."""
        mock_airspan_client = AsyncMock()
        mock_inventory_client = AsyncMock()
        mock_orchestrator_client = AsyncMock()

        # Mock successful responses
        mock_airspan_response = MagicMock()
        mock_airspan_response.json.return_value = sample_airspan_acp_response
        mock_airspan_response.raise_for_status.return_value = None
        mock_airspan_client.get.return_value = mock_airspan_response

        mock_inventory_response = MagicMock()
        mock_inventory_response.json.return_value = sample_inventory_response
        mock_inventory_response.raise_for_status.return_value = None
        mock_inventory_client.get.return_value = mock_inventory_response

        # Mock orchestrator client failure
        mock_orchestrator_client.get.side_effect = RequestError("Connection failed")

        with (
            patch.object(
                instance_reconciliation_manager,
                "_get_airspan_acp_agent_client",
                return_value=mock_airspan_client,
            ),
            patch.object(
                instance_reconciliation_manager,
                "_get_inventory_client",
                return_value=mock_inventory_client,
            ),
            patch.object(
                instance_reconciliation_manager,
                "_get_orchestrator_client",
                return_value=mock_orchestrator_client,
            ),
        ):
            # Should not raise exception, should handle gracefully
            await instance_reconciliation_manager.reconcile_airspan_acp_agent_instances()

    @pytest.mark.asyncio
    async def test_reconcile_instances_database_operation_failure(
        self, instance_reconciliation_manager, sample_airspan_acp_response
    ):
        """Test reconcile_instances when database operations fail."""
        mock_airspan_client = AsyncMock()

        # Mock successful Airspan response
        mock_airspan_response = MagicMock()
        mock_airspan_response.json.return_value = sample_airspan_acp_response
        mock_airspan_response.raise_for_status.return_value = None
        mock_airspan_client.get.return_value = mock_airspan_response

        # Mock database operation failure
        with (
            patch.object(
                instance_reconciliation_manager,
                "_get_airspan_acp_agent_client",
                return_value=mock_airspan_client,
            ),
            patch(
                "airspan_acp_metrics.managers.instance_reconciliation_manager.get_db_session"
            ) as mock_get_session,
        ):
            mock_get_session.side_effect = Exception("Database connection failed")

            # Should not raise exception, should handle gracefully
            await instance_reconciliation_manager.reconcile_airspan_acp_agent_instances()

    @pytest.mark.asyncio
    async def test_reconcile_instances_empty_api_responses(
        self, instance_reconciliation_manager
    ):
        """Test reconcile_instances when API responses are empty."""
        mock_airspan_client = AsyncMock()

        # Mock empty Airspan response
        mock_airspan_response = MagicMock()
        mock_airspan_response.json.return_value = []
        mock_airspan_response.raise_for_status.return_value = None
        mock_airspan_client.get.return_value = mock_airspan_response

        with patch.object(
            instance_reconciliation_manager,
            "_get_airspan_acp_agent_client",
            return_value=mock_airspan_client,
        ):
            # Should not raise exception, should handle gracefully
            await instance_reconciliation_manager.reconcile_airspan_acp_agent_instances()

    @pytest.mark.asyncio
    async def test_reconcile_instances_malformed_api_responses(
        self, instance_reconciliation_manager
    ):
        """Test reconcile_instances when API responses have unexpected structure."""
        mock_airspan_client = AsyncMock()

        # Mock malformed Airspan response (missing required fields)
        mock_airspan_response = MagicMock()
        mock_airspan_response.json.return_value = [{"invalid": "data"}]  # Wrong structure
        mock_airspan_response.raise_for_status.return_value = None
        mock_airspan_client.get.return_value = mock_airspan_response

        with patch.object(
            instance_reconciliation_manager,
            "_get_airspan_acp_agent_client",
            return_value=mock_airspan_client,
        ):
            # Should not raise exception, should handle gracefully
            await instance_reconciliation_manager.reconcile_airspan_acp_agent_instances()

    # FETCH COMPONENTS FROM INVENTORY TESTS
    @pytest.mark.asyncio
    async def test_fetch_components_from_inventory_success(
        self, instance_reconciliation_manager, sample_inventory_response
    ):
        """Test successful fetching of components from inventory using real data."""
        mock_inventory_client = AsyncMock()

        mock_inventory_response = MagicMock()
        mock_inventory_response.json.return_value = sample_inventory_response
        mock_inventory_response.raise_for_status.return_value = None
        mock_inventory_client.get.return_value = mock_inventory_response

        with patch.object(
            instance_reconciliation_manager,
            "_get_inventory_client",
            return_value=mock_inventory_client,
        ):
            result = await instance_reconciliation_manager._fetch_components_from_inventory()

            assert result == sample_inventory_response

    @pytest.mark.asyncio
    async def test_fetch_components_from_inventory_http_error(
        self, instance_reconciliation_manager
    ):
        """Test handling of HTTP errors when fetching components from inventory."""
        with (
            patch.object(
                instance_reconciliation_manager, "_get_inventory_client"
            ) as mock_client,
            patch.object(
                instance_reconciliation_manager,
                "_raise_service_connectivity_alarm",
                new_callable=AsyncMock,
            ),
        ):
            mock_response = MagicMock()
            mock_response.raise_for_status.side_effect = Exception("HTTP 500 Error")
            mock_client.return_value.get.return_value = mock_response

            result = await instance_reconciliation_manager._fetch_components_from_inventory()

            assert result == []

    @pytest.mark.asyncio
    async def test_fetch_components_from_inventory_network_error(
        self, instance_reconciliation_manager
    ):
        """Test handling of network errors when fetching components from inventory."""
        with (
            patch.object(
                instance_reconciliation_manager, "_get_inventory_client"
            ) as mock_client,
            patch.object(
                instance_reconciliation_manager,
                "_raise_service_connectivity_alarm",
                new_callable=AsyncMock,
            ),
        ):
            mock_client.return_value.get.side_effect = Exception("Network error")

            result = await instance_reconciliation_manager._fetch_components_from_inventory()

            assert result == []

    # EXTRACT INSTANCE NODE MAPPING TESTS
    def test_extract_instance_node_mapping_success(
        self, instance_reconciliation_manager, sample_inventory_response
    ):
        """Test successful extraction of instance node mapping using real data."""
        result = instance_reconciliation_manager._extract_instance_node_mappings(
            sample_inventory_response
        )

        expected = {
            "acp-dauk-mrl-green-acp": "GB-DEV2-VM-0002",
            "ha-secondary-from-golden-ha-acp-snapshot-of-ha-primary": "GB-DEV2-VM-0001",
        }
        assert result == expected

    def test_extract_instance_node_mapping_empty_components(
        self, instance_reconciliation_manager
    ):
        """Test extraction with empty components list."""
        result = instance_reconciliation_manager._extract_instance_node_mappings([])
        assert result == {}

    def test_extract_instance_node_mapping_non_acp_components(
        self, instance_reconciliation_manager, sample_inventory_response
    ):
        """Test extraction filters out non-ACP components using real data."""
        # Use the real data which includes AIRSPAN components

        result = instance_reconciliation_manager._extract_instance_node_mappings(
            sample_inventory_response
        )

        # Should only include ACP components, not AIRSPAN ones
        expected = {
            "acp-dauk-mrl-green-acp": "GB-DEV2-VM-0002",
            "ha-secondary-from-golden-ha-acp-snapshot-of-ha-primary": "GB-DEV2-VM-0001",
        }
        assert result == expected

    # FETCH SERVER ID FOR NODE TESTS
    @pytest.mark.asyncio
    async def test_fetch_server_id_for_node_success(
        self, instance_reconciliation_manager, sample_success_orchestrator_response
    ):
        """Test successful fetching of server ID for node using real success data."""
        mock_orchestrator_client = AsyncMock()

        # Mock successful orchestrator response
        mock_orchestrator_response = MagicMock()
        mock_orchestrator_response.json.return_value = sample_success_orchestrator_response
        mock_orchestrator_response.raise_for_status.return_value = None
        mock_orchestrator_client.get.return_value = mock_orchestrator_response

        with patch.object(
            instance_reconciliation_manager,
            "_get_orchestrator_client",
            return_value=mock_orchestrator_client,
        ):
            (
                server_details,
                error_info,
            ) = await instance_reconciliation_manager._fetch_server_id_for_node(
                "GB-DEV2-VM-0002"
            )

            expected_server_details = {
                "host_secret_id": "GP081D3-dauk-mrl-green-acp",
                "host_ip_address": "*************",
            }
            expected_error_info = {}

            assert server_details == expected_server_details
            assert error_info == expected_error_info
            assert (
                mock_orchestrator_client.get.call_args[0][0] == "/server/nodes/GB-DEV2-VM-0002"
            )

    @pytest.mark.asyncio
    async def test_fetch_server_id_for_node_failed_response(
        self, instance_reconciliation_manager, sample_failed_orchestrator_response
    ):
        """Test handling of failed orchestrator response using real failure data."""
        mock_orchestrator_client = AsyncMock()

        # Mock failed orchestrator response
        mock_orchestrator_response = MagicMock()
        mock_orchestrator_response.json.return_value = sample_failed_orchestrator_response
        mock_orchestrator_response.raise_for_status.side_effect = HTTPStatusError(
            "404 Not Found", request=MagicMock(), response=MagicMock()
        )
        mock_orchestrator_client.get.return_value = mock_orchestrator_response

        with (
            patch.object(
                instance_reconciliation_manager,
                "_get_orchestrator_client",
                return_value=mock_orchestrator_client,
            ),
            patch.object(
                instance_reconciliation_manager,
                "_raise_service_connectivity_alarm",
                new_callable=AsyncMock,
            ),
        ):
            (
                server_details,
                error_info,
            ) = await instance_reconciliation_manager._fetch_server_id_for_node(
                "ha-secondary-from-golden-ha-acp-snapshot-of-ha-primary"
            )

            expected_error_info = {
                "reason": "Error fetching server ID for node ha-secondary-from-golden-ha-acp-snapshot-of-ha-primary from orchestrator: 404 Not Found",
            }

            assert server_details == {}
            assert error_info == expected_error_info

    # IDENTIFY MISSING INSTANCES TESTS
    @pytest.mark.asyncio
    async def test_identify_missing_instances_with_real_data(
        self, instance_reconciliation_manager, test_async_session, sample_airspan_acp_response
    ):
        """Test identification of missing instances using real ACP data."""
        # Create one existing instance
        existing_instance = await InstanceDbFactory(
            async_session=test_async_session,
            manager_instance="acp-dauk-mrl-green-acp",
            url="https://*************/api/20.5",
        )
        await test_async_session.commit()
        await test_async_session.refresh(existing_instance)

        missing_instances = instance_reconciliation_manager._identify_missing_instances(
            sample_airspan_acp_response, [existing_instance]
        )

        # Should find all instances except the existing one
        expected_count = len(sample_airspan_acp_response) - 1
        assert len(missing_instances) == expected_count

        # Verify the existing instance is not in missing list
        missing_names = [instance["manager_instance"] for instance in missing_instances]
        assert "acp-dauk-mrl-green-acp" not in missing_names

    @pytest.mark.asyncio
    async def test_identify_missing_instances_all_missing(
        self, instance_reconciliation_manager, sample_airspan_acp_response
    ):
        """Test identification when all instances are missing."""
        missing_instances = instance_reconciliation_manager._identify_missing_instances(
            sample_airspan_acp_response, []
        )

        assert len(missing_instances) == len(sample_airspan_acp_response)

    @pytest.mark.asyncio
    async def test_identify_missing_instances_none_missing(
        self, instance_reconciliation_manager, test_async_session, sample_airspan_acp_response
    ):
        """Test identification when no instances are missing."""
        # Create all instances from the sample data
        existing_instances = []
        for acp_instance in sample_airspan_acp_response:
            instance = await InstanceDbFactory(
                async_session=test_async_session,
                manager_instance=acp_instance["manager_instance"],
                url=acp_instance["url"],
            )
            existing_instances.append(instance)

        await test_async_session.commit()
        for instance in existing_instances:
            await test_async_session.refresh(instance)

        missing_instances = instance_reconciliation_manager._identify_missing_instances(
            sample_airspan_acp_response, existing_instances
        )

        assert len(missing_instances) == 0

    # IDENTIFY EXTRA INSTANCES TESTS
    @pytest.mark.asyncio
    async def test_identify_extra_instances_with_real_data(
        self, instance_reconciliation_manager, test_async_session, sample_airspan_acp_response
    ):
        """Test identification of extra instances using real data."""
        # Create instances including one that's not in the ACP response
        existing_instances = []
        for acp_instance in sample_airspan_acp_response[:2]:  # Only first 2
            instance = await InstanceDbFactory(
                async_session=test_async_session,
                manager_instance=acp_instance["manager_instance"],
                url=acp_instance["url"],
            )
            existing_instances.append(instance)

        # Add an extra instance not in ACP response
        extra_instance = await InstanceDbFactory(
            async_session=test_async_session,
            manager_instance="extra-instance-not-in-acp",
            url="http://extra-url",
        )
        existing_instances.append(extra_instance)

        await test_async_session.commit()
        for instance in existing_instances:
            await test_async_session.refresh(instance)

        extra_instances = instance_reconciliation_manager._identify_extra_instances(
            sample_airspan_acp_response, existing_instances
        )

        assert len(extra_instances) == 1
        assert extra_instances[0].manager_instance == "extra-instance-not-in-acp"

    # IDENTIFY INSTANCES NEEDING UPDATE TESTS
    @pytest.mark.asyncio
    async def test_identify_instances_needing_update_url_change(
        self, instance_reconciliation_manager, test_async_session, sample_airspan_acp_response
    ):
        """Test identification of instances needing URL updates."""
        # Create instance with different URL
        instance = await InstanceDbFactory(
            async_session=test_async_session,
            manager_instance=sample_airspan_acp_response[0]["manager_instance"],
            url="http://old-url",  # Different from sample data
        )
        await test_async_session.commit()
        await test_async_session.refresh(instance)

        # Mock server mappings
        server_mappings = {
            sample_airspan_acp_response[0]["manager_instance"]: (
                {"host_secret_id": "test-secret", "host_ip_address": "***********"},
                {},
            )
        }

        instances_to_update = (
            instance_reconciliation_manager._identify_instances_needing_update(
                sample_airspan_acp_response, [instance], server_mappings
            )
        )

        assert len(instances_to_update) == 1
        assert (
            instances_to_update[0][1].manager_instance
            == sample_airspan_acp_response[0]["manager_instance"]
        )

    # CREATE MISSING INSTANCES TESTS
    @pytest.mark.asyncio
    async def test_create_missing_instances_success_with_server_mapping(
        self, instance_reconciliation_manager, test_async_session, sample_airspan_acp_response
    ):
        """Test successful creation of missing instances with server mapping."""

        # Use first instance from real data
        missing_instances = [sample_airspan_acp_response[0]]
        manager_instance = sample_airspan_acp_response[0]["manager_instance"]
        host_secret_id = "GP081D3-dauk-mrl-green-acp"
        host_ip_address = "*************"
        server_mappings = {
            manager_instance: (
                {
                    "host_secret_id": host_secret_id,
                    "host_ip_address": host_ip_address,
                },
                {"status": None, "reason": None},
            )
        }
        instance_db_service = InstanceDbService(test_async_session)
        await instance_reconciliation_manager._create_missing_instances(
            missing_instances, instance_db_service, server_mappings
        )

        instance_db_model = await instance_db_service.get_acp_instance_by_manager_instance(
            manager_instance
        )
        assert instance_db_model is not None
        assert instance_db_model.reason == "Ready for metrics collection"
        assert instance_db_model.metrics_collection_status == Status.OK
        assert instance_db_model.host_secret_id == host_secret_id
        assert instance_db_model.host_ip_address == host_ip_address

    @pytest.mark.asyncio
    async def test_create_missing_instances_with_error_mapping(
        self, instance_reconciliation_manager, test_async_session, sample_airspan_acp_response
    ):
        """Test creation of missing instances with error in server mapping."""

        missing_instances = [sample_airspan_acp_response[0]]
        server_mappings = {
            sample_airspan_acp_response[0]["manager_instance"]: (
                {"host_secret_id": None, "host_ip_address": None},
                {
                    "status": Status.ERROR,
                    "reason": "missing from inventory or nms-orchestrator",
                },
            )
        }

        instance_db_service = InstanceDbService(test_async_session)
        await instance_reconciliation_manager._create_missing_instances(
            missing_instances, instance_db_service, server_mappings
        )

    # UPDATE EXISTING INSTANCES TESTS
    @pytest.mark.asyncio
    async def test_update_existing_instances_success(
        self, instance_reconciliation_manager, test_async_session, sample_airspan_acp_response
    ):
        """Test successful update of existing instances."""

        # Create instance that needs updating
        instance = await InstanceDbFactory(
            async_session=test_async_session,
            manager_instance=sample_airspan_acp_response[0]["manager_instance"],
            url="http://old-url",
        )
        await test_async_session.commit()
        await test_async_session.refresh(instance)

        instance_db_service = InstanceDbService(test_async_session)
        new_host_secret_id = "new-secret"
        new_host_ip_address = "*************"
        new_reason = {}  # Empty reason dict as expected by the method
        instances_needing_update = [
            (
                sample_airspan_acp_response[0],
                instance,
                new_host_secret_id,
                new_host_ip_address,
                new_reason,
            )
        ]
        await instance_reconciliation_manager._update_existing_instances(
            instances_needing_update, instance_db_service
        )
        await test_async_session.refresh(instance)
        assert instance.host_ip_address == new_host_ip_address
        assert instance.host_secret_id == new_host_secret_id
        assert instance.metrics_collection_status == Status.OK
        assert instance.reason == "Ready for metrics collection"

    # DELETE EXTRA INSTANCES TESTS
    @pytest.mark.asyncio
    async def test_delete_extra_instances_success(
        self, instance_reconciliation_manager, test_async_session
    ):
        """Test successful deletion of extra instances."""

        # Create instance to delete
        instance = await InstanceDbFactory(
            async_session=test_async_session,
            manager_instance="instance-to-delete",
            url="http://delete-me",
        )
        await test_async_session.commit()
        await test_async_session.refresh(instance)

        extra_instances = [instance]

        instance_db_service = InstanceDbService(test_async_session)
        await instance_reconciliation_manager._delete_extra_instances(
            extra_instances, instance_db_service
        )

        instance_db_model = await instance_db_service.get_acp_instance_by_manager_instance(
            "instance-to-delete"
        )
        assert instance_db_model is None

    # INTEGRATION TESTS WITH REAL DATA
    @pytest.mark.asyncio
    async def test_full_reconciliation_cycle_with_real_data(
        self,
        instance_reconciliation_manager,
        test_async_session,
        sample_airspan_acp_response,
        sample_inventory_response,
        sample_success_orchestrator_response,
    ):
        """Test complete reconciliation cycle using real test data."""
        with (
            patch.object(
                instance_reconciliation_manager, "_get_airspan_acp_agent_client"
            ) as mock_acp_client,
            patch.object(
                instance_reconciliation_manager, "_get_inventory_client"
            ) as mock_inventory_client,
            patch.object(
                instance_reconciliation_manager, "_get_orchestrator_client"
            ) as mock_orchestrator_client,
        ):
            # Mock ACP agent response
            mock_acp_response = MagicMock()
            mock_acp_response.json.return_value = sample_airspan_acp_response
            mock_acp_response.raise_for_status.return_value = None
            mock_acp_client.return_value.get.return_value = mock_acp_response

            # Mock inventory response
            mock_inventory_response = MagicMock()
            mock_inventory_response.json.return_value = sample_inventory_response
            mock_inventory_response.raise_for_status.return_value = None
            mock_inventory_client.return_value.get.return_value = mock_inventory_response

            # Mock orchestrator response - return success for first node, failure for second
            def orchestrator_side_effect(url):
                if "GB-DEV2-VM-0002" in url:
                    mock_response = MagicMock()
                    mock_response.json.return_value = sample_success_orchestrator_response
                    mock_response.raise_for_status.return_value = None
                    return mock_response
                else:
                    mock_response = MagicMock()
                    mock_response.raise_for_status.side_effect = Exception("404 Not Found")
                    return mock_response

            mock_orchestrator_client.return_value.get.side_effect = orchestrator_side_effect

            # Mock database operations
            await instance_reconciliation_manager.reconcile_airspan_acp_agent_instances()

    # GRANULAR MISSING DATA ALARM TESTS
    @pytest.mark.asyncio
    async def test_handle_component_missing_alarm(
        self, instance_reconciliation_manager, test_async_session_maker
    ):
        """Test component missing alarm handling."""
        async with test_async_session_maker() as session:
            with (
                patch.object(
                    instance_reconciliation_manager,
                    "alarm_topic",
                    "test-alarm-topic",
                ),
                patch(
                    "airspan_acp_metrics.managers.instance_reconciliation_manager.AlarmManager"
                ) as mock_alarm_manager_class,
            ):
                mock_alarm_manager = AsyncMock()
                mock_alarm_manager_class.return_value = mock_alarm_manager

                # Test component missing alarm
                await instance_reconciliation_manager._handle_component_missing_alarm(
                    session, "test-instance", "test-prefix"
                )

                # Should call raise_alarm with INVENTORY_DATA_MISSING
                mock_alarm_manager.raise_alarm.assert_called_once()
                call_args = mock_alarm_manager.raise_alarm.call_args
                assert call_args[1]["alarm_type"] == AlarmType.INVENTORY_DATA_MISSING
                assert "not found in inventory-manager" in call_args[1]["description"]

    @pytest.mark.asyncio
    async def test_handle_server_mapping_missing_alarm(
        self, instance_reconciliation_manager, test_async_session_maker
    ):
        """Test server mapping missing alarm handling."""
        async with test_async_session_maker() as session:
            with (
                patch.object(
                    instance_reconciliation_manager,
                    "alarm_topic",
                    "test-alarm-topic",
                ),
                patch(
                    "airspan_acp_metrics.managers.instance_reconciliation_manager.AlarmManager"
                ) as mock_alarm_manager_class,
            ):
                mock_alarm_manager = AsyncMock()
                mock_alarm_manager_class.return_value = mock_alarm_manager

                # Test server mapping missing alarm
                await instance_reconciliation_manager._handle_server_mapping_missing_alarm(
                    session, "test-instance", "test-node", "Node not found", "test-prefix"
                )

                # Should call raise_alarm with ORCHESTRATOR_DATA_MISSING
                mock_alarm_manager.raise_alarm.assert_called_once()
                call_args = mock_alarm_manager.raise_alarm.call_args
                assert call_args[1]["alarm_type"] == AlarmType.ORCHESTRATOR_DATA_MISSING
                assert "Server mapping missing" in call_args[1]["description"]
                assert "test-node" in call_args[1]["description"]

    @pytest.mark.asyncio
    async def test_handle_partial_server_data_alarm(
        self, instance_reconciliation_manager, test_async_session_maker
    ):
        """Test partial server data alarm handling."""
        async with test_async_session_maker() as session:
            with (
                patch.object(
                    instance_reconciliation_manager,
                    "alarm_topic",
                    "test-alarm-topic",
                ),
                patch(
                    "airspan_acp_metrics.managers.instance_reconciliation_manager.AlarmManager"
                ) as mock_alarm_manager_class,
            ):
                mock_alarm_manager = AsyncMock()
                mock_alarm_manager_class.return_value = mock_alarm_manager

                # Test partial server data alarm (missing IP address)
                server_details = {"host_secret_id": "test-secret"}
                await instance_reconciliation_manager._handle_partial_server_data_alarm(
                    session, "test-instance", "test-node", server_details, "test-prefix"
                )

                # Should call raise_alarm with ORCHESTRATOR_DATA_MISSING
                mock_alarm_manager.raise_alarm.assert_called_once()
                call_args = mock_alarm_manager.raise_alarm.call_args
                assert call_args[1]["alarm_type"] == AlarmType.ORCHESTRATOR_DATA_MISSING
                assert "Partial server data" in call_args[1]["description"]
                assert "host_ip_address" in call_args[1]["description"]

    @pytest.mark.asyncio
    async def test_handle_instance_data_alarms_component_missing(
        self, instance_reconciliation_manager, test_async_session_maker
    ):
        """Test instance data alarms for missing component scenario."""
        async with test_async_session_maker() as session:
            # Mock AlarmManager
            with patch(
                "airspan_acp_metrics.managers.instance_reconciliation_manager.AlarmManager"
            ) as mock_alarm_manager_class:
                mock_alarm_manager = AsyncMock()
                mock_alarm_manager_class.return_value = mock_alarm_manager

                # Test data: Instance exists in ACP agent but not in inventory
                external_instances = [{"manager_instance": "missing-component-instance"}]
                server_id_mappings = {}  # No mapping because component not found
                instance_node_mappings = {}  # No node mapping because component not found

                await instance_reconciliation_manager._handle_instance_data_alarms(
                    session,
                    external_instances,
                    server_id_mappings,
                    instance_node_mappings,
                    "test-prefix",
                )

                # Should call component missing alarm
                mock_alarm_manager.raise_alarm.assert_called_once()
                call_args = mock_alarm_manager.raise_alarm.call_args
                assert call_args[1]["alarm_type"] == AlarmType.INVENTORY_DATA_MISSING
                assert "not found in inventory-manager" in call_args[1]["description"]

    @pytest.mark.asyncio
    async def test_handle_instance_data_alarms_server_mapping_missing(
        self, instance_reconciliation_manager, test_async_session_maker
    ):
        """Test instance data alarms for missing server mapping scenario."""
        async with test_async_session_maker() as session:
            # Mock AlarmManager
            with patch(
                "airspan_acp_metrics.managers.instance_reconciliation_manager.AlarmManager"
            ) as mock_alarm_manager_class:
                mock_alarm_manager = AsyncMock()
                mock_alarm_manager_class.return_value = mock_alarm_manager

                # Test data: Component exists but no server mapping
                external_instances = [{"manager_instance": "component-exists-instance"}]
                server_id_mappings = {
                    "component-exists-instance": (
                        {},
                        {"reason": "Node test-node not found in orchestrator"},
                    )
                }
                instance_node_mappings = {"component-exists-instance": "test-node"}

                await instance_reconciliation_manager._handle_instance_data_alarms(
                    session,
                    external_instances,
                    server_id_mappings,
                    instance_node_mappings,
                    "test-prefix",
                )

                # Should call server mapping missing alarm
                mock_alarm_manager.raise_alarm.assert_called_once()
                call_args = mock_alarm_manager.raise_alarm.call_args
                assert call_args[1]["alarm_type"] == AlarmType.ORCHESTRATOR_DATA_MISSING
                assert "Server mapping missing" in call_args[1]["description"]

    @pytest.mark.asyncio
    async def test_handle_instance_data_alarms_partial_server_data(
        self, instance_reconciliation_manager, test_async_session_maker
    ):
        """Test instance data alarms for partial server data scenario."""
        async with test_async_session_maker() as session:
            # Mock AlarmManager
            with patch(
                "airspan_acp_metrics.managers.instance_reconciliation_manager.AlarmManager"
            ) as mock_alarm_manager_class:
                mock_alarm_manager = AsyncMock()
                mock_alarm_manager_class.return_value = mock_alarm_manager

                # Test data: Server exists but missing IP address
                external_instances = [{"manager_instance": "partial-server-instance"}]
                server_id_mappings = {
                    "partial-server-instance": ({"host_secret_id": "test-secret"}, {})
                }
                instance_node_mappings = {"partial-server-instance": "test-node"}

                await instance_reconciliation_manager._handle_instance_data_alarms(
                    session,
                    external_instances,
                    server_id_mappings,
                    instance_node_mappings,
                    "test-prefix",
                )

                # Should call partial server data alarm
                mock_alarm_manager.raise_alarm.assert_called_once()
                call_args = mock_alarm_manager.raise_alarm.call_args
                assert call_args[1]["alarm_type"] == AlarmType.ORCHESTRATOR_DATA_MISSING
                assert "Partial server data" in call_args[1]["description"]

    @pytest.mark.asyncio
    async def test_handle_instance_data_alarms_complete_data_clears_alarms(
        self, instance_reconciliation_manager, test_async_session_maker
    ):
        """Test instance data alarms clearing when complete data is available."""
        async with test_async_session_maker() as session:
            # Mock AlarmManager
            with patch(
                "airspan_acp_metrics.managers.instance_reconciliation_manager.AlarmManager"
            ) as mock_alarm_manager_class:
                mock_alarm_manager = AsyncMock()
                mock_alarm_manager_class.return_value = mock_alarm_manager

                # Test data: Complete server data available
                external_instances = [{"manager_instance": "complete-data-instance"}]
                server_id_mappings = {
                    "complete-data-instance": (
                        {"host_secret_id": "test-secret", "host_ip_address": "***********"},
                        {},
                    )
                }
                instance_node_mappings = {"complete-data-instance": "test-node"}

                await instance_reconciliation_manager._handle_instance_data_alarms(
                    session,
                    external_instances,
                    server_id_mappings,
                    instance_node_mappings,
                    "test-prefix",
                )

                # Should call clear_alarm twice (for both alarm types)
                assert mock_alarm_manager.clear_alarm.call_count == 2
                mock_alarm_manager.raise_alarm.assert_not_called()

    # COMPREHENSIVE RECONCILIATION SCENARIO TESTS
    @pytest.mark.asyncio
    async def test_reconcile_multiple_instances_needing_update(
        self, instance_reconciliation_manager, test_async_session_maker
    ):
        """Test reconciliation of multiple instances that need updates (URL, host_secret_id, host_ip_address changes)."""

        # Setup: Create external instances data (what comes from ACP agent)
        external_instances = [
            {
                "manager_instance": "acp-instance-update-1",
                "url": "https://************/api/20.5",  # New URL
            },
            {
                "manager_instance": "acp-instance-update-2",
                "url": "https://************/api/20.5",  # New URL
            },
            {
                "manager_instance": "acp-instance-update-3",
                "url": "https://************/api/20.5",  # New URL
            },
        ]

        # Setup: Create existing database instances with outdated information
        async with test_async_session_maker() as session:
            instance_service = InstanceDbService(session)

            # Create existing instances with old URLs and server info
            existing_instances = []
            for i, external_instance in enumerate(external_instances, 1):
                db_instance = await InstanceDbFactory(
                    async_session=session,
                    manager_instance=external_instance["manager_instance"],
                    url=f"https://old-url-{i}/api/20.5",  # Old URL
                    host_ip_address=f"192.168.0.{i}",  # Old IP
                    host_secret_id=f"old-secret-{i}",  # Old secret
                    metrics_collection_status=Status.OK,
                    reason="Ready for metrics collection",
                )
                existing_instances.append(db_instance)

            await session.commit()
            for instance in existing_instances:
                await session.refresh(instance)

        # Setup: Mock server mappings with new data
        server_mappings = {
            "acp-instance-update-1": (
                {"host_secret_id": "new-secret-1", "host_ip_address": "************"},
                {},
            ),
            "acp-instance-update-2": (
                {"host_secret_id": "new-secret-2", "host_ip_address": "************"},
                {},
            ),
            "acp-instance-update-3": (
                {"host_secret_id": "new-secret-3", "host_ip_address": "************"},
                {},
            ),
        }

        instance_node_mappings = {
            "acp-instance-update-1": "node-1",
            "acp-instance-update-2": "node-2",
            "acp-instance-update-3": "node-3",
        }

        # Mock external service calls
        with (
            patch.object(
                instance_reconciliation_manager,
                "_fetch_airspan_acp_instances",
                return_value=external_instances,
            ),
            patch.object(
                instance_reconciliation_manager,
                "get_instance_server_id_mappings_with_nodes",
                return_value=(server_mappings, instance_node_mappings),
            ),
            patch.object(
                instance_reconciliation_manager,
                "_handle_instance_data_alarms",
                new_callable=AsyncMock,
            ),
        ):
            # Execute the reconciliation
            await instance_reconciliation_manager.reconcile_airspan_acp_agent_instances(
                prefix_log="test-update-multiple"
            )

        # Verify: All instances were updated with new information
        async with test_async_session_maker() as session:
            instance_service = InstanceDbService(session)

            for i, external_instance in enumerate(external_instances, 1):
                updated_instance = await instance_service.get_acp_instance_by_manager_instance(
                    external_instance["manager_instance"]
                )

                # Verify instance was updated correctly
                assert updated_instance is not None
                assert updated_instance.url == external_instance["url"]  # New URL
                assert updated_instance.host_ip_address == f"192.168.10.{i}"  # New IP
                assert updated_instance.host_secret_id == f"new-secret-{i}"  # New secret
                assert updated_instance.metrics_collection_status == Status.OK
                assert updated_instance.reason == "Ready for metrics collection"

    @pytest.mark.asyncio
    async def test_reconcile_multiple_extra_instances_deletion(
        self, instance_reconciliation_manager, test_async_session_maker
    ):
        """Test reconciliation of multiple extra instances that need deletion (exist in DB but not in external service)."""

        # Setup: Create external instances data (smaller set than what's in DB)
        external_instances = [
            {
                "manager_instance": "acp-instance-keep-1",
                "url": "https://************/api/20.5",
            },
            {
                "manager_instance": "acp-instance-keep-2",
                "url": "https://************/api/20.5",
            },
        ]

        # Setup: Create more instances in database than exist externally
        async with test_async_session_maker() as session:
            instance_service = InstanceDbService(session)

            # Create instances that should be kept (exist in external service)
            keep_instances = []
            for external_instance in external_instances:
                db_instance = await InstanceDbFactory(
                    async_session=session,
                    manager_instance=external_instance["manager_instance"],
                    url=external_instance["url"],
                    host_ip_address="*************",
                    host_secret_id="keep-secret",
                    metrics_collection_status=Status.OK,
                )
                keep_instances.append(db_instance)

            # Create extra instances that should be deleted (don't exist in external service)
            extra_instances = []
            for i in range(1, 4):  # Create 3 extra instances
                db_instance = await InstanceDbFactory(
                    async_session=session,
                    manager_instance=f"acp-instance-delete-{i}",
                    url=f"https://delete-me-{i}/api/20.5",
                    host_ip_address=f"192.168.99.{i}",
                    host_secret_id=f"delete-secret-{i}",
                    metrics_collection_status=Status.OK,
                )
                extra_instances.append(db_instance)

            await session.commit()
            for instance in keep_instances + extra_instances:
                await session.refresh(instance)

        # Setup: Mock server mappings for kept instances
        server_mappings = {
            "acp-instance-keep-1": (
                {"host_secret_id": "keep-secret-1", "host_ip_address": "************"},
                {},
            ),
            "acp-instance-keep-2": (
                {"host_secret_id": "keep-secret-2", "host_ip_address": "************"},
                {},
            ),
        }

        instance_node_mappings = {
            "acp-instance-keep-1": "node-1",
            "acp-instance-keep-2": "node-2",
        }

        # Mock external service calls
        with (
            patch.object(
                instance_reconciliation_manager,
                "_fetch_airspan_acp_instances",
                return_value=external_instances,
            ),
            patch.object(
                instance_reconciliation_manager,
                "get_instance_server_id_mappings_with_nodes",
                return_value=(server_mappings, instance_node_mappings),
            ),
            patch.object(
                instance_reconciliation_manager,
                "_handle_instance_data_alarms",
                new_callable=AsyncMock,
            ),
        ):
            # Execute the reconciliation
            await instance_reconciliation_manager.reconcile_airspan_acp_agent_instances(
                prefix_log="test-delete-multiple"
            )

        # Verify: Extra instances were deleted, kept instances remain
        async with test_async_session_maker() as session:
            instance_service = InstanceDbService(session)

            # Verify kept instances still exist
            for external_instance in external_instances:
                kept_instance = await instance_service.get_acp_instance_by_manager_instance(
                    external_instance["manager_instance"]
                )
                assert kept_instance is not None
                assert kept_instance.url == external_instance["url"]

            # Verify extra instances were deleted
            for i in range(1, 4):
                deleted_instance = await instance_service.get_acp_instance_by_manager_instance(
                    f"acp-instance-delete-{i}"
                )
                assert deleted_instance is None  # Should be deleted

    @pytest.mark.asyncio
    async def test_reconcile_multiple_missing_instances_creation(
        self, instance_reconciliation_manager, test_async_session_maker
    ):
        """Test reconciliation of multiple missing instances that need creation (exist in external service but not in DB)."""

        # Setup: Create external instances data (new instances not in DB)
        external_instances = [
            {
                "manager_instance": "acp-instance-create-1",
                "url": "https://************/api/20.5",
            },
            {
                "manager_instance": "acp-instance-create-2",
                "url": "https://************/api/20.5",
            },
            {
                "manager_instance": "acp-instance-create-3",
                "url": "https://************/api/20.5",
            },
            {
                "manager_instance": "acp-instance-create-4",
                "url": "https://************/api/20.5",
            },
        ]

        # Setup: Start with empty database (no existing instances)
        # This means all external instances are "missing" and need to be created

        # Setup: Mock server mappings for new instances
        server_mappings = {
            "acp-instance-create-1": (
                {"host_secret_id": "create-secret-1", "host_ip_address": "************"},
                {},
            ),
            "acp-instance-create-2": (
                {"host_secret_id": "create-secret-2", "host_ip_address": "************"},
                {},
            ),
            "acp-instance-create-3": (
                {"host_secret_id": "create-secret-3", "host_ip_address": "************"},
                {},
            ),
            "acp-instance-create-4": (
                # This one has missing server data to test error handling
                {"host_secret_id": "create-secret-4"},  # Missing host_ip_address
                {"status": Status.ERROR, "reason": "Missing host_ip_address"},
            ),
        }

        instance_node_mappings = {
            "acp-instance-create-1": "node-1",
            "acp-instance-create-2": "node-2",
            "acp-instance-create-3": "node-3",
            "acp-instance-create-4": "node-4",
        }

        # Mock external service calls
        with (
            patch.object(
                instance_reconciliation_manager,
                "_fetch_airspan_acp_instances",
                return_value=external_instances,
            ),
            patch.object(
                instance_reconciliation_manager,
                "get_instance_server_id_mappings_with_nodes",
                return_value=(server_mappings, instance_node_mappings),
            ),
            patch.object(
                instance_reconciliation_manager,
                "_handle_instance_data_alarms",
                new_callable=AsyncMock,
            ),
        ):
            # Execute the reconciliation
            await instance_reconciliation_manager.reconcile_airspan_acp_agent_instances(
                prefix_log="test-create-multiple"
            )

        # Verify: All missing instances were created
        async with test_async_session_maker() as session:
            instance_service = InstanceDbService(session)

            # Verify first 3 instances created successfully
            for i in range(1, 4):
                created_instance = await instance_service.get_acp_instance_by_manager_instance(
                    f"acp-instance-create-{i}"
                )

                assert created_instance is not None
                assert created_instance.url == f"https://192.168.1.{i}0/api/20.5"
                assert created_instance.host_ip_address == f"192.168.10.{i}"
                assert created_instance.host_secret_id == f"create-secret-{i}"
                assert created_instance.metrics_collection_status == Status.OK
                assert created_instance.reason == "Ready for metrics collection"

            # Verify 4th instance created with error status due to missing data
            error_instance = await instance_service.get_acp_instance_by_manager_instance(
                "acp-instance-create-4"
            )
            assert error_instance is not None
            assert error_instance.url == "https://************/api/20.5"
            assert error_instance.host_secret_id == "create-secret-4"
            assert error_instance.host_ip_address is None  # Missing data
            assert error_instance.metrics_collection_status == Status.ERROR
            assert "Missing host_ip_address" in error_instance.reason

    @pytest.mark.asyncio
    async def test_reconcile_combined_scenarios_all_operations(
        self, instance_reconciliation_manager, test_async_session_maker
    ):
        """Test reconciliation with all three scenarios combined: create missing, update existing, delete extra instances."""

        # Setup: Create external instances data
        external_instances = [
            # These will be MISSING (need creation)
            {
                "manager_instance": "acp-new-instance-1",
                "url": "https://************/api/20.5",
            },
            {
                "manager_instance": "acp-new-instance-2",
                "url": "https://************/api/20.5",
            },
            # These will need UPDATE (exist in DB but with different data)
            {
                "manager_instance": "acp-update-instance-1",
                "url": "https://************/api/20.5",  # New URL
            },
            {
                "manager_instance": "acp-update-instance-2",
                "url": "https://192.168.2.20/api/20.5",  # New URL
            },
        ]
        # Note: Extra instances will be created in DB but not included in external_instances

        # Setup: Create existing database instances
        async with test_async_session_maker() as session:
            instance_service = InstanceDbService(session)

            # Create instances that need UPDATES (exist in external but with different data)
            update_instances = []
            for i, external_instance in enumerate(
                [
                    {
                        "manager_instance": "acp-update-instance-1",
                        "url": "https://old-url-1/api/20.5",
                    },
                    {
                        "manager_instance": "acp-update-instance-2",
                        "url": "https://old-url-2/api/20.5",
                    },
                ],
                1,
            ):
                db_instance = await InstanceDbFactory(
                    async_session=session,
                    manager_instance=external_instance["manager_instance"],
                    url=external_instance["url"],  # Old URL
                    host_ip_address=f"192.168.0.{i}",  # Old IP
                    host_secret_id=f"old-secret-{i}",  # Old secret
                    metrics_collection_status=Status.OK,
                )
                update_instances.append(db_instance)

            # Create EXTRA instances that need deletion (exist in DB but not external)
            extra_instances = []
            for i in range(1, 3):  # Create 2 extra instances
                db_instance = await InstanceDbFactory(
                    async_session=session,
                    manager_instance=f"acp-delete-instance-{i}",
                    url=f"https://delete-me-{i}/api/20.5",
                    host_ip_address=f"192.168.99.{i}",
                    host_secret_id=f"delete-secret-{i}",
                    metrics_collection_status=Status.OK,
                )
                extra_instances.append(db_instance)

            await session.commit()
            for instance in update_instances + extra_instances:
                await session.refresh(instance)

        # Setup: Mock server mappings
        server_mappings = {
            # For NEW instances (missing)
            "acp-new-instance-1": (
                {"host_secret_id": "new-secret-1", "host_ip_address": "************"},
                {},
            ),
            "acp-new-instance-2": (
                {"host_secret_id": "new-secret-2", "host_ip_address": "************"},
                {},
            ),
            # For UPDATE instances
            "acp-update-instance-1": (
                {"host_secret_id": "updated-secret-1", "host_ip_address": "************"},
                {},
            ),
            "acp-update-instance-2": (
                {"host_secret_id": "updated-secret-2", "host_ip_address": "************"},
                {},
            ),
        }

        instance_node_mappings = {
            "acp-new-instance-1": "node-new-1",
            "acp-new-instance-2": "node-new-2",
            "acp-update-instance-1": "node-update-1",
            "acp-update-instance-2": "node-update-2",
        }

        # Mock external service calls
        with (
            patch.object(
                instance_reconciliation_manager,
                "_fetch_airspan_acp_instances",
                return_value=external_instances,
            ),
            patch.object(
                instance_reconciliation_manager,
                "get_instance_server_id_mappings_with_nodes",
                return_value=(server_mappings, instance_node_mappings),
            ),
            patch.object(
                instance_reconciliation_manager,
                "_handle_instance_data_alarms",
                new_callable=AsyncMock,
            ),
        ):
            # Execute the reconciliation
            await instance_reconciliation_manager.reconcile_airspan_acp_agent_instances(
                prefix_log="test-combined-operations"
            )

        # Verify: All operations were performed correctly
        async with test_async_session_maker() as session:
            instance_service = InstanceDbService(session)

            # Verify NEW instances were created
            for i in range(1, 3):
                new_instance = await instance_service.get_acp_instance_by_manager_instance(
                    f"acp-new-instance-{i}"
                )
                assert new_instance is not None
                assert new_instance.url == f"https://192.168.1.{i}0/api/20.5"
                assert new_instance.host_ip_address == f"192.168.10.{i}"
                assert new_instance.host_secret_id == f"new-secret-{i}"
                assert new_instance.metrics_collection_status == Status.OK
                assert new_instance.reason == "Ready for metrics collection"

            # Verify UPDATED instances were modified
            for i in range(1, 3):
                updated_instance = await instance_service.get_acp_instance_by_manager_instance(
                    f"acp-update-instance-{i}"
                )
                assert updated_instance is not None
                assert updated_instance.url == f"https://192.168.2.{i}0/api/20.5"  # New URL
                assert updated_instance.host_ip_address == f"192.168.20.{i}"  # New IP
                assert updated_instance.host_secret_id == f"updated-secret-{i}"  # New secret
                assert updated_instance.metrics_collection_status == Status.OK
                assert updated_instance.reason == "Ready for metrics collection"

            # Verify EXTRA instances were deleted
            for i in range(1, 3):
                deleted_instance = await instance_service.get_acp_instance_by_manager_instance(
                    f"acp-delete-instance-{i}"
                )
                assert deleted_instance is None  # Should be deleted

    @pytest.mark.asyncio
    async def test_reconcile_multiple_instances_with_different_server_mapping_errors(
        self, instance_reconciliation_manager, test_async_session_maker
    ):
        """Test reconciliation of multiple instances with various server mapping error scenarios."""

        # Setup: Create external instances with different error conditions
        external_instances = [
            {
                "manager_instance": "acp-error-missing-component",
                "url": "https://************/api/20.5",
            },
            {
                "manager_instance": "acp-error-missing-node",
                "url": "https://************/api/20.5",
            },
            {
                "manager_instance": "acp-error-partial-server-data",
                "url": "https://************/api/20.5",
            },
            {
                "manager_instance": "acp-success-complete-data",
                "url": "https://************/api/20.5",
            },
        ]

        # Setup: Mock server mappings with different error scenarios
        server_mappings = {
            # Instance 1: Component missing from inventory (no mapping at all)
            # "acp-error-missing-component" not in mappings
            # Instance 2: Component exists but node not found in orchestrator
            "acp-error-missing-node": (
                {},
                {"reason": "Node test-node-2 not found in orchestrator"},
            ),
            # Instance 3: Node exists but partial server data (missing IP)
            "acp-error-partial-server-data": (
                {"host_secret_id": "partial-secret-3"},  # Missing host_ip_address
                {},
            ),
            # Instance 4: Complete data available
            "acp-success-complete-data": (
                {"host_secret_id": "complete-secret-4", "host_ip_address": "************"},
                {},
            ),
        }

        instance_node_mappings = {
            # Instance 1: Missing from node mappings (component not found)
            # "acp-error-missing-component" not in mappings
            "acp-error-missing-node": "test-node-2",
            "acp-error-partial-server-data": "test-node-3",
            "acp-success-complete-data": "test-node-4",
        }

        # Mock external service calls
        with (
            patch.object(
                instance_reconciliation_manager,
                "_fetch_airspan_acp_instances",
                return_value=external_instances,
            ),
            patch.object(
                instance_reconciliation_manager,
                "get_instance_server_id_mappings_with_nodes",
                return_value=(server_mappings, instance_node_mappings),
            ),
            patch.object(
                instance_reconciliation_manager,
                "_handle_instance_data_alarms",
                new_callable=AsyncMock,
            ),
        ):
            # Execute the reconciliation
            await instance_reconciliation_manager.reconcile_airspan_acp_agent_instances(
                prefix_log="test-error-scenarios"
            )

        # Verify: Instances were created with appropriate status based on data availability
        async with test_async_session_maker() as session:
            instance_service = InstanceDbService(session)

            # Verify instance with missing component - should be created with ERROR status
            missing_component_instance = (
                await instance_service.get_acp_instance_by_manager_instance(
                    "acp-error-missing-component"
                )
            )
            assert missing_component_instance is not None
            assert missing_component_instance.url == "https://************/api/20.5"
            assert missing_component_instance.host_ip_address is None
            assert missing_component_instance.host_secret_id is None
            assert missing_component_instance.metrics_collection_status == Status.ERROR
            # From logs: "Missing host IP address and host secret id in nms-orchestrator"
            assert (
                missing_component_instance.reason
                == "Missing host IP address and host secret id in nms-orchestrator"
            )

            # Verify instance with missing node mapping - should be created with ERROR status
            missing_node_instance = await instance_service.get_acp_instance_by_manager_instance(
                "acp-error-missing-node"
            )
            assert missing_node_instance is not None
            assert missing_node_instance.url == "https://************/api/20.5"
            assert missing_node_instance.host_ip_address is None
            assert missing_node_instance.host_secret_id is None
            assert missing_node_instance.metrics_collection_status == Status.ERROR
            # Expected: "Node test-node-2 not found in orchestrator"
            assert missing_node_instance.reason == "Node test-node-2 not found in orchestrator"

            # Verify instance with partial server data - should be created with ERROR status
            partial_data_instance = await instance_service.get_acp_instance_by_manager_instance(
                "acp-error-partial-server-data"
            )
            assert partial_data_instance is not None
            assert partial_data_instance.url == "https://************/api/20.5"
            assert partial_data_instance.host_secret_id == "partial-secret-3"
            assert partial_data_instance.host_ip_address is None  # Missing data
            assert partial_data_instance.metrics_collection_status == Status.ERROR
            # Expected: "Missing host IP address and host secret id in nms-orchestrator" (from static method)
            # But since we have secret_id but missing ip_address, it should be the partial case
            assert "Missing host IP address" in partial_data_instance.reason

            # Verify instance with complete data - should be created with OK status
            complete_data_instance = (
                await instance_service.get_acp_instance_by_manager_instance(
                    "acp-success-complete-data"
                )
            )
            assert complete_data_instance is not None
            assert complete_data_instance.url == "https://************/api/20.5"
            assert complete_data_instance.host_secret_id == "complete-secret-4"
            assert complete_data_instance.host_ip_address == "************"
            assert complete_data_instance.metrics_collection_status == Status.OK
            assert complete_data_instance.reason == "Ready for metrics collection"
