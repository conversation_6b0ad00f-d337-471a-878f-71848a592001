from unittest.mock import MagicMock
from unittest.mock import patch

import asyncssh
import pytest
from da_common.models import Status

from airspan_acp_metrics.constants import SERVER_CONNECTION_REFUSED_ALARM_TYPE
from airspan_acp_metrics.constants import SERVER_CONNECTION_TIMED_OUT_ALARM_TYPE
from airspan_acp_metrics.constants import SERVER_HOST_UNREACHABLE_ALARM_TYPE
from airspan_acp_metrics.constants import SERVER_MISSING_CREDENTIALS_ALARM_TYPE
from airspan_acp_metrics.constants import SERVER_PERMISSION_DENIED_ALARM_TYPE
from airspan_acp_metrics.constants import SERVER_UNEXPECTED_CONNECTION_ERROR_ALARM_TYPE
from airspan_acp_metrics.managers.ssh_connection_manager import SSHConnectionManager
from airspan_acp_metrics.pyd_models.instance_pyd_model import AuthType


class TestSSHConnectionManager:
    """Tests for the SSHConnectionManager class including both success and exception scenarios."""

    @pytest.fixture
    def ssh_manager(self, test_config):
        """Create an SSHConnectionManager instance."""
        return SSHConnectionManager(test_config)

    @pytest.fixture
    def sample_metric_config(self):
        """Create a sample metric configuration."""
        return {
            "metric_name": "Stats",
            "enabled": True,
            "frequency": 5,
        }

    # SUCCESS TESTS
    @pytest.mark.asyncio
    async def test_get_connection_success(self, ssh_manager, sample_instance_pyd_model):
        """Test SSH connection manager successful connection."""
        # Mock successful connection
        mock_connection = MagicMock()

        with (
            patch(
                "airspan_acp_metrics.managers.ssh_connection_manager.get_secret_data",
                return_value={"username": "test", "password": "test"},
            ) as mock_get_secret,
            patch.object(ssh_manager, "_establish_connection", return_value=mock_connection),
        ):
            connection, response = await ssh_manager.get_connection(
                sample_instance_pyd_model, 30.0
            )

            # Verify successful connection
            assert connection == mock_connection
            assert response["status"] == Status.OK

            # Verify secret was retrieved
            mock_get_secret.assert_called_once()

    def test_close_connection_success(self):
        """Test close_connection successful execution."""
        mock_connection = MagicMock()

        # Should not raise exception
        SSHConnectionManager.close_connection(mock_connection, "test-instance")

        # Verify close was called
        mock_connection.close.assert_called_once()

    # EXCEPTION TESTS
    @pytest.mark.asyncio
    async def test_get_connection_missing_credentials_from_secret_manager(
        self, ssh_manager, sample_instance_pyd_model
    ):
        """Test get_connection when credentials are missing from secret manager."""
        with patch(
            "airspan_acp_metrics.managers.ssh_connection_manager.get_secret_data",
            return_value=None,
        ) as mock_get_secret:
            connection, response = await ssh_manager.get_connection(
                sample_instance_pyd_model, 30.0
            )

            # Verify get_secret_data was called
            mock_get_secret.assert_called_once()

            # Verify failed connection
            assert connection is None
            assert response["status"] == Status.ERROR
            assert "SSH credentials not found in Google Secret Manager" in response["reason"]

    @pytest.mark.parametrize(
        "exception_class,exception_message,expected_cause,expected_reason_contains",
        [
            (
                PermissionError,
                "Permission denied",
                SERVER_PERMISSION_DENIED_ALARM_TYPE,
                "SSH authentication failed - invalid username/password",
            ),
            (
                TimeoutError,
                "Connection timed out",
                SERVER_CONNECTION_TIMED_OUT_ALARM_TYPE,
                "SSH connection timeout after 30.0s",
            ),
            (
                ConnectionRefusedError,
                "Connection refused",
                SERVER_CONNECTION_REFUSED_ALARM_TYPE,
                "SSH connection refused - service unavailable",
            ),
            (
                OSError,
                "Host unreachable",
                SERVER_HOST_UNREACHABLE_ALARM_TYPE,
                "Host unreachable - network error:",
            ),
            # asyncssh.Error is not tested here rather in test_get_connection_asyncssh_connect_exception
        ],
    )
    @pytest.mark.asyncio
    async def test_get_connection_exceptions(
        self,
        ssh_manager,
        sample_instance_pyd_model,
        exception_class,
        exception_message,
        expected_cause,
        expected_reason_contains,
    ):
        """Test get_connection with various exception types."""
        with (
            patch(
                "airspan_acp_metrics.managers.ssh_connection_manager.get_secret_data",
                return_value={"username": "test", "password": "test"},
            ),
            patch.object(
                ssh_manager,
                "_establish_connection",
                side_effect=exception_class(exception_message),
            ),
        ):
            connection, response = await ssh_manager.get_connection(
                sample_instance_pyd_model, 30.0
            )

            # Verify failed connection
            assert connection is None
            assert response["status"] == Status.ERROR
            assert response["cause"] == expected_cause
            assert expected_reason_contains in response["reason"]

    @pytest.mark.asyncio
    async def test_get_connection_asyncssh_connect_exception(
        self, ssh_manager, sample_instance_pyd_model
    ):
        """Test get_connection when asyncssh.connect raises an exception."""
        with (
            patch(
                "airspan_acp_metrics.managers.ssh_connection_manager.get_secret_data",
                return_value={"username": "test", "password": "test"},
            ),
            patch(
                "airspan_acp_metrics.managers.ssh_connection_manager.asyncssh.connect",
                side_effect=asyncssh.Error(code=1, reason="SSH protocol error"),
            ),
        ):
            connection, response = await ssh_manager.get_connection(
                sample_instance_pyd_model, 30.0
            )

            # Verify failed connection
            assert connection is None
            assert response["status"] == Status.ERROR
            assert response["cause"] == SERVER_UNEXPECTED_CONNECTION_ERROR_ALARM_TYPE

    def test_close_connection_exception(self):
        """Test close_connection when closing the connection raises an exception."""
        mock_connection = MagicMock()
        mock_connection.close.side_effect = Exception("Connection already closed")

        # Should not raise exception
        SSHConnectionManager.close_connection(mock_connection, "test-instance")

        # Verify close was attempted
        mock_connection.close.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_connection_secret_manager_exception(
        self, ssh_manager, sample_instance_pyd_model
    ):
        """Test get_connection when secret manager raises an exception."""
        with patch(
            "airspan_acp_metrics.managers.ssh_connection_manager.get_secret_data",
            side_effect=Exception("Secret manager unavailable"),
        ):
            # Should handle exception and return missing credentials error
            try:
                connection, response = await ssh_manager.get_connection(
                    sample_instance_pyd_model, 30.0
                )
                # If no exception is raised, ensure connection failed properly
                assert connection is None
                assert response["status"] == Status.ERROR
            except Exception:
                # If exception propagates, that's also acceptable behavior
                pass

    # SSH KEY AUTHENTICATION TESTS
    @pytest.mark.asyncio
    async def test_get_connection_ssh_key_success(self, ssh_manager, sample_instance_pyd_model):
        """Test SSH connection with SSH key authentication success."""
        # Set up SSH key instance
        sample_instance_pyd_model.auth_type = AuthType.SSH_KEY
        mock_connection = MagicMock()
        mock_private_key = MagicMock()

        with (
            patch(
                "airspan_acp_metrics.managers.ssh_connection_manager.get_secret_data",
                return_value={
                    "username": "test",
                    # Linters doesnt allow even test private key
                    "private_key": "-----BEGIN OPENSSH-----\ntest_key\n-----END OPENSSH -----",
                },
            ),
            patch(
                "airspan_acp_metrics.managers.ssh_connection_manager.asyncssh.import_private_key",
                return_value=mock_private_key,
            ),
            patch.object(
                ssh_manager, "_establish_connection", return_value=mock_connection
            ) as mock_establish,
        ):
            connection, response = await ssh_manager.get_connection(
                sample_instance_pyd_model, 30.0
            )

            # Verify successful connection
            assert connection == mock_connection
            assert response["status"] == Status.OK
            assert "SSH connection successful" in response["reason"]

            # Verify _establish_connection was called correctly
            mock_establish.assert_called_once()

            # Verify _establish_connection was called with correct parameters
            call_args = mock_establish.call_args
            assert call_args[1]["auth_type"] == AuthType.SSH_KEY
            assert call_args[1]["credentials"]["username"] == "test"
            assert "private_key" in call_args[1]["credentials"]

    @pytest.mark.asyncio
    async def test_get_connection_ssh_key_missing_private_key(
        self, ssh_manager, sample_instance_pyd_model
    ):
        """Test SSH connection fails when private key is missing from credentials."""
        sample_instance_pyd_model.auth_type = AuthType.SSH_KEY

        with patch(
            "airspan_acp_metrics.managers.ssh_connection_manager.get_secret_data",
            return_value={"username": "test"},  # Missing private_key
        ):
            connection, response = await ssh_manager.get_connection(
                sample_instance_pyd_model, 30.0
            )

            # Verify failed connection
            assert connection is None
            assert response["status"] == Status.ERROR
            assert response["cause"] == SERVER_MISSING_CREDENTIALS_ALARM_TYPE
            assert "SSH key credentials incomplete" in response["reason"]
            assert "missing username or private_key" in response["reason"]

    @pytest.mark.asyncio
    async def test_get_connection_ssh_key_missing_username(
        self, ssh_manager, sample_instance_pyd_model
    ):
        """Test SSH connection fails when username is missing for SSH key auth."""
        sample_instance_pyd_model.auth_type = AuthType.SSH_KEY

        with patch(
            "airspan_acp_metrics.managers.ssh_connection_manager.get_secret_data",
            return_value={"private_key": "test_key"},  # Missing username
        ):
            connection, response = await ssh_manager.get_connection(
                sample_instance_pyd_model, 30.0
            )

            # Verify failed connection
            assert connection is None
            assert response["status"] == Status.ERROR
            assert response["cause"] == SERVER_MISSING_CREDENTIALS_ALARM_TYPE
            assert "SSH key credentials incomplete" in response["reason"]

    @pytest.mark.asyncio
    async def test_get_connection_ssh_key_invalid_format(
        self, ssh_manager, sample_instance_pyd_model
    ):
        """Test SSH connection fails when private key has invalid format."""
        sample_instance_pyd_model.auth_type = AuthType.SSH_KEY

        with (
            patch(
                "airspan_acp_metrics.managers.ssh_connection_manager.get_secret_data",
                return_value={"username": "test", "private_key": "invalid_key_format"},
            ),
            patch(
                "airspan_acp_metrics.managers.ssh_connection_manager.asyncssh.import_private_key",
                side_effect=ValueError("Invalid key format"),
            ),
        ):
            connection, response = await ssh_manager.get_connection(
                sample_instance_pyd_model, 30.0
            )

            # Verify failed connection
            assert connection is None
            assert response["status"] == Status.ERROR
            assert response["cause"] == SERVER_MISSING_CREDENTIALS_ALARM_TYPE
            assert "SSH key validation failed" in response["reason"]
            assert "Invalid key format" in response["reason"]

    @pytest.mark.asyncio
    async def test_get_connection_ssh_key_authentication_failed(
        self, ssh_manager, sample_instance_pyd_model
    ):
        """Test SSH connection fails when SSH key authentication is denied."""
        sample_instance_pyd_model.auth_type = AuthType.SSH_KEY
        mock_private_key = MagicMock()

        with (
            patch(
                "airspan_acp_metrics.managers.ssh_connection_manager.get_secret_data",
                return_value={"username": "test", "private_key": "valid_key"},
            ),
            patch(
                "airspan_acp_metrics.managers.ssh_connection_manager.asyncssh.import_private_key",
                return_value=mock_private_key,
            ),
            patch(
                "airspan_acp_metrics.managers.ssh_connection_manager.asyncssh.connect",
                side_effect=asyncssh.PermissionDenied("SSH key rejected"),
            ),
        ):
            connection, response = await ssh_manager.get_connection(
                sample_instance_pyd_model, 30.0
            )

            # Verify failed connection
            assert connection is None
            assert response["status"] == Status.ERROR
            assert response["cause"] == SERVER_PERMISSION_DENIED_ALARM_TYPE
            assert "SSH authentication failed - invalid SSH key" in response["reason"]

    @pytest.mark.asyncio
    async def test_get_connection_password_auth_still_works(
        self, ssh_manager, sample_instance_pyd_model
    ):
        """Test that password authentication still works after SSH key implementation."""
        # Ensure password auth type (default)
        sample_instance_pyd_model.auth_type = AuthType.PASSWORD
        mock_connection = MagicMock()

        with (
            patch(
                "airspan_acp_metrics.managers.ssh_connection_manager.get_secret_data",
                return_value={"username": "test", "password": "test"},
            ),
            patch.object(
                ssh_manager, "_establish_connection", return_value=mock_connection
            ) as mock_establish,
        ):
            connection, response = await ssh_manager.get_connection(
                sample_instance_pyd_model, 30.0
            )

            # Verify successful connection
            assert connection == mock_connection
            assert response["status"] == Status.OK

            # Verify _establish_connection was called with correct parameters
            call_args = mock_establish.call_args
            assert call_args[1]["auth_type"] == AuthType.PASSWORD
            assert call_args[1]["credentials"]["username"] == "test"
            assert call_args[1]["credentials"]["password"] == "test"

    @pytest.mark.asyncio
    async def test_get_connection_password_missing_credentials(
        self, ssh_manager, sample_instance_pyd_model
    ):
        """Test password authentication fails when credentials are incomplete."""
        sample_instance_pyd_model.auth_type = AuthType.PASSWORD

        with patch(
            "airspan_acp_metrics.managers.ssh_connection_manager.get_secret_data",
            return_value={"username": "test"},  # Missing password
        ):
            connection, response = await ssh_manager.get_connection(
                sample_instance_pyd_model, 30.0
            )

            # Verify failed connection
            assert connection is None
            assert response["status"] == Status.ERROR
            assert response["cause"] == SERVER_MISSING_CREDENTIALS_ALARM_TYPE
            assert "Password credentials incomplete" in response["reason"]
            assert "missing username or password" in response["reason"]

    @pytest.mark.asyncio
    async def test_get_connection_password_authentication_failed(
        self, ssh_manager, sample_instance_pyd_model
    ):
        """Test password authentication fails with wrong credentials."""
        sample_instance_pyd_model.auth_type = AuthType.PASSWORD

        with (
            patch(
                "airspan_acp_metrics.managers.ssh_connection_manager.get_secret_data",
                return_value={"username": "test", "password": "wrong_password"},
            ),
            patch(
                "airspan_acp_metrics.managers.ssh_connection_manager.asyncssh.connect",
                side_effect=asyncssh.PermissionDenied("Invalid password"),
            ),
        ):
            connection, response = await ssh_manager.get_connection(
                sample_instance_pyd_model, 30.0
            )

            # Verify failed connection
            assert connection is None
            assert response["status"] == Status.ERROR
            assert response["cause"] == SERVER_PERMISSION_DENIED_ALARM_TYPE
            assert "SSH authentication failed - invalid username/password" in response["reason"]

    @pytest.mark.asyncio
    async def test_get_connection_with_builtin_timeout_error(
        self, ssh_manager, sample_instance_pyd_model
    ):
        """Test get_connection when built-in TimeoutError is raised."""
        with (
            patch(
                "airspan_acp_metrics.managers.ssh_connection_manager.get_secret_data",
                return_value={"username": "test", "password": "test"},
            ),
            patch.object(
                ssh_manager,
                "_establish_connection",
                side_effect=TimeoutError("Built-in timeout"),
            ),
        ):
            connection, response = await ssh_manager.get_connection(
                sample_instance_pyd_model, 30.0
            )

            # Verify failed connection
            assert connection is None
            assert response["status"] == Status.ERROR
            assert response["cause"] == SERVER_CONNECTION_TIMED_OUT_ALARM_TYPE

    @pytest.mark.asyncio
    async def test_get_connection_with_builtin_permission_error(
        self, ssh_manager, sample_instance_pyd_model
    ):
        """Test get_connection when built-in PermissionError is raised."""
        with (
            patch(
                "airspan_acp_metrics.managers.ssh_connection_manager.get_secret_data",
                return_value={"username": "test", "password": "test"},
            ),
            patch.object(
                ssh_manager,
                "_establish_connection",
                side_effect=PermissionError("Built-in permission error"),
            ),
        ):
            connection, response = await ssh_manager.get_connection(
                sample_instance_pyd_model, 30.0
            )

            # Verify failed connection
            assert connection is None
            assert response["status"] == Status.ERROR
            assert response["cause"] == SERVER_PERMISSION_DENIED_ALARM_TYPE
