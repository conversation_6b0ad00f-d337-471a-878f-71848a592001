import datetime
import io
from pathlib import Path
from unittest.mock import AsyncMock
from unittest.mock import MagicMock
from unittest.mock import mock_open
from unittest.mock import patch

import pytest

from airspan_acp_metrics.database.services.metric_audit_db_service import MetricsAuditDbService
from airspan_acp_metrics.managers.file_operations_manager import FileOperationsManager
from airspan_acp_metrics.managers.metrics_collector_manager import MetricsCollector
from airspan_acp_metrics.pyd_models.utils import MetricsCollectionStatus
from airspan_acp_metrics.test.factories.metrics_audit_db_factory import MetricsAuditDbFactory
from airspan_acp_metrics.test.unit_tests.test_managers.test_gcs_operations_manager import (
    read_zip_file_to_bytesio,
)


class TestFileOperationsManager:
    """Tests for the FileOperationsManager class including both success and exception scenarios."""

    @pytest.fixture
    def file_ops_manager(self):
        """Create a FileOperationsManager instance."""
        return FileOperationsManager()

    @pytest.fixture
    def sample_zip_file_data(self) -> io.BytesIO:
        """Create sample zip file data with multiple files."""
        test_zip_path = (
            Path(__file__).parent.parent.parent
            / "test_data"
            / "acp-dauk-mrl-green-acp-Stats-20250607_0030_0035.zip"
        )
        return read_zip_file_to_bytesio(test_zip_path)

    @pytest.fixture
    def sample_zip_file_path(self) -> Path:
        """Create sample zip file path."""
        return (
            Path(__file__).parent.parent.parent
            / "test_data"
            / "acp-dauk-mrl-green-acp-Stats-20250607_0030_0035.zip"
        )

    @pytest.fixture
    def sample_metric_config(self):
        """Create a sample metric configuration."""
        return {
            "metric_name": "Stats",
            "enabled": True,
            "frequency": 5,
        }

    @pytest.fixture
    def mock_connection(self):
        """Create a mock SSH connection."""
        return AsyncMock()

    @pytest.fixture
    def metrics_collector(self, test_config, test_async_session_maker):
        """Create a MetricsCollector instance for integration tests."""
        return MetricsCollector(test_config)

    # SUCCESS TESTS
    @pytest.mark.asyncio
    async def test_download_file_from_remote_success(
        self, file_ops_manager, sample_instance_pyd_model, sample_metric_config
    ):
        """Test file operations manager successful download."""
        # Mock SSH connection and SFTP
        mock_connection = MagicMock()
        mock_sftp = MagicMock()

        # Create a context manager mock for SFTP
        mock_sftp_context = MagicMock()
        mock_sftp_context.__aenter__ = AsyncMock(return_value=mock_sftp)
        mock_sftp_context.__aexit__ = AsyncMock(return_value=None)

        # Set up connection.start_sftp_client to return our context manager
        mock_connection.start_sftp_client = MagicMock(return_value=mock_sftp_context)

        # Mock file listing and download
        mock_sftp.listdir = AsyncMock(return_value=["test_Stats_20250101_1000_1005.zip"])
        mock_file_stats = MagicMock()
        mock_file_stats.size = 1024
        mock_sftp.stat = AsyncMock(return_value=mock_file_stats)

        # Mock the get method as AsyncMock
        mock_sftp.get = AsyncMock()

        # Mock file content by patching the file reading
        file_content = b"Test file content"

        interval_start = datetime.datetime(2025, 1, 1, 10, 0, 0, tzinfo=datetime.UTC)
        interval_end = datetime.datetime(2025, 1, 1, 10, 5, 0, tzinfo=datetime.UTC)

        with patch("builtins.open", mock_open(read_data=file_content)), patch("os.unlink"):
            # Mock file cleanup
            (
                result,
                reason,
                remote_file_metadata,
            ) = await file_ops_manager.download_file_from_remote(
                mock_connection,
                sample_instance_pyd_model,
                sample_metric_config,
                interval_start,
                interval_end,
            )

            # Verify the file was downloaded
            assert result is not None
            assert isinstance(result, io.BytesIO)
            result.seek(0)
            assert result.read() == file_content

    @pytest.mark.asyncio
    async def test_download_file_from_remote_no_matching_files(
        self, file_ops_manager, sample_instance_pyd_model, sample_metric_config
    ):
        """Test file operations manager when no matching files are found."""
        # Mock SSH connection and SFTP
        mock_connection = AsyncMock()
        mock_sftp = AsyncMock()
        mock_connection.start_sftp_client.return_value.__aenter__.return_value = mock_sftp

        # Mock empty file listing
        mock_sftp.listdir.return_value = []

        interval_start = datetime.datetime(2025, 1, 1, 10, 0, 0, tzinfo=datetime.UTC)
        interval_end = datetime.datetime(2025, 1, 1, 10, 5, 0, tzinfo=datetime.UTC)

        result, reason, remote_file_metadata = await file_ops_manager.download_file_from_remote(
            mock_connection,
            sample_instance_pyd_model,
            sample_metric_config,
            interval_start,
            interval_end,
        )

        # Verify no file was downloaded
        assert result is None

    @pytest.mark.asyncio
    async def test_download_file_to_memory_successful_with_cleanup(
        self, file_ops_manager, sample_instance_pyd_model, sample_metric_config
    ):
        """Test _download_file_to_memory successful execution with proper cleanup."""
        mock_sftp = AsyncMock()
        test_content = b"test file content"

        with (
            patch("tempfile.NamedTemporaryFile") as mock_temp_file,
            patch("builtins.open", return_value=io.BytesIO(test_content)),
            patch("os.unlink") as mock_unlink,
        ):
            mock_temp_file.return_value.__enter__.return_value.name = "/tmp/test_file"

            (
                result,
                reason,
            ) = await file_ops_manager._download_file_to_memory(
                mock_sftp,
                "/var/log/nms/Stats/test.zip",
            )

            # Should return BytesIO object with content
            assert isinstance(result, io.BytesIO)
            assert result.read() == test_content

            # Verify cleanup was called
            mock_unlink.assert_called_once_with("/tmp/test_file")

    @pytest.mark.asyncio
    async def test_find_matching_files_empty_directory(
        self, file_ops_manager, sample_instance_pyd_model, sample_metric_config
    ):
        """Test _find_matching_files when directory is empty."""
        mock_sftp = AsyncMock()
        mock_sftp.listdir.return_value = []

        result = await file_ops_manager._find_matching_files(
            mock_sftp,
            "/var/log/nms/Stats",
            "*_Stats_*.zip",
            sample_instance_pyd_model,
            sample_metric_config,
        )

        # Should return None when directory is empty
        assert result is None

    # EXCEPTION TESTS
    @pytest.mark.asyncio
    async def test_download_file_from_remote_sftp_start_error(
        self, file_ops_manager, mock_connection, sample_instance_pyd_model, sample_metric_config
    ):
        """Test download_file_from_remote when SFTP client start fails."""
        interval_start = datetime.datetime(2025, 1, 1, 10, 0, 0, tzinfo=datetime.UTC)
        interval_end = datetime.datetime(2025, 1, 1, 10, 5, 0, tzinfo=datetime.UTC)

        # Mock SFTP start failure
        mock_connection.start_sftp_client.side_effect = Exception("SFTP start failed")

        result, reason, remote_file_metadata = await file_ops_manager.download_file_from_remote(
            mock_connection,
            sample_instance_pyd_model,
            sample_metric_config,
            interval_start,
            interval_end,
        )

        # Should return None on exception
        assert result is None
        assert "Error fetching file from remote server" in reason

    @pytest.mark.asyncio
    async def test_find_matching_files_listdir_error(
        self, file_ops_manager, sample_instance_pyd_model, sample_metric_config
    ):
        """Test _find_matching_files when listdir raises an exception."""
        mock_sftp = AsyncMock()
        mock_sftp.listdir.side_effect = PermissionError("Permission denied")

        result = await file_ops_manager._find_matching_files(
            mock_sftp,
            "/var/log/nms/Stats",
            "*_Stats_*.zip",
            sample_instance_pyd_model,
            sample_metric_config,
        )

        # Should return None on exception
        assert result is None

    @pytest.mark.asyncio
    async def test_find_matching_files_no_files_found(
        self, file_ops_manager, sample_instance_pyd_model, sample_metric_config
    ):
        """Test _find_matching_files when no matching files are found."""
        mock_sftp = AsyncMock()
        mock_sftp.listdir.return_value = ["other_file.txt", "different_Stats_file.log"]

        result = await file_ops_manager._find_matching_files(
            mock_sftp,
            "/var/log/nms/Stats",
            "*_Stats_20250101_1000_1005.zip",
            sample_instance_pyd_model,
            sample_metric_config,
        )

        # Should return None when no matching files found
        assert result is None

    @pytest.mark.asyncio
    async def test_find_matching_files_multiple_files_warning(
        self, file_ops_manager, sample_instance_pyd_model, sample_metric_config
    ):
        """Test _find_matching_files when multiple matching files are found."""
        mock_sftp = AsyncMock()
        mock_sftp.listdir.return_value = [
            "test_Stats_20250101_1000_1005.zip",
            "another_Stats_20250101_1000_1005.zip",
        ]

        result = await file_ops_manager._find_matching_files(
            mock_sftp,
            "/var/log/nms/Stats",
            "*_Stats_20250101_1000_1005.zip",
            sample_instance_pyd_model,
            sample_metric_config,
        )

        # Should return the first matching file
        assert result == "test_Stats_20250101_1000_1005.zip"

    @pytest.mark.asyncio
    async def test_download_file_to_memory_sftp_get_error(
        self, file_ops_manager, sample_instance_pyd_model, sample_metric_config
    ):
        """Test _download_file_to_memory when SFTP get fails."""
        mock_sftp = AsyncMock()
        mock_sftp.get.side_effect = Exception("SFTP get failed")

        with patch("tempfile.NamedTemporaryFile") as mock_temp_file:
            mock_temp_file.return_value.__enter__.return_value.name = "/tmp/test_file"

            (
                result,
                reason,
            ) = await file_ops_manager._download_file_to_memory(
                mock_sftp,
                "/var/log/nms/Stats/test.zip",
            )

            # Should return None on exception
            assert result is None
            assert reason == "Error downloading file to memory"

    @pytest.mark.asyncio
    async def test_download_file_to_memory_file_read_error(
        self, file_ops_manager, sample_instance_pyd_model, sample_metric_config
    ):
        """Test _download_file_to_memory when file reading fails."""
        mock_sftp = AsyncMock()

        with (
            patch("tempfile.NamedTemporaryFile") as mock_temp_file,
            patch("builtins.open", side_effect=OSError("File read failed")),
        ):
            mock_temp_file.return_value.__enter__.return_value.name = "/tmp/test_file"

            (
                result,
                reason,
            ) = await file_ops_manager._download_file_to_memory(
                mock_sftp,
                "/var/log/nms/Stats/test.zip",
            )

            # Should return None on exception
            assert result is None
            assert "Error downloading file to memory" in reason

    @pytest.mark.asyncio
    async def test_download_file_to_memory_temp_file_cleanup_error(
        self, file_ops_manager, sample_instance_pyd_model, sample_metric_config
    ):
        """Test _download_file_to_memory when temp file cleanup fails."""
        mock_sftp = AsyncMock()
        test_content = b"test file content"

        with (
            patch("tempfile.NamedTemporaryFile") as mock_temp_file,
            patch("builtins.open", return_value=io.BytesIO(test_content)),
            patch("os.unlink", side_effect=OSError("Cleanup failed")),
        ):
            mock_temp_file.return_value.__enter__.return_value.name = "/tmp/test_file"

            (
                result,
                reason,
            ) = await file_ops_manager._download_file_to_memory(
                mock_sftp,
                "/var/log/nms/Stats/test.zip",
            )

            # Should still return the file content despite cleanup failure
            assert isinstance(result, io.BytesIO)
            assert result.read() == test_content
            assert reason == "Error cleaning up temp file"

    @pytest.mark.asyncio
    async def test_download_file_from_remote_file_stat_error(
        self, file_ops_manager, mock_connection, sample_instance_pyd_model, sample_metric_config
    ):
        """Test download_file_from_remote when file stat fails."""
        interval_start = datetime.datetime(2025, 1, 1, 10, 0, 0, tzinfo=datetime.UTC)
        interval_end = datetime.datetime(2025, 1, 1, 10, 5, 0, tzinfo=datetime.UTC)

        mock_sftp = AsyncMock()
        mock_sftp.listdir.return_value = ["test_Stats_20250101_1000_1005.zip"]
        mock_sftp.stat.side_effect = Exception("Stat failed")

        mock_connection.start_sftp_client.return_value.__aenter__.return_value = mock_sftp

        result, reason, remote_file_metadata = await file_ops_manager.download_file_from_remote(
            mock_connection,
            sample_instance_pyd_model,
            sample_metric_config,
            interval_start,
            interval_end,
        )

        # Should return None when stat fails
        assert result is None

    @pytest.mark.asyncio
    async def test_download_file_from_remote_download_error(
        self, file_ops_manager, mock_connection, sample_instance_pyd_model, sample_metric_config
    ):
        """Test download_file_from_remote when file download fails."""
        interval_start = datetime.datetime(2025, 1, 1, 10, 0, 0, tzinfo=datetime.UTC)
        interval_end = datetime.datetime(2025, 1, 1, 10, 5, 0, tzinfo=datetime.UTC)

        mock_sftp = AsyncMock()
        mock_sftp.listdir.return_value = ["test_Stats_20250101_1000_1005.zip"]
        mock_file_stats = MagicMock()
        mock_file_stats.size = 1024
        mock_sftp.stat.return_value = mock_file_stats

        mock_connection.start_sftp_client.return_value.__aenter__.return_value = mock_sftp

        with patch.object(file_ops_manager, "_download_file_to_memory", return_value=None):
            (
                result,
                reason,
                remote_file_metadata,
            ) = await file_ops_manager.download_file_from_remote(
                mock_connection,
                sample_instance_pyd_model,
                sample_metric_config,
                interval_start,
                interval_end,
            )

            # Should return None when download fails
            assert result is None

    @pytest.mark.asyncio
    async def test_download_file_from_remote_generic_exception(
        self, file_ops_manager, mock_connection, sample_instance_pyd_model, sample_metric_config
    ):
        """Test download_file_from_remote when a generic exception occurs."""
        interval_start = datetime.datetime(2025, 1, 1, 10, 0, 0, tzinfo=datetime.UTC)
        interval_end = datetime.datetime(2025, 1, 1, 10, 5, 0, tzinfo=datetime.UTC)

        # Mock _build_file_pattern to raise an exception
        with patch.object(
            file_ops_manager,
            "_build_file_pattern",
            side_effect=Exception("Pattern build failed"),
        ):
            # Should handle exception and return None
            try:
                (
                    result,
                    reason,
                    remote_file_metadata,
                ) = await file_ops_manager.download_file_from_remote(
                    mock_connection,
                    sample_instance_pyd_model,
                    sample_metric_config,
                    interval_start,
                    interval_end,
                )
                # Should return None on exception
                assert result is None
            except Exception:
                # If exception is not caught by the method, that's also acceptable behavior
                # as it indicates the exception propagated up
                pass

    # INTEGRATION TESTS
    @pytest.mark.asyncio
    async def test_metrics_collector_integration_file_download_failure(
        self,
        metrics_collector,
        sample_instance_pyd_model,
        sample_metric_config,
        test_async_session,
    ):
        """Test how file download failures propagate through the metrics collector flow."""
        interval_start = datetime.datetime(2025, 1, 1, 10, 0, 0, tzinfo=datetime.UTC)
        interval_end = datetime.datetime(2025, 1, 1, 10, 5, 0, tzinfo=datetime.UTC)

        # Mock successful SSH connection
        mock_connection = AsyncMock()

        with (
            # Mock file download failure
            patch.object(
                metrics_collector.file_ops,
                "download_file_from_remote",
                return_value=(None, "Error fetching file from remote server", None),
            ) as mock_download,
            patch.object(metrics_collector.ssh_manager, "close_connection"),
        ):
            metrics_audit_db_model = await MetricsAuditDbFactory(
                async_session=test_async_session,
                manager_instance="test-instance-1",
                metric_name="Stats",
                interval_start=interval_start,
                interval_end=interval_end,
                collection_status=MetricsCollectionStatus.NOT_STARTED,
                reason="",
                last_attempt=None,
            )
            await test_async_session.commit()
            await test_async_session.refresh(metrics_audit_db_model)
            # Should complete without raising exceptions
            await metrics_collector.collect_instance_metrics_task(
                instance_pyd_model=sample_instance_pyd_model,
                connection=mock_connection,
                metric_config=sample_metric_config,
                metrics_audit_db_model=metrics_audit_db_model,
                interval_start=interval_start,
                interval_end=interval_end,
            )

            # Verify file download was attempted
            mock_download.assert_called_once()

            # Verify audit was updated with FAILED status
            audit_service = MetricsAuditDbService(test_async_session)
            audits = await audit_service.get_instance_metrics_audits(
                metric_name="Stats",
                manager_instance="test-instance-1",
            )
            assert len(audits) == 1
            audit = audits[0]
            await test_async_session.refresh(audit)
            assert audit.collection_status == MetricsCollectionStatus.FAILED
            assert audit.reason == "Error fetching file from remote server"

    @pytest.mark.asyncio
    async def test_metrics_collector_integration_file_operations_exception(
        self,
        metrics_collector,
        sample_instance_pyd_model,
        sample_metric_config,
        test_async_session,
    ):
        """Test metrics collector behavior when file operations raise unexpected exceptions."""
        interval_start = datetime.datetime(2025, 1, 1, 10, 0, 0, tzinfo=datetime.UTC)
        interval_end = datetime.datetime(2025, 1, 1, 10, 5, 0, tzinfo=datetime.UTC)

        # Mock successful SSH connection
        mock_connection = AsyncMock()

        with (
            # Mock file operations to raise an exception
            patch.object(
                metrics_collector.file_ops,
                "download_file_from_remote",
                side_effect=Exception("file operation error"),
            ),
            patch.object(metrics_collector.ssh_manager, "close_connection"),
        ):
            metrics_audit_db_model = await MetricsAuditDbFactory(
                async_session=test_async_session,
                manager_instance="test-instance-1",
                metric_name="Stats",
                interval_start=interval_start,
                interval_end=interval_end,
                collection_status=MetricsCollectionStatus.NOT_STARTED,
                reason="",
                last_attempt=None,
            )
            await test_async_session.commit()
            await test_async_session.refresh(metrics_audit_db_model)
            # Should complete without raising exceptions
            await metrics_collector.collect_instance_metrics_task(
                instance_pyd_model=sample_instance_pyd_model,
                connection=mock_connection,
                metric_config=sample_metric_config,
                metrics_audit_db_model=metrics_audit_db_model,
                interval_start=interval_start,
                interval_end=interval_end,
            )

            # Verify audit was logged with failure status
            audit_service = MetricsAuditDbService(test_async_session)
            audits = await audit_service.get_instance_metrics_audits(
                metric_name="Stats",
                manager_instance="test-instance-1",
                interval_start=interval_start,
                interval_end=interval_end,
            )
            assert len(audits) >= 1
            audit = audits[0]
            await test_async_session.refresh(audit)
            assert audit.collection_status == MetricsCollectionStatus.FAILED
            assert audit.reason == "Unexpected error: file operation error"
