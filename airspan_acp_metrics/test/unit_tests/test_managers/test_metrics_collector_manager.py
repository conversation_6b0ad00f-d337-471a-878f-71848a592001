import datetime
from pathlib import Path
from unittest.mock import MagicMock
from unittest.mock import patch

import pytest
from da_common.models import Status

from airspan_acp_metrics.database.services.metric_audit_db_service import MetricsAuditDbService
from airspan_acp_metrics.database.services.metrics_db_service import MetricsDbService
from airspan_acp_metrics.managers import GCSOperationsManager
from airspan_acp_metrics.managers.metrics_collector_manager import MetricsCollector
from airspan_acp_metrics.pyd_models.metrics_audit_pyd_model import MetricsAuditPydModel
from airspan_acp_metrics.pyd_models.utils import MetricsCollectionStatus
from airspan_acp_metrics.test.unit_tests.test_managers.test_gcs_operations_manager import (
    read_zip_file_to_bytesio,
)


class TestMetricsCollectorManager:
    """Tests for the new status tracking functionality in MetricsCollector."""

    @pytest.fixture
    def metrics_collector(self, test_config):
        """Create a MetricsCollector instance."""
        metrics_collector = MetricsCollector(test_config)
        metrics_collector.pub_sub = MagicMock()
        return metrics_collector

    @pytest.fixture
    def sample_metric_config(self):
        """Create a sample metric configuration."""
        return {
            "metric_name": "Stats",
            "frequency": 5,
            "enabled": True,
            "remote_path": "/var/log/nms/Stats",
            "file_pattern": "*_Stats_*.zip",
        }

    @pytest.fixture
    def sample_zip_file_data(self):
        """Create sample zip file data with multiple files."""
        test_zip_path = (
            Path(__file__).parent.parent.parent
            / "test_data"
            / "acp-dauk-mrl-green-acp-Stats-20250607_0030_0035.zip"
        )
        return read_zip_file_to_bytesio(test_zip_path)

    @pytest.fixture
    def remote_file_metadata(self):
        """Create sample remote file metadata."""
        return {
            # Remote host metadata
            "file_size_bytes": 172335,
            "remote_file_mode": 33188,  # File permissions
            "remote_file_mtime_iso": "2025-06-07T00:35:11+00:00",  # ISO format
            "remote_file_atime_iso": "2025-06-07T00:35:11+00:00",  # ISO format
        }

    @pytest.mark.asyncio
    async def test_file_not_found_updates_audit_entry(
        self,
        metrics_collector,
        sample_instance_pyd_model,
        sample_metric_config,
        test_async_session,
    ):
        """Test that file not found creates metrics audit entry with MISSING status."""
        interval_start = datetime.datetime(2025, 1, 1, 10, 0, 0, tzinfo=datetime.UTC)
        interval_end = datetime.datetime(2025, 1, 1, 10, 5, 0, tzinfo=datetime.UTC)

        mock_connection = MagicMock()

        with (
            patch.object(
                metrics_collector.ssh_manager,
                "get_connection",
                return_value=(
                    mock_connection,
                    {"status": Status.OK, "reason": "SSH connection successful"},
                ),
            ),
            patch.object(
                metrics_collector.file_ops,
                "download_file_from_remote",
                return_value=(None, "File not found", None),  # File not found
            ),
            patch.object(metrics_collector.ssh_manager, "close_connection"),
        ):
            metrics_audit_db_service = MetricsAuditDbService(test_async_session)
            audit_pyd_model = MetricsAuditPydModel(
                manager_instance="test-instance-1",
                metric_name="Stats",
                interval_start=interval_start,
                interval_end=interval_end,
                collection_status=MetricsCollectionStatus.NOT_STARTED,
                attempt_count=1,
            )
            metrics_audit_db_model = await metrics_audit_db_service.create_audit(
                audit_pyd_model
            )
            await test_async_session.commit()
            await test_async_session.refresh(metrics_audit_db_model)
            audit_id = metrics_audit_db_model.id

            await metrics_collector.collect_instance_metrics_task(
                instance_pyd_model=sample_instance_pyd_model,
                connection=mock_connection,
                metric_config=sample_metric_config,
                metrics_audit_db_model=metrics_audit_db_model,
                interval_start=interval_start,
                interval_end=interval_end,
            )

            # Verify metrics audit was logged for file not found
            audit_service = MetricsAuditDbService(test_async_session)
            metric_name = sample_metric_config["metric_name"]
            audits = await audit_service.get_instance_metrics_audits(
                metric_name=metric_name,
                manager_instance="test-instance-1",
                interval_start=interval_start,
                interval_end=interval_end,
            )
            assert len(audits) >= 1
            # Verify the audit record was updated rather than a new one created
            audit = audits[0]
            await test_async_session.refresh(audit)
            assert audit.id == audit_id
            assert audit.collection_status == MetricsCollectionStatus.FAILED
            assert audit.reason == "File not found"

            assert metrics_collector.pub_sub.push_payload.assert_not_called, (
                "publish_metrics was called"
            )

    @pytest.mark.asyncio
    async def test_gcs_upload_failure_updates_audit_entry(
        self,
        metrics_collector,
        sample_instance_pyd_model,
        sample_metric_config,
        test_async_session,
        sample_zip_file_data,
        remote_file_metadata,
    ):
        """Test that GCS upload failure creates metrics audit entry with FAILED status."""
        interval_start = datetime.datetime(2025, 1, 1, 10, 0, 0, tzinfo=datetime.UTC)
        interval_end = datetime.datetime(2025, 1, 1, 10, 5, 0, tzinfo=datetime.UTC)

        mock_connection = MagicMock()

        with (
            patch.object(
                metrics_collector.ssh_manager,
                "get_connection",
                return_value=(
                    mock_connection,
                    {"status": Status.OK, "reason": "SSH connection successful"},
                ),
            ),
            patch.object(
                metrics_collector.file_ops,
                "download_file_from_remote",
                return_value=(sample_zip_file_data, "", remote_file_metadata),
            ),
            patch.object(
                metrics_collector.gcs_ops,
                "upload_file_to_gcs",
                return_value=(None, None),  # GCS upload failed
            ),
            patch.object(metrics_collector.ssh_manager, "close_connection"),
        ):
            audit_db_service = MetricsAuditDbService(test_async_session)
            audit_pyd_model = MetricsAuditPydModel(
                manager_instance="test-instance-1",
                metric_name="Stats",
                interval_start=interval_start,
                interval_end=interval_end,
                collection_status=MetricsCollectionStatus.NOT_STARTED,
                attempt_count=1,
            )
            metrics_audit_db_model = await audit_db_service.create_audit(audit_pyd_model)
            await test_async_session.commit()
            await test_async_session.refresh(metrics_audit_db_model)
            audit_id = metrics_audit_db_model.id

            await metrics_collector.collect_instance_metrics_task(
                instance_pyd_model=sample_instance_pyd_model,
                connection=mock_connection,
                metric_config=sample_metric_config,
                metrics_audit_db_model=metrics_audit_db_model,
                interval_start=interval_start,
                interval_end=interval_end,
            )

            # Verify metrics audit was logged for GCS upload failure
            audit_service = MetricsAuditDbService(test_async_session)
            metric_name = sample_metric_config["metric_name"]
            audits = await audit_service.get_instance_metrics_audits(
                metric_name=metric_name,
                manager_instance="test-instance-1",
                interval_start=interval_start,
                interval_end=interval_end,
            )
            assert len(audits) >= 1
            audit = audits[0]
            await test_async_session.refresh(audit)
            assert audit.id == audit_id
            assert audits[0].collection_status == MetricsCollectionStatus.FAILED
            assert "GCS upload failed" in audits[0].reason

            assert metrics_collector.pub_sub.push_payload.assert_not_called, (
                "publish_metrics was called"
            )

    @pytest.mark.asyncio
    async def test_file_processing_exception_updates_audit_entry(
        self,
        metrics_collector,
        sample_instance_pyd_model,
        sample_metric_config,
        test_async_session,
    ):
        """Test that file processing exceptions create metrics audit entry with FAILED status."""
        interval_start = datetime.datetime(2025, 1, 1, 10, 0, 0, tzinfo=datetime.UTC)
        interval_end = datetime.datetime(2025, 1, 1, 10, 5, 0, tzinfo=datetime.UTC)

        mock_connection = MagicMock()

        with (
            patch.object(
                metrics_collector.ssh_manager,
                "get_connection",
                return_value=(
                    mock_connection,
                    {"status": Status.OK, "reason": "SSH connection successful"},
                ),
            ),
            patch.object(
                metrics_collector.file_ops,
                "download_file_from_remote",
                return_value=(None, "File processing error", None),
            ),
            patch.object(metrics_collector.ssh_manager, "close_connection"),
        ):
            audit_db_service = MetricsAuditDbService(test_async_session)
            audit_pyd_model = MetricsAuditPydModel(
                manager_instance="test-instance-1",
                metric_name="Stats",
                interval_start=interval_start,
                interval_end=interval_end,
                collection_status=MetricsCollectionStatus.NOT_STARTED,
                attempt_count=1,
            )
            metrics_audit_db_model = await audit_db_service.create_audit(audit_pyd_model)
            await test_async_session.commit()
            await test_async_session.refresh(metrics_audit_db_model)
            audit_id = metrics_audit_db_model.id

            await metrics_collector.collect_instance_metrics_task(
                instance_pyd_model=sample_instance_pyd_model,
                connection=mock_connection,
                metric_config=sample_metric_config,
                metrics_audit_db_model=metrics_audit_db_model,
                interval_start=interval_start,
                interval_end=interval_end,
            )

            # Verify metrics audit was logged for file processing error
            audit_service = MetricsAuditDbService(test_async_session)
            metric_name = sample_metric_config["metric_name"]
            audits = await audit_service.get_instance_metrics_audits(
                metric_name=metric_name,
                manager_instance="test-instance-1",
                interval_start=interval_start,
                interval_end=interval_end,
            )
            assert len(audits) >= 1
            audit = audits[0]
            await test_async_session.refresh(audit)
            assert audit.id == audit_id
            assert audit.collection_status == MetricsCollectionStatus.FAILED
            assert audit.reason == "File processing error"

            assert metrics_collector.pub_sub.push_payload.assert_not_called, (
                "publish_metrics was called"
            )

    @pytest.mark.asyncio
    async def test_successful_flow_creates_metrics_and_update_audit_entries(
        self,
        metrics_collector,
        sample_instance_pyd_model,
        sample_metric_config,
        test_async_session,
        sample_zip_file_data,
        remote_file_metadata,
    ):
        """Test that successful flow creates both metrics and audit entries."""
        interval_start = datetime.datetime(2025, 1, 1, 10, 0, 0, tzinfo=datetime.UTC)
        interval_end = datetime.datetime(2025, 1, 1, 10, 5, 0, tzinfo=datetime.UTC)

        mock_connection = MagicMock()
        mock_metrics_model = MagicMock()
        mock_metrics_model.id = "test-metrics-id"
        mock_file_metadata = {
            "file_count": 233,
            "file_hash_sha256": GCSOperationsManager._calculate_file_hash(sample_zip_file_data),
            "retry_attempts": 1,
            "successful_attempts": 1,
            **remote_file_metadata,
        }

        mock_gcs_path = "test-instance-1/Stats/20250101_1000_1005.zip"

        with (
            patch.object(
                metrics_collector.ssh_manager,
                "get_connection",
                return_value=(
                    mock_connection,
                    {"status": Status.OK, "reason": "SSH connection successful"},
                ),
            ),
            patch.object(
                metrics_collector.file_ops,
                "download_file_from_remote",
                return_value=(sample_zip_file_data, "", remote_file_metadata),
            ),
            patch.object(
                metrics_collector.gcs_ops,
                "upload_file_to_gcs",
                return_value=(mock_file_metadata, mock_gcs_path),
            ),
            patch.object(metrics_collector.ssh_manager, "close_connection"),
        ):
            audit_db_service = MetricsAuditDbService(test_async_session)
            audit_pyd_model = MetricsAuditPydModel(
                manager_instance="test-instance-1",
                metric_name="Stats",
                interval_start=interval_start,
                interval_end=interval_end,
                collection_status=MetricsCollectionStatus.NOT_STARTED,
                attempt_count=1,
            )
            metrics_audit_db_model = await audit_db_service.create_audit(audit_pyd_model)
            await test_async_session.commit()
            await test_async_session.refresh(metrics_audit_db_model)
            audit_id = metrics_audit_db_model.id

            await metrics_collector.collect_instance_metrics_task(
                instance_pyd_model=sample_instance_pyd_model,
                connection=mock_connection,
                metric_config=sample_metric_config,
                metrics_audit_db_model=metrics_audit_db_model,
                interval_start=interval_start,
                interval_end=interval_end,
            )

            # Verify metrics record was created
            metrics_service = MetricsDbService(test_async_session)
            all_metrics = await metrics_service.get_metrics_by_interval(
                manager_instance="test-instance-1",
                metric_name="Stats",
                interval_start=interval_start,
                interval_end=interval_end,
            )
            assert len(all_metrics) == 1
            metrics = all_metrics[0]
            assert metrics.interval_start == interval_start
            assert metrics.interval_end == interval_end
            assert metrics.gcs_file_path == mock_gcs_path

            # Verify metrics audit was logged for successful collection
            audit_service = MetricsAuditDbService(test_async_session)
            metric_name = sample_metric_config["metric_name"]
            audits = await audit_service.get_instance_metrics_audits(
                metric_name=metric_name,
                manager_instance="test-instance-1",
                interval_start=interval_start,
                interval_end=interval_end,
            )
            assert len(audits) >= 1
            audit = audits[0]
            await test_async_session.refresh(audit)
            assert audit.id == audit_id
            assert audit.collection_status == MetricsCollectionStatus.COLLECTED
            assert audit.metrics_id == metrics.id
            assert audit.reason == "Metrics collection completed successfully"

            assert metrics_collector.pub_sub.push_payload.called, (
                "publish_metrics was not called"
            )
            assert metrics_collector.pub_sub.push_payload.call_count == 1, (
                "publish_metrics was called multiple times"
            )

    @pytest.mark.asyncio
    async def test_collect_instance_metrics_task_multiple_calls_field_updates(
        self,
        metrics_collector,
        sample_instance_pyd_model,
        sample_metric_config,
        test_async_session,
        sample_zip_file_data,
        remote_file_metadata,
    ):
        """Test calling collect_instance_metrics_task 3 times and verify field updates each time."""
        interval_start = datetime.datetime(2025, 1, 1, 10, 0, 0, tzinfo=datetime.UTC)
        interval_end = datetime.datetime(2025, 1, 1, 10, 5, 0, tzinfo=datetime.UTC)

        mock_connection = MagicMock()

        # Create initial audit record
        audit_db_service = MetricsAuditDbService(test_async_session)
        audit_pyd_model = MetricsAuditPydModel(
            manager_instance="test-instance-1",
            metric_name="Stats",
            interval_start=interval_start,
            interval_end=interval_end,
            collection_status=MetricsCollectionStatus.RUNNING,
            attempt_count=1,
            last_attempt=datetime.datetime.now(datetime.UTC),
        )
        metrics_audit_db_model = await audit_db_service.create_audit(audit_pyd_model)
        await test_async_session.commit()
        await test_async_session.refresh(metrics_audit_db_model)
        audit_id = metrics_audit_db_model.id

        # First call - file operation fails
        with patch.object(
            metrics_collector.file_ops,
            "download_file_from_remote",
            return_value=(None, "File not found on attempt 1", None),
        ):
            await metrics_collector.collect_instance_metrics_task(
                instance_pyd_model=sample_instance_pyd_model,
                connection=mock_connection,
                metric_config=sample_metric_config,
                metrics_audit_db_model=metrics_audit_db_model,
                interval_start=interval_start,
                interval_end=interval_end,
                prefix_log="test-prefix-attempt-1",
            )

            # Verify first attempt updates
            audit_service = MetricsAuditDbService(test_async_session)
            audits = await audit_service.get_instance_metrics_audits(
                metric_name="Stats",
                manager_instance="test-instance-1",
                interval_start=interval_start,
                interval_end=interval_end,
            )
            audit = audits[0]
            await test_async_session.refresh(audit)

            assert audit.id == audit_id
            assert audit.collection_status == MetricsCollectionStatus.FAILED
            assert audit.metrics_id is None
            assert audit.file_metadata is None
            assert audit.reason == "File not found on attempt 1"
            assert audit.attempt_count == 1
            assert metrics_collector.pub_sub.push_payload.assert_not_called, (
                "publish_metrics was called"
            )

        # Second call - GCS operation fails
        with (
            patch.object(
                metrics_collector.file_ops,
                "download_file_from_remote",
                return_value=(sample_zip_file_data, "", remote_file_metadata),
            ),
            patch.object(
                metrics_collector.gcs_ops,
                "upload_file_to_gcs",
                return_value=(None, None),  # GCS fails
            ),
        ):
            # Refresh the audit model for the second call
            await test_async_session.refresh(metrics_audit_db_model)
            metrics_audit_db_model.attempt_count = 2
            metrics_audit_db_model.last_attempt = datetime.datetime.now(datetime.UTC)
            metrics_audit_db_model.collection_status = MetricsCollectionStatus.RUNNING
            await test_async_session.commit()
            await test_async_session.refresh(metrics_audit_db_model)

            await metrics_collector.collect_instance_metrics_task(
                instance_pyd_model=sample_instance_pyd_model,
                connection=mock_connection,
                metric_config=sample_metric_config,
                metrics_audit_db_model=metrics_audit_db_model,
                interval_start=interval_start,
                interval_end=interval_end,
                prefix_log="test-prefix-attempt-2",
            )

            # Verify second attempt updates
            audits = await audit_service.get_instance_metrics_audits(
                metric_name="Stats",
                manager_instance="test-instance-1",
                interval_start=interval_start,
                interval_end=interval_end,
            )
            audit = audits[0]
            await test_async_session.refresh(audit)

            assert audit.id == audit_id
            assert audit.collection_status == MetricsCollectionStatus.FAILED
            assert audit.metrics_id is None
            assert audit.file_metadata is None
            assert audit.reason == "GCS upload failed"
            assert audit.attempt_count == 2
            assert metrics_collector.pub_sub.push_payload.assert_not_called, (
                "publish_metrics was called"
            )

        # Third call - successful
        mock_file_metadata = {"file_size_bytes": 172335, "file_count": 233}
        with (
            patch.object(
                metrics_collector.file_ops,
                "download_file_from_remote",
                return_value=(sample_zip_file_data, "", remote_file_metadata),
            ),
            patch.object(
                metrics_collector.gcs_ops,
                "upload_file_to_gcs",
                return_value=(
                    mock_file_metadata,
                    "test-instance-1/Stats/20250101_1000_1005.zip",
                ),
            ),
        ):
            # Refresh the audit model for the third call
            await test_async_session.refresh(metrics_audit_db_model)
            metrics_audit_db_model.attempt_count = 3
            metrics_audit_db_model.last_attempt = datetime.datetime.now(datetime.UTC)
            metrics_audit_db_model.collection_status = MetricsCollectionStatus.RUNNING
            await test_async_session.commit()
            await test_async_session.refresh(metrics_audit_db_model)

            await metrics_collector.collect_instance_metrics_task(
                instance_pyd_model=sample_instance_pyd_model,
                connection=mock_connection,
                metric_config=sample_metric_config,
                metrics_audit_db_model=metrics_audit_db_model,
                interval_start=interval_start,
                interval_end=interval_end,
                prefix_log="test-prefix-attempt-3",
            )

            # Verify third attempt (successful) updates
            audits = await audit_service.get_instance_metrics_audits(
                metric_name="Stats",
                manager_instance="test-instance-1",
                interval_start=interval_start,
                interval_end=interval_end,
            )
            audit = audits[0]
            await test_async_session.refresh(audit)

            assert audit.id == audit_id
            assert audit.collection_status == MetricsCollectionStatus.COLLECTED
            assert audit.metrics_id is not None
            assert audit.file_metadata == mock_file_metadata
            assert audit.reason == "Metrics collection completed successfully"
            assert audit.attempt_count == 3
            assert metrics_collector.pub_sub.push_payload.assert_called, (
                "publish_metrics was not called"
            )
            assert metrics_collector.pub_sub.push_payload.call_count == 1, (
                "publish_metrics was called multiple times"
            )
