import datetime
import uuid

import pytest

from airspan_acp_metrics.database.services.metric_audit_db_service import MetricsAuditDbService
from airspan_acp_metrics.pyd_models.metrics_audit_pyd_model import MetricsAuditPydModel
from airspan_acp_metrics.pyd_models.metrics_audit_pyd_model import UpdateMetricsAuditPydModel
from airspan_acp_metrics.pyd_models.utils import MetricsCollectionStatus
from airspan_acp_metrics.test.factories.metrics_audit_db_factory import MetricsAuditDbFactory


class TestMetricsAudit:
    @pytest.mark.asyncio
    async def test_create_audit_success(self, test_async_session_maker):
        async with test_async_session_maker() as db_session:
            metrics_audit_db_service = MetricsAuditDbService(db_session)

            # Create audit using Pydantic model
            interval_start = datetime.datetime.now(datetime.UTC)
            interval_end = interval_start + datetime.timedelta(minutes=5)
            last_attempt = datetime.datetime.now(datetime.UTC)

            metrics_audit_pyd_model = MetricsAuditPydModel(
                manager_instance="test-instance",
                metric_name="test-metric",
                file_metadata={"file_size_bytes": 1024, "file_count": 1},
                interval_start=interval_start,
                interval_end=interval_end,
                collection_status=MetricsCollectionStatus.COLLECTED,
                last_attempt=last_attempt,
                attempt_count=1,
                reason="",
                updated_at=datetime.datetime.now(datetime.UTC),
                metrics_id=None,
            )

            created_audit = await metrics_audit_db_service.create_audit(metrics_audit_pyd_model)
            await db_session.commit()
            await db_session.refresh(created_audit)

            # Verify audit was created
            assert created_audit.id is not None
            assert isinstance(created_audit.id, uuid.UUID)
            assert created_audit.manager_instance == "test-instance"
            assert created_audit.file_metadata == {"file_size_bytes": 1024, "file_count": 1}
            assert created_audit.interval_start == interval_start
            assert created_audit.interval_end == interval_end
            assert created_audit.collection_status == MetricsCollectionStatus.COLLECTED
            assert created_audit.last_attempt == last_attempt
            assert created_audit.attempt_count == 1
            assert created_audit.reason == ""
            assert created_audit.created_at is not None
            assert created_audit.updated_at is not None
            assert created_audit.metrics_id is None

    @pytest.mark.asyncio
    async def test_create_audit_using_factory(self, test_async_session_maker):
        async with test_async_session_maker() as db_session:
            # Create audit using factory
            interval_start = datetime.datetime.now(datetime.UTC)
            interval_end = interval_start + datetime.timedelta(minutes=5)

            audit = await MetricsAuditDbFactory(
                async_session=db_session,
                manager_instance="factory-instance",
                file_metadata={"file_size_bytes": 110000, "file_count": 30},
                interval_start=interval_start,
                interval_end=interval_end,
                collection_status=MetricsCollectionStatus.FAILED,
                last_attempt=datetime.datetime.now(datetime.UTC),
                attempt_count=3,
                reason="Connection timeout",
                metrics_id=None,
            )

            # Verify factory-created audit
            assert audit.id is not None
            assert isinstance(audit.id, uuid.UUID)
            assert audit.manager_instance == "factory-instance"
            assert audit.file_metadata == {"file_size_bytes": 110000, "file_count": 30}
            assert audit.interval_start == interval_start
            assert audit.interval_end == interval_end
            assert audit.collection_status == MetricsCollectionStatus.FAILED
            assert audit.attempt_count == 3
            assert audit.reason == "Connection timeout"
            assert audit.created_at is not None
            assert audit.updated_at is not None

    @pytest.mark.asyncio
    async def test_update_audit_success(self, test_async_session_maker):
        async with test_async_session_maker() as db_session:
            metrics_audit_db_service = MetricsAuditDbService(db_session)

            # Create initial audit
            interval_start = datetime.datetime.now(datetime.UTC)
            interval_end = interval_start + datetime.timedelta(minutes=5)

            metrics_audit_pyd_model = MetricsAuditPydModel(
                manager_instance="update-test-instance",
                metric_name="test-metric",
                file_metadata=None,
                interval_start=interval_start,
                interval_end=interval_end,
                collection_status=MetricsCollectionStatus.NOT_STARTED,
                last_attempt=datetime.datetime.now(datetime.UTC),
                attempt_count=1,
                reason="",
                updated_at=datetime.datetime.now(datetime.UTC),
                metrics_id=None,
            )

            created_audit = await metrics_audit_db_service.create_audit(metrics_audit_pyd_model)
            await db_session.commit()
            await db_session.refresh(created_audit)
            update_metrics_audit_pyd_model = UpdateMetricsAuditPydModel(
                collection_status=MetricsCollectionStatus.COLLECTED,
                file_metadata={"file_size_bytes": 4096, "file_count": 1},
                attempt_count=1,
                reason="Successfully collected",
                updated_at=datetime.datetime.now(datetime.UTC),
            )
            # Update the audit
            updated_audit = await metrics_audit_db_service.update_audit(
                audit_db_model=created_audit,
                update_metrics_audit_pyd_model=update_metrics_audit_pyd_model,
            )
            await db_session.commit()
            await db_session.refresh(updated_audit)

            # Verify updates
            assert updated_audit is not None
            assert updated_audit.id == created_audit.id
            assert updated_audit.collection_status == MetricsCollectionStatus.COLLECTED
            assert updated_audit.file_metadata == {"file_size_bytes": 4096, "file_count": 1}
            assert updated_audit.attempt_count == 1
            assert updated_audit.reason == "Successfully collected"
            assert updated_audit.manager_instance == "update-test-instance"

    @pytest.mark.asyncio
    async def test_get_instance_metrics_audits_success(
        self, test_async_session_maker, test_config
    ):
        async with test_async_session_maker() as db_session:
            for metric_name, _ in test_config.data["metrics"].items():
                metrics_audit_db_service = MetricsAuditDbService(db_session)

                # Create multiple audits for the same instance
                base_time = datetime.datetime.now(datetime.UTC)
                manager_instance = "get-audits-test-instance"

                audit_data = [
                    (
                        base_time,
                        base_time + datetime.timedelta(minutes=5),
                        MetricsCollectionStatus.COLLECTED,
                    ),
                    (
                        base_time + datetime.timedelta(minutes=5),
                        base_time + datetime.timedelta(minutes=10),
                        MetricsCollectionStatus.FAILED,
                    ),
                    (
                        base_time + datetime.timedelta(minutes=10),
                        base_time + datetime.timedelta(minutes=15),
                        MetricsCollectionStatus.NOT_STARTED,
                    ),
                ]

                for interval_start, interval_end, status in audit_data:
                    audit_pyd_model = MetricsAuditPydModel(
                        manager_instance=manager_instance,
                        metric_name=metric_name,
                        file_metadata={"file_size_bytes": 1024, "file_count": 1}
                        if status == MetricsCollectionStatus.COLLECTED
                        else None,
                        interval_start=interval_start,
                        interval_end=interval_end,
                        collection_status=status,
                        last_attempt=datetime.datetime.now(datetime.UTC),
                        attempt_count=1,
                        reason="",
                        updated_at=datetime.datetime.now(datetime.UTC),
                        metrics_id=None,
                    )
                    await metrics_audit_db_service.create_audit(audit_pyd_model)

                await db_session.commit()

                # Get all audits for the instance
                retrieved_audits = await metrics_audit_db_service.get_instance_metrics_audits(
                    manager_instance=manager_instance,
                    metric_name=metric_name,
                    interval_start=base_time,
                    interval_end=base_time + datetime.timedelta(minutes=15),
                )

                # Verify all audits are returned (ordered by interval_start desc)
                assert len(retrieved_audits) == 3
                assert all(
                    audit.manager_instance == manager_instance for audit in retrieved_audits
                )

                # Verify ordering (newest first)
                for i in range(len(retrieved_audits) - 1):
                    assert (
                        retrieved_audits[i].interval_start
                        >= retrieved_audits[i + 1].interval_start
                    )

    @pytest.mark.asyncio
    async def test_get_pending_backfills(self, test_async_session_maker):
        async with test_async_session_maker() as db_session:
            metrics_audit_db_service = MetricsAuditDbService(db_session)

            # Create audits with different statuses
            base_time = datetime.datetime.now(datetime.UTC)
            manager_instance = "backfill-test-instance"

            audit_statuses = [
                MetricsCollectionStatus.COLLECTED,  # Should not be in backfill
                MetricsCollectionStatus.NOT_STARTED,  # Should be in backfill
                MetricsCollectionStatus.FAILED,  # Should be in backfill
                MetricsCollectionStatus.METRICS_EMPTY,  # Should be in backfill
            ]

            created_audits = []
            for i, status in enumerate(audit_statuses):
                interval_start = base_time + datetime.timedelta(minutes=i * 5)
                interval_end = interval_start + datetime.timedelta(minutes=5)

                audit_pyd_model = MetricsAuditPydModel(
                    manager_instance=manager_instance,
                    metric_name="test-metric",
                    file_metadata={"file_size_bytes": 1024, "file_count": 1}
                    if status == MetricsCollectionStatus.COLLECTED
                    else None,
                    interval_start=interval_start,
                    interval_end=interval_end,
                    collection_status=status,
                    last_attempt=datetime.datetime.now(datetime.UTC),
                    attempt_count=1,
                    reason="",
                    updated_at=datetime.datetime.now(datetime.UTC),
                    metrics_id=None,
                )
                audit = await metrics_audit_db_service.create_audit(audit_pyd_model)
                created_audits.append(audit)

            await db_session.commit()

            # Get pending backfills
            pending_backfills = await metrics_audit_db_service.get_pending_metrics_audits(
                manager_instance, max_audits_to_process=10, max_retry_attempts=3
            )

            # Should return only MISSING, FAILED, and METRICS_EMPTY audits
            assert len(pending_backfills) == 3
            expected_statuses = {
                MetricsCollectionStatus.NOT_STARTED,
                MetricsCollectionStatus.FAILED,
                MetricsCollectionStatus.METRICS_EMPTY,
            }
            actual_statuses = {audit.collection_status for audit in pending_backfills}
            assert actual_statuses == expected_statuses

    @pytest.mark.asyncio
    async def test_metrics_audit_timestamps_auto_generated(self, test_async_session_maker):
        async with test_async_session_maker() as db_session:
            metrics_audit_db_service = MetricsAuditDbService(db_session)

            interval_start = datetime.datetime.now(datetime.UTC)
            interval_end = interval_start + datetime.timedelta(minutes=5)

            metrics_audit_pyd_model = MetricsAuditPydModel(
                manager_instance="timestamp-test-instance",
                metric_name="test-metric",
                file_metadata={"file_size_bytes": 1024, "file_count": 1},
                interval_start=interval_start,
                interval_end=interval_end,
                collection_status=MetricsCollectionStatus.COLLECTED,
                last_attempt=datetime.datetime.now(datetime.UTC),
                attempt_count=1,
                reason="",
                updated_at=datetime.datetime.now(datetime.UTC),
                metrics_id=None,
            )

            created_audit = await metrics_audit_db_service.create_audit(metrics_audit_pyd_model)
            await db_session.commit()
            await db_session.refresh(created_audit)

            # Verify timestamps are auto-generated by database
            assert created_audit.created_at is not None
            assert created_audit.updated_at is not None
            assert isinstance(created_audit.created_at, datetime.datetime)
            assert isinstance(created_audit.updated_at, datetime.datetime)
            assert created_audit.created_at.tzinfo is not None  # Should have timezone info
            assert created_audit.updated_at.tzinfo is not None  # Should have timezone info
            assert created_audit.last_attempt.tzinfo is not None  # Should have timezone info

    @pytest.mark.asyncio
    async def test_metrics_audit_collection_status_enum(self, test_async_session_maker):
        async with test_async_session_maker() as db_session:
            metrics_audit_db_service = MetricsAuditDbService(db_session)

            # Test all collection status values
            interval_start = datetime.datetime.now(datetime.UTC)
            interval_end = interval_start + datetime.timedelta(minutes=5)

            all_statuses = [
                MetricsCollectionStatus.NOT_STARTED,
                MetricsCollectionStatus.RUNNING,
                MetricsCollectionStatus.COLLECTED,
                MetricsCollectionStatus.FAILED,
                MetricsCollectionStatus.METRICS_EMPTY,
            ]

            created_audits = []
            for i, status in enumerate(all_statuses):
                audit_pyd_model = MetricsAuditPydModel(
                    manager_instance=f"status-test-instance-{i}",
                    metric_name="test-metric",
                    file_metadata={"file_size_bytes": 1024, "file_count": 1}
                    if status == MetricsCollectionStatus.COLLECTED
                    else None,
                    interval_start=interval_start + datetime.timedelta(minutes=i),
                    interval_end=interval_end + datetime.timedelta(minutes=i),
                    collection_status=status,
                    last_attempt=datetime.datetime.now(datetime.UTC),
                    attempt_count=1,
                    reason="",
                    updated_at=datetime.datetime.now(datetime.UTC),
                    metrics_id=None,
                )

                audit = await metrics_audit_db_service.create_audit(audit_pyd_model)
                created_audits.append(audit)

            await db_session.commit()

            # Verify all statuses were stored correctly
            for i, audit in enumerate(created_audits):
                await db_session.refresh(audit)
                assert audit.collection_status == all_statuses[i]

    @pytest.mark.asyncio
    async def test_metrics_audit_foreign_key_relationship(self, test_async_session_maker):
        async with test_async_session_maker() as db_session:
            from airspan_acp_metrics.database.services.metrics_db_service import (
                MetricsDbService,
            )
            from airspan_acp_metrics.pyd_models.metrics_pyd_model import MetricsPydModel

            metrics_audit_db_service = MetricsAuditDbService(db_session)
            metrics_db_service = MetricsDbService(db_session)

            # First create a valid metrics record
            interval_start = datetime.datetime.now(datetime.UTC)
            interval_end = interval_start + datetime.timedelta(minutes=5)

            metrics_pyd_model = MetricsPydModel(
                manager_instance="fk-test-instance",
                metric_name="test-metric",
                gcs_file_path="test/path/file.zip",
                interval_start=interval_start,
                interval_end=interval_end,
            )

            created_metrics = await metrics_db_service.create_metrics(metrics_pyd_model)
            await db_session.commit()
            await db_session.refresh(created_metrics)

            # Store the metrics ID to avoid session detachment issues
            metrics_id = created_metrics.id

            # Now create audit with valid metrics_id (foreign key)
            metrics_audit_pyd_model = MetricsAuditPydModel(
                manager_instance="fk-test-instance",
                metric_name="test-metric",
                file_metadata={"file_size_bytes": 1024, "file_count": 1},
                interval_start=interval_start,
                interval_end=interval_end,
                collection_status=MetricsCollectionStatus.COLLECTED,
                last_attempt=datetime.datetime.now(datetime.UTC),
                attempt_count=1,
                reason="",
                updated_at=datetime.datetime.now(datetime.UTC),
                metrics_id=metrics_id,
            )

            created_audit = await metrics_audit_db_service.create_audit(metrics_audit_pyd_model)
            await db_session.commit()
            await db_session.refresh(created_audit)

            # Verify foreign key is stored correctly
            assert created_audit.metrics_id == metrics_id
            assert isinstance(created_audit.metrics_id, uuid.UUID)

    @pytest.mark.asyncio
    async def test_metrics_audit_nullable_fields(self, test_async_session_maker):
        async with test_async_session_maker() as db_session:
            metrics_audit_db_service = MetricsAuditDbService(db_session)

            # Create audit with nullable fields set to None
            interval_start = datetime.datetime.now(datetime.UTC)
            interval_end = interval_start + datetime.timedelta(minutes=5)

            metrics_audit_pyd_model = MetricsAuditPydModel(
                manager_instance="nullable-test-instance",
                metric_name="test-metric",
                file_metadata=None,  # Nullable
                interval_start=interval_start,
                interval_end=interval_end,
                collection_status=MetricsCollectionStatus.NOT_STARTED,
                last_attempt=datetime.datetime.now(datetime.UTC),
                attempt_count=1,
                reason="",
                updated_at=datetime.datetime.now(datetime.UTC),
                metrics_id=None,  # Nullable
            )

            created_audit = await metrics_audit_db_service.create_audit(metrics_audit_pyd_model)
            await db_session.commit()
            await db_session.refresh(created_audit)

            # Verify nullable fields are handled correctly
            assert created_audit.file_metadata is None
            assert created_audit.metrics_id is None
            assert created_audit.manager_instance is not None  # Non-nullable
            assert created_audit.collection_status is not None  # Non-nullable

    @pytest.mark.asyncio
    async def test_metrics_audit_repr_method(self, test_async_session_maker):
        async with test_async_session_maker() as db_session:
            metrics_audit_db_service = MetricsAuditDbService(db_session)

            interval_start = datetime.datetime.now(datetime.UTC)
            interval_end = interval_start + datetime.timedelta(minutes=5)

            metrics_audit_pyd_model = MetricsAuditPydModel(
                manager_instance="repr-test-instance",
                metric_name="test-metric",
                file_metadata={"file_size_bytes": 1024, "file_count": 1},
                interval_start=interval_start,
                interval_end=interval_end,
                collection_status=MetricsCollectionStatus.COLLECTED,
                last_attempt=datetime.datetime.now(datetime.UTC),
                attempt_count=1,
                reason="",
                updated_at=datetime.datetime.now(datetime.UTC),
                metrics_id=None,
            )

            created_audit = await metrics_audit_db_service.create_audit(metrics_audit_pyd_model)
            await db_session.commit()
            await db_session.refresh(created_audit)

            # Test __repr__ method
            repr_str = repr(created_audit)
            assert "MetricsAuditDbModel" in repr_str
            assert str(created_audit.id) in repr_str
            assert "repr-test-instance" in repr_str
            assert str(interval_start) in repr_str
            assert str(interval_end) in repr_str
            assert MetricsCollectionStatus.COLLECTED in repr_str

    @pytest.mark.asyncio
    async def test_metrics_audit_attempt_count_tracking(self, test_async_session_maker):
        async with test_async_session_maker() as db_session:
            metrics_audit_db_service = MetricsAuditDbService(db_session)

            # Create audit with initial attempt count
            interval_start = datetime.datetime.now(datetime.UTC)
            interval_end = interval_start + datetime.timedelta(minutes=5)

            metrics_audit_pyd_model = MetricsAuditPydModel(
                manager_instance="attempt-count-test-instance",
                metric_name="test-metric",
                file_metadata=None,
                interval_start=interval_start,
                interval_end=interval_end,
                collection_status=MetricsCollectionStatus.FAILED,
                last_attempt=datetime.datetime.now(datetime.UTC),
                attempt_count=1,
                reason="First attempt failed",
                updated_at=datetime.datetime.now(datetime.UTC),
                metrics_id=None,
            )

            created_audit = await metrics_audit_db_service.create_audit(metrics_audit_pyd_model)
            await db_session.commit()
            await db_session.refresh(created_audit)

            # Simulate retry attempts
            for attempt in range(2, 5):  # Attempts 2, 3, 4
                update_metrics_audit_pyd_model = UpdateMetricsAuditPydModel(
                    attempt_count=attempt,
                    last_attempt=datetime.datetime.now(datetime.UTC),
                    reason=f"Attempt {attempt} failed",
                    updated_at=datetime.datetime.now(datetime.UTC),
                    collection_status=MetricsCollectionStatus.FAILED,
                )
                updated_audit = await metrics_audit_db_service.update_audit(
                    audit_db_model=created_audit,
                    update_metrics_audit_pyd_model=update_metrics_audit_pyd_model,
                )
                await db_session.commit()
                await db_session.refresh(updated_audit)

                assert updated_audit is not None
                assert updated_audit.attempt_count == attempt
                assert f"Attempt {attempt} failed" in updated_audit.reason

            # Final successful attempt
            update_metrics_audit_pyd_model = UpdateMetricsAuditPydModel(
                attempt_count=5,
                collection_status=MetricsCollectionStatus.COLLECTED,
                file_metadata={"file_size_bytes": 2048, "file_count": 1},
                reason="Successfully collected on attempt 5",
                updated_at=datetime.datetime.now(datetime.UTC),
            )
            final_audit = await metrics_audit_db_service.update_audit(
                audit_db_model=created_audit,
                update_metrics_audit_pyd_model=update_metrics_audit_pyd_model,
            )
            await db_session.commit()
            await db_session.refresh(final_audit)

            assert final_audit is not None
            assert final_audit.attempt_count == 5
            assert final_audit.collection_status == MetricsCollectionStatus.COLLECTED
            assert final_audit.file_metadata == {"file_size_bytes": 2048, "file_count": 1}
