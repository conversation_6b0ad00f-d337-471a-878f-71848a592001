import datetime

import pytest

from airspan_acp_metrics.database.services.metric_audit_db_service import MetricsAuditDbService
from airspan_acp_metrics.database.services.metrics_db_service import MetricsDbService
from airspan_acp_metrics.pyd_models.metrics_audit_pyd_model import MetricsAuditPydModel
from airspan_acp_metrics.pyd_models.metrics_pyd_model import MetricsPydModel
from airspan_acp_metrics.pyd_models.utils import MetricsCollectionStatus
from airspan_acp_metrics.test.factories.metrics_audit_db_factory import MetricsAuditDbFactory
from airspan_acp_metrics.test.factories.metrics_db_factory import MetricsDbFactory


class TestMetricsRelationship:
    """Test the relationship between metrics and metrics_audit tables."""

    @pytest.mark.asyncio
    async def test_metrics_audit_relationship(self, test_async_session_maker):
        """Test that the relationship between metrics and metrics_audit works correctly."""
        async with test_async_session_maker() as db_session:
            # Create a metrics record first
            metrics_service = MetricsDbService(db_session)
            metrics_pyd_model = MetricsPydModel(
                manager_instance="test-instance",
                metric_name="test-metric",
                gcs_file_path="gs://test-bucket/test-file.json",
                interval_start=datetime.datetime.now(datetime.UTC),
                interval_end=datetime.datetime.now(datetime.UTC)
                + datetime.timedelta(minutes=5),
            )
            metrics_record = await metrics_service.create_metrics(metrics_pyd_model)

            # Create an audit record that references the metrics record
            audit_service = MetricsAuditDbService(db_session)
            audit_pyd_model = MetricsAuditPydModel(
                manager_instance="test-instance",
                metric_name="test-metric",
                interval_start=metrics_record.interval_start,
                interval_end=metrics_record.interval_end,
                collection_status=MetricsCollectionStatus.COLLECTED,
                last_attempt=datetime.datetime.now(datetime.UTC),
                attempt_count=1,
                reason="",
                updated_at=datetime.datetime.now(datetime.UTC),
                metrics_id=metrics_record.id,
            )
            audit_record = await audit_service.create_audit(audit_pyd_model)

            await db_session.commit()
            await db_session.refresh(metrics_record)
            await db_session.refresh(audit_record)

            # Test the relationship
            assert audit_record.metrics_id == metrics_record.id
            assert audit_record.metric is not None
            assert audit_record.metric.id == metrics_record.id

            # Explicitly load the audit relationship to avoid lazy loading issues
            from sqlalchemy import select
            from sqlalchemy.orm import selectinload

            from airspan_acp_metrics.database.models.metrics_db_model import MetricsDbModel

            # Re-fetch the metrics record with the audit relationship loaded
            stmt = (
                select(MetricsDbModel)
                .options(selectinload(MetricsDbModel.audit))  # type: ignore
                .where(MetricsDbModel.id == metrics_record.id)
            )
            result = await db_session.execute(stmt)
            metrics_with_audit = result.scalar_one()

            assert len(metrics_with_audit.audit) == 1
            assert metrics_with_audit.audit[0].id == audit_record.id

    @pytest.mark.asyncio
    async def test_factory_creates_audit_without_metrics(self, test_async_session_maker):
        """Test that the factory creates audit records without auto-creating metrics."""
        async with test_async_session_maker() as db_session:
            # Create audit with COLLECTED status - factory doesn't auto-create metrics
            audit_record = await MetricsAuditDbFactory(
                async_session=db_session,
                manager_instance="auto-test-instance",
                metric_name="auto-test-metric",
                collection_status=MetricsCollectionStatus.FAILED,
                interval_start=datetime.datetime.now(datetime.UTC),
                interval_end=datetime.datetime.now(datetime.UTC)
                + datetime.timedelta(minutes=5),
            )

            await db_session.commit()
            await db_session.refresh(audit_record)

            # Verify audit was created but no metrics auto-created
            assert audit_record.metrics_id is None
            assert audit_record.metric is None
            assert audit_record.manager_instance == "auto-test-instance"
            assert audit_record.metric_name == "auto-test-metric"

    @pytest.mark.asyncio
    async def test_factory_creates_missing_status_audit(self, test_async_session_maker):
        """Test that the factory creates audit records with MISSING status."""
        async with test_async_session_maker() as db_session:
            # Create audit with MISSING status
            audit_record = await MetricsAuditDbFactory._create(
                MetricsAuditDbFactory._meta.model,
                async_session=db_session,
                manager_instance="missing-test-instance",
                metric_name="missing-test-metric",
                collection_status=MetricsCollectionStatus.NOT_STARTED,
                interval_start=datetime.datetime.now(datetime.UTC),
                interval_end=datetime.datetime.now(datetime.UTC)
                + datetime.timedelta(minutes=5),
            )

            await db_session.commit()
            await db_session.refresh(audit_record)

            # Verify audit was created with MISSING status
            assert audit_record.metrics_id is None
            assert audit_record.metric is None
            assert audit_record.collection_status == MetricsCollectionStatus.NOT_STARTED

    @pytest.mark.asyncio
    async def test_factory_respects_provided_metrics_id(self, test_async_session_maker):
        """Test that the factory respects explicitly provided metrics_id."""
        async with test_async_session_maker() as db_session:
            # Create a metrics record manually
            metrics_record = await MetricsDbFactory._create(
                MetricsDbFactory._meta.model,
                async_session=db_session,
                manager_instance="manual-test-instance",
                metric_name="manual-test-metric",
                gcs_file_path="gs://test-bucket/manual-test-file.json",
                interval_start=datetime.datetime.now(datetime.UTC),
                interval_end=datetime.datetime.now(datetime.UTC)
                + datetime.timedelta(minutes=5),
            )

            # Create audit with explicit metrics_id
            audit_record = await MetricsAuditDbFactory._create(
                MetricsAuditDbFactory._meta.model,
                async_session=db_session,
                manager_instance="manual-test-instance",
                metric_name="manual-test-metric",
                collection_status=MetricsCollectionStatus.COLLECTED,
                interval_start=metrics_record.interval_start,
                interval_end=metrics_record.interval_end,
                metrics_id=metrics_record.id,
            )

            await db_session.commit()
            await db_session.refresh(audit_record)
            await db_session.refresh(metrics_record)

            # Verify the provided metrics_id was used
            metrics_id = metrics_record.id  # Get the id before any relationship access
            assert audit_record.metrics_id == metrics_id

            # Load the relationship explicitly to avoid async issues
            from sqlalchemy import select
            from sqlalchemy.orm import selectinload

            from airspan_acp_metrics.database.models.metrics_audits_db_model import (
                MetricsAuditDbModel,
            )

            stmt = (
                select(MetricsAuditDbModel)
                .options(selectinload(MetricsAuditDbModel.metric))  # type: ignore
                .where(MetricsAuditDbModel.id == audit_record.id)
            )
            result = await db_session.execute(stmt)
            audit_with_metric = result.scalar_one()

            assert audit_with_metric.metric.id == metrics_id
