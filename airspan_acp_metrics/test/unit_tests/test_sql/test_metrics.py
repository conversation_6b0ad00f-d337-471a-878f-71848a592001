import datetime
import uuid

import pytest

from airspan_acp_metrics.database.services.metrics_db_service import MetricsDbService
from airspan_acp_metrics.pyd_models.metrics_pyd_model import MetricsPydModel
from airspan_acp_metrics.test.factories.metrics_db_factory import MetricsDbFactory


class TestMetrics:
    @pytest.mark.asyncio
    async def test_create_metrics_success(self, test_async_session_maker):
        async with test_async_session_maker() as db_session:
            metrics_db_service = MetricsDbService(db_session)

            # Create metrics using Pydantic model
            interval_start = datetime.datetime.now(datetime.UTC)
            interval_end = interval_start + datetime.timedelta(minutes=5)

            metrics_pyd_model = MetricsPydModel(
                manager_instance="test-instance",
                metric_name="Stats",
                gcs_file_path="acp-metrics/test-instance/Stats/test_file.zip",
                interval_start=interval_start,
                interval_end=interval_end,
            )

            created_metrics = await metrics_db_service.create_metrics(metrics_pyd_model)
            await db_session.commit()
            await db_session.refresh(created_metrics)

            # Verify metrics was created
            assert created_metrics.id is not None
            assert isinstance(created_metrics.id, uuid.UUID)
            assert created_metrics.manager_instance == "test-instance"
            assert created_metrics.metric_name == "Stats"
            assert (
                created_metrics.gcs_file_path == "acp-metrics/test-instance/Stats/test_file.zip"
            )
            assert created_metrics.interval_start == interval_start
            assert created_metrics.interval_end == interval_end
            assert created_metrics.created_at is not None
            assert created_metrics.updated_at is not None

    @pytest.mark.asyncio
    async def test_create_metrics_using_factory(self, test_async_session_maker):
        async with test_async_session_maker() as db_session:
            # Create metrics using factory
            interval_start = datetime.datetime.now(datetime.UTC)
            interval_end = interval_start + datetime.timedelta(minutes=5)

            metrics = await MetricsDbFactory._create(
                MetricsDbFactory._meta.model,
                async_session=db_session,
                manager_instance="factory-instance",
                metric_name="Performance",
                gcs_file_path="acp-metrics/factory-instance/Performance/factory_file.zip",
                interval_start=interval_start,
                interval_end=interval_end,
            )

            # Verify factory-created metrics
            assert metrics.id is not None
            assert isinstance(metrics.id, uuid.UUID)
            assert metrics.manager_instance == "factory-instance"
            assert metrics.metric_name == "Performance"
            assert (
                metrics.gcs_file_path
                == "acp-metrics/factory-instance/Performance/factory_file.zip"
            )
            assert metrics.interval_start == interval_start
            assert metrics.interval_end == interval_end
            assert metrics.created_at is not None
            assert metrics.updated_at is not None

    @pytest.mark.asyncio
    async def test_metrics_unique_constraint_allows_different_times(
        self, test_async_session_maker
    ):
        async with test_async_session_maker() as db_session:
            metrics_db_service = MetricsDbService(db_session)

            # Create first metrics record
            interval_start_1 = datetime.datetime.now(datetime.UTC)
            interval_end_1 = interval_start_1 + datetime.timedelta(minutes=5)

            metrics_pyd_model_1 = MetricsPydModel(
                manager_instance="test-instance",
                metric_name="Stats",
                gcs_file_path="acp-metrics/test-instance/Stats/file1.zip",
                interval_start=interval_start_1,
                interval_end=interval_end_1,
            )

            created_metrics_1 = await metrics_db_service.create_metrics(metrics_pyd_model_1)
            await db_session.commit()

            # Create second metrics record with same manager_instance but different times
            interval_start_2 = interval_start_1 + datetime.timedelta(minutes=5)
            interval_end_2 = interval_start_2 + datetime.timedelta(minutes=5)

            metrics_pyd_model_2 = MetricsPydModel(
                manager_instance="test-instance",  # Same manager_instance
                metric_name="Stats",  # Same metric_name
                gcs_file_path="acp-metrics/test-instance/Stats/file2.zip",
                interval_start=interval_start_2,  # Different interval_start
                interval_end=interval_end_2,  # Different interval_end
            )

            # This should succeed because times are different
            created_metrics_2 = await metrics_db_service.create_metrics(metrics_pyd_model_2)
            await db_session.commit()
            await db_session.refresh(created_metrics_1)
            await db_session.refresh(created_metrics_2)

            # Verify both records exist
            assert created_metrics_1.id != created_metrics_2.id
            assert created_metrics_1.manager_instance == created_metrics_2.manager_instance
            assert created_metrics_1.interval_start != created_metrics_2.interval_start
            assert created_metrics_1.interval_end != created_metrics_2.interval_end

    @pytest.mark.asyncio
    async def test_metrics_timestamps_auto_generated(self, test_async_session_maker):
        async with test_async_session_maker() as db_session:
            metrics_db_service = MetricsDbService(db_session)

            interval_start = datetime.datetime.now(datetime.UTC)
            interval_end = interval_start + datetime.timedelta(minutes=5)

            metrics_pyd_model = MetricsPydModel(
                manager_instance="test-timestamps",
                metric_name="Stats",
                gcs_file_path="acp-metrics/test-timestamps/Stats/test_file.zip",
                interval_start=interval_start,
                interval_end=interval_end,
            )

            created_metrics = await metrics_db_service.create_metrics(metrics_pyd_model)
            await db_session.commit()
            await db_session.refresh(created_metrics)

            # Verify timestamps are auto-generated by database
            assert created_metrics.created_at is not None
            assert created_metrics.updated_at is not None
            assert isinstance(created_metrics.created_at, datetime.datetime)
            assert isinstance(created_metrics.updated_at, datetime.datetime)
            assert created_metrics.created_at.tzinfo is not None  # Should have timezone info
            assert created_metrics.updated_at.tzinfo is not None  # Should have timezone info

    @pytest.mark.asyncio
    async def test_metrics_field_validation(self, test_async_session_maker):
        async with test_async_session_maker() as db_session:
            metrics_db_service = MetricsDbService(db_session)

            # Test with all valid fields
            interval_start = datetime.datetime.now(datetime.UTC)
            interval_end = interval_start + datetime.timedelta(minutes=5)

            valid_metrics = MetricsPydModel(
                manager_instance="valid-instance",
                metric_name="ValidMetric",
                gcs_file_path="acp-metrics/valid-instance/ValidMetric/valid_file.zip",
                interval_start=interval_start,
                interval_end=interval_end,
            )

            created_metrics = await metrics_db_service.create_metrics(valid_metrics)
            await db_session.commit()
            await db_session.refresh(created_metrics)

            # Verify all fields are set correctly
            assert created_metrics.manager_instance == "valid-instance"
            assert created_metrics.metric_name == "ValidMetric"
            assert (
                created_metrics.gcs_file_path
                == "acp-metrics/valid-instance/ValidMetric/valid_file.zip"
            )
            assert created_metrics.interval_start == interval_start
            assert created_metrics.interval_end == interval_end
            assert created_metrics.created_at is not None
            assert created_metrics.updated_at is not None

    @pytest.mark.asyncio
    async def test_metrics_time_ordering_validation(self, test_async_session_maker):
        async with test_async_session_maker() as db_session:
            metrics_db_service = MetricsDbService(db_session)

            # Test with interval_end before interval_start (should still create but logically invalid)
            interval_start = datetime.datetime.now(datetime.UTC)
            interval_end = interval_start - datetime.timedelta(minutes=5)  # End before start

            metrics_pyd_model = MetricsPydModel(
                manager_instance="time-test-instance",
                metric_name="TimeTest",
                gcs_file_path="acp-metrics/time-test-instance/TimeTest/test_file.zip",
                interval_start=interval_start,
                interval_end=interval_end,
            )

            # Database doesn't enforce time ordering, so this should succeed
            created_metrics = await metrics_db_service.create_metrics(metrics_pyd_model)
            await db_session.commit()
            await db_session.refresh(created_metrics)

            # Verify the record was created with the provided times
            assert created_metrics.interval_start == interval_start
            assert created_metrics.interval_end == interval_end
            assert (
                created_metrics.interval_start > created_metrics.interval_end
            )  # Logically invalid but allowed

    @pytest.mark.asyncio
    async def test_metrics_multiple_instances_same_time(self, test_async_session_maker):
        async with test_async_session_maker() as db_session:
            metrics_db_service = MetricsDbService(db_session)

            # Create metrics for multiple instances with same time intervals
            interval_start = datetime.datetime.now(datetime.UTC)
            interval_end = interval_start + datetime.timedelta(minutes=5)

            instances = ["instance-1", "instance-2", "instance-3"]
            created_metrics = []

            for instance in instances:
                metrics_pyd_model = MetricsPydModel(
                    manager_instance=instance,
                    metric_name="Stats",
                    gcs_file_path=f"acp-metrics/{instance}/Stats/test_file.zip",
                    interval_start=interval_start,
                    interval_end=interval_end,
                )

                metrics = await metrics_db_service.create_metrics(metrics_pyd_model)
                created_metrics.append(metrics)

            await db_session.commit()

            # Verify all metrics were created successfully
            assert len(created_metrics) == 3
            for i, metrics in enumerate(created_metrics):
                await db_session.refresh(metrics)
                assert metrics.manager_instance == instances[i]
                assert metrics.interval_start == interval_start
                assert metrics.interval_end == interval_end
                assert metrics.id is not None
                # Each should have unique ID
                for j, other_metrics in enumerate(created_metrics):
                    await db_session.refresh(other_metrics)
                    if i != j:
                        assert metrics.id != other_metrics.id

    @pytest.mark.asyncio
    async def test_metrics_factory_with_custom_attributes(self, test_async_session_maker):
        async with test_async_session_maker() as db_session:
            # Test factory with custom attributes
            custom_interval_start = datetime.datetime(2024, 1, 1, 12, 0, 0, tzinfo=datetime.UTC)
            custom_interval_end = datetime.datetime(2024, 1, 1, 12, 5, 0, tzinfo=datetime.UTC)

            metrics = await MetricsDbFactory._create(
                MetricsDbFactory._meta.model,
                async_session=db_session,
                manager_instance="custom-factory-instance",
                metric_name="CustomMetric",
                gcs_file_path="acp-metrics/custom-factory-instance/CustomMetric/custom_file.zip",
                interval_start=custom_interval_start,
                interval_end=custom_interval_end,
            )

            # Verify custom attributes
            assert metrics.manager_instance == "custom-factory-instance"
            assert metrics.metric_name == "CustomMetric"
            assert (
                metrics.gcs_file_path
                == "acp-metrics/custom-factory-instance/CustomMetric/custom_file.zip"
            )
            assert metrics.interval_start == custom_interval_start
            assert metrics.interval_end == custom_interval_end

    @pytest.mark.asyncio
    async def test_metrics_repr_method(self, test_async_session_maker):
        async with test_async_session_maker() as db_session:
            metrics_db_service = MetricsDbService(db_session)

            interval_start = datetime.datetime.now(datetime.UTC)
            interval_end = interval_start + datetime.timedelta(minutes=5)

            metrics_pyd_model = MetricsPydModel(
                manager_instance="repr-test-instance",
                metric_name="ReprTest",
                gcs_file_path="acp-metrics/repr-test-instance/ReprTest/test_file.zip",
                interval_start=interval_start,
                interval_end=interval_end,
            )

            created_metrics = await metrics_db_service.create_metrics(metrics_pyd_model)
            await db_session.commit()
            await db_session.refresh(created_metrics)

            # Test __repr__ method
            repr_str = repr(created_metrics)
            assert "MetricsDbModel" in repr_str
            assert str(created_metrics.id) in repr_str
            assert "repr-test-instance" in repr_str
            assert str(interval_start) in repr_str
            assert str(interval_end) in repr_str
