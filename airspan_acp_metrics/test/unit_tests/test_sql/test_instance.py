import asyncio
import datetime

import pytest
from da_common.models import Status

from airspan_acp_metrics.custom_exceptions import AlreadyExistsError
from airspan_acp_metrics.database.services.instance_db_service import InstanceDbService
from airspan_acp_metrics.pyd_models.instance_pyd_model import InstancePydModel
from airspan_acp_metrics.pyd_models.instance_pyd_model import InstanceUpdatePydModel


class TestInstance:
    @pytest.mark.asyncio
    async def test_create_instance(self, test_async_session_maker):
        async with test_async_session_maker() as db_session:
            instance_db_service = InstanceDbService(db_session)
            manager_instance = "test-instance"
            instance_pyd_model = InstancePydModel(
                manager_instance=manager_instance,
                url="http://test-url",
                host_ip_address="127.0.0.1",
                host_secret_id="test-secret-id",
                metrics_collection_status=Status.OK,
            )
            created_instance = await instance_db_service.create_acp_instance(instance_pyd_model)
            await db_session.commit()
            await db_session.refresh(created_instance)

            # Verify instance was created
            assert created_instance.manager_instance == manager_instance
            assert created_instance.url == "http://test-url"
            assert created_instance.host_ip_address == "127.0.0.1"
            assert created_instance.host_secret_id == "test-secret-id"
            assert created_instance.metrics_collection_status == Status.OK
            assert created_instance.created_at is not None
            assert created_instance.updated_at is not None

            # Verify it exists in database
            acp_instance_db_models = await instance_db_service.get_all_acp_instances()
            assert len(acp_instance_db_models) == 1
            assert acp_instance_db_models[0].manager_instance == manager_instance

    @pytest.mark.asyncio
    async def test_create_instance_duplicate_raises_already_exists_error(
        self, test_async_session_maker
    ):
        async with test_async_session_maker() as db_session:
            instance_db_service = InstanceDbService(db_session)
            manager_instance = "duplicate-instance"
            instance_pyd_model = InstancePydModel(
                manager_instance=manager_instance,
                url="http://test-url",
                host_ip_address="127.0.0.1",
                host_secret_id="test-secret-id",
                metrics_collection_status=Status.OK,
            )

            # Create first instance
            await instance_db_service.create_acp_instance(instance_pyd_model)
            await db_session.commit()

            # Try to create duplicate - should raise AlreadyExistsError
            with pytest.raises(AlreadyExistsError) as exc_info:
                await instance_db_service.create_acp_instance(instance_pyd_model)

            assert "already exists" in str(exc_info.value)
            assert manager_instance in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_all_acp_instances_empty(self, test_async_session_maker):
        async with test_async_session_maker() as db_session:
            instance_db_service = InstanceDbService(db_session)

            instances = await instance_db_service.get_all_acp_instances()
            assert len(instances) == 0

    @pytest.mark.asyncio
    async def test_get_all_acp_instances_multiple(self, test_async_session_maker):
        async with test_async_session_maker() as db_session:
            instance_db_service = InstanceDbService(db_session)

            # Create multiple instances
            instances_data = [
                ("instance-1", "http://url1", "***********"),
                ("instance-2", "http://url2", "***********"),
                ("instance-3", "http://url3", "***********"),
            ]

            for manager_instance, url, ip in instances_data:
                instance_pyd_model = InstancePydModel(
                    manager_instance=manager_instance,
                    url=url,
                    host_ip_address=ip,
                    host_secret_id=f"secret-{manager_instance}",
                    metrics_collection_status=Status.OK,
                )
                await instance_db_service.create_acp_instance(instance_pyd_model)

            await db_session.commit()

            # Verify all instances are returned
            instances = await instance_db_service.get_all_acp_instances()
            assert len(instances) == 3

            manager_instances = [instance.manager_instance for instance in instances]
            assert "instance-1" in manager_instances
            assert "instance-2" in manager_instances
            assert "instance-3" in manager_instances

    @pytest.mark.asyncio
    async def test_get_acp_instance_by_manager_instance_success(self, test_async_session_maker):
        async with test_async_session_maker() as db_session:
            instance_db_service = InstanceDbService(db_session)
            manager_instance = "test-get-instance"

            # Create instance
            instance_pyd_model = InstancePydModel(
                manager_instance=manager_instance,
                url="http://test-url",
                host_ip_address="127.0.0.1",
                host_secret_id="test-secret-id",
                metrics_collection_status=Status.OK,
            )
            await instance_db_service.create_acp_instance(instance_pyd_model)
            await db_session.commit()

            # Get instance by manager_instance
            retrieved_instance = await instance_db_service.get_acp_instance_by_manager_instance(
                manager_instance
            )

            assert retrieved_instance.manager_instance == manager_instance
            assert retrieved_instance.url == "http://test-url"
            assert retrieved_instance.host_ip_address == "127.0.0.1"
            assert retrieved_instance.host_secret_id == "test-secret-id"
            assert retrieved_instance.metrics_collection_status == Status.OK

    @pytest.mark.asyncio
    async def test_get_acp_instance_by_manager_instance_not_found(
        self, test_async_session_maker
    ):
        async with test_async_session_maker() as db_session:
            instance_db_service = InstanceDbService(db_session)
            acp_instance_db_model = (
                await instance_db_service.get_acp_instance_by_manager_instance(
                    "non-existent-instance"
                )
            )

            assert acp_instance_db_model is None

    @pytest.mark.asyncio
    async def test_update_acp_instance_success(self, test_async_session_maker):
        async with test_async_session_maker() as db_session:
            instance_db_service = InstanceDbService(db_session)
            manager_instance = "test-update-instance"

            # Create instance
            original_instance_pyd_model = InstancePydModel(
                manager_instance=manager_instance,
                url="http://original-url",
                host_ip_address="127.0.0.1",
                host_secret_id="original-secret-id",
                metrics_collection_status=Status.OK,
            )
            await instance_db_service.create_acp_instance(original_instance_pyd_model)
            await db_session.commit()

            # Update instance
            updated_at = datetime.datetime.now(datetime.UTC)
            updated_instance_pyd_model = InstanceUpdatePydModel(
                manager_instance=manager_instance,
                metrics_collection_status=Status.ERROR,
                updated_at=updated_at,
            )
            updated_instance = await instance_db_service.update_acp_instance(
                updated_instance_pyd_model
            )
            await db_session.commit()
            await db_session.refresh(updated_instance)

            # Verify updates
            assert updated_instance.manager_instance == manager_instance
            assert updated_instance.updated_at == updated_at
            assert updated_instance.metrics_collection_status == Status.ERROR

            # Verify in database
            retrieved_instance = await instance_db_service.get_acp_instance_by_manager_instance(
                manager_instance
            )
            assert retrieved_instance.url == "http://original-url"
            assert retrieved_instance.host_ip_address == "127.0.0.1"
            assert retrieved_instance.host_secret_id == "original-secret-id"
            assert retrieved_instance.metrics_collection_status == Status.ERROR

    @pytest.mark.asyncio
    async def test_update_acp_instance_not_found(self, test_async_session_maker):
        async with test_async_session_maker() as db_session:
            instance_db_service = InstanceDbService(db_session)

            # Try to update non-existent instance
            update_instance_pyd_model = InstancePydModel(
                manager_instance="non-existent-instance",
                url="http://test-url",
                host_ip_address="127.0.0.1",
                host_secret_id="test-secret-id",
                metrics_collection_status=Status.OK,
            )
            acp_instance = await instance_db_service.update_acp_instance(
                update_instance_pyd_model
            )
            assert acp_instance is None

    @pytest.mark.asyncio
    async def test_delete_acp_instance_success(self, test_async_session_maker):
        async with test_async_session_maker() as db_session:
            instance_db_service = InstanceDbService(db_session)
            manager_instance = "test-delete-instance"

            # Create instance
            instance_pyd_model = InstancePydModel(
                manager_instance=manager_instance,
                url="http://test-url",
                host_ip_address="127.0.0.1",
                host_secret_id="test-secret-id",
                metrics_collection_status=Status.OK,
            )
            await instance_db_service.create_acp_instance(instance_pyd_model)
            await db_session.commit()

            # Verify instance exists
            instances_before = await instance_db_service.get_all_acp_instances()
            assert len(instances_before) == 1

            # Delete instance
            await instance_db_service.delete_acp_instance(manager_instance)
            await db_session.commit()

            # Verify instance is deleted
            instances_after = await instance_db_service.get_all_acp_instances()
            assert len(instances_after) == 0

            # Verify after getting deleted instance, we can no longer find it in db
            acp_instance_db_model = (
                await instance_db_service.get_acp_instance_by_manager_instance(manager_instance)
            )
            assert acp_instance_db_model is None

    @pytest.mark.asyncio
    async def test_delete_acp_instance_not_found(self, test_async_session_maker):
        async with test_async_session_maker() as db_session:
            instance_db_service = InstanceDbService(db_session)

            # Try to delete non-existent instance ans it will handle is gracefully
            await instance_db_service.delete_acp_instance("non-existent-instance")

    @pytest.mark.asyncio
    async def test_instance_timestamps_auto_generated(self, test_async_session_maker):
        async with test_async_session_maker() as db_session:
            instance_db_service = InstanceDbService(db_session)
            manager_instance = "test-timestamps"

            # Create instance
            instance_pyd_model = InstancePydModel(
                manager_instance=manager_instance,
                url="http://test-url",
                host_ip_address="127.0.0.1",
                host_secret_id="test-secret-id",
                metrics_collection_status=Status.OK,
            )
            created_instance = await instance_db_service.create_acp_instance(instance_pyd_model)
            await db_session.commit()
            await db_session.refresh(created_instance)

            # Verify timestamps are auto-generated
            assert created_instance.created_at is not None
            assert created_instance.updated_at is not None

            # Store original timestamps
            original_created_at = created_instance.created_at

            await asyncio.sleep(0.01)  # Small delay to ensure timestamp difference

            updated_instance_pyd_model = InstanceUpdatePydModel(
                manager_instance=manager_instance,
                metrics_collection_status=Status.OK,
                updated_at=datetime.datetime.now(datetime.UTC),
            )
            updated_instance = await instance_db_service.update_acp_instance(
                updated_instance_pyd_model
            )
            await db_session.commit()
            await db_session.refresh(updated_instance)

            # Verify created_at stays the same but updated_at changes
            assert updated_instance.created_at == original_created_at
            assert updated_instance.updated_at is not None
            assert updated_instance.updated_at > original_created_at

    @pytest.mark.asyncio
    async def test_instance_primary_key_constraint(self, test_async_session_maker):
        """Test that manager_instance acts as primary key and enforces uniqueness"""
        async with test_async_session_maker() as db_session:
            instance_db_service = InstanceDbService(db_session)
            manager_instance = "primary-key-test"

            # Create first instance
            instance_pyd_model_1 = InstancePydModel(
                manager_instance=manager_instance,
                url="http://url1",
                host_ip_address="127.0.0.1",
                host_secret_id="secret-1",
                metrics_collection_status=Status.OK,
            )
            await instance_db_service.create_acp_instance(instance_pyd_model_1)
            await db_session.commit()

            # Try to create second instance with same manager_instance but different other fields
            instance_pyd_model_2 = InstancePydModel(
                manager_instance=manager_instance,  # Same primary key
                url="http://url2",  # Different URL
                host_ip_address="***********",  # Different IP
                host_secret_id="secret-2",  # Different secret
                metrics_collection_status=Status.ERROR,  # Different status
            )

            # Should raise AlreadyExistsError due to primary key constraint
            with pytest.raises(AlreadyExistsError) as exc_info:
                await instance_db_service.create_acp_instance(instance_pyd_model_2)

            assert "already exists" in str(exc_info.value)
            assert manager_instance in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_instance_field_validation(self, test_async_session_maker):
        """Test that all required fields are properly validated"""
        async with test_async_session_maker() as db_session:
            instance_db_service = InstanceDbService(db_session)

            # Test with all valid fields
            valid_instance = InstancePydModel(
                manager_instance="valid-instance",
                url="http://valid-url",
                host_ip_address="127.0.0.1",
                host_secret_id="valid-secret-id",
                metrics_collection_status=Status.OK,
            )

            created_instance = await instance_db_service.create_acp_instance(valid_instance)
            await db_session.commit()
            await db_session.refresh(created_instance)

            # Verify all fields are set correctly
            assert created_instance.manager_instance == "valid-instance"
            assert created_instance.url == "http://valid-url"
            assert created_instance.host_ip_address == "127.0.0.1"
            assert created_instance.host_secret_id == "valid-secret-id"
            assert created_instance.metrics_collection_status == Status.OK
            assert created_instance.created_at is not None
            assert created_instance.updated_at is not None
