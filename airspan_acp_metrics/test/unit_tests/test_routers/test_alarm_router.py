import datetime

import pytest
from metrics_collector.api_schema.models import Severity as AlarmSeverityEnum

from airspan_acp_metrics.pyd_models.alarm_enums import AlarmPriority
from airspan_acp_metrics.pyd_models.alarm_enums import AlarmStatus
from airspan_acp_metrics.pyd_models.alarm_enums import AlarmType
from airspan_acp_metrics.pyd_models.instance_pyd_model import InstancePydModel
from airspan_acp_metrics.test.factories.alarm_db_factory import AlarmDbFactory
from airspan_acp_metrics.test.factories.alarm_type_db_factory import AlarmTypeDbFactory
from airspan_acp_metrics.test.factories.instance_db_factory import InstanceDbFactory


@pytest.mark.asyncio
class TestAlarmRouter:
    """Test cases for alarm router endpoints."""

    async def test_get_alarms(self, test_async_client, test_async_session):
        """Test getting alarms endpoint."""
        # Create test data
        instance_db_model = await InstanceDbFactory(async_session=test_async_session)
        instance_pyd_model = InstancePydModel.model_validate(instance_db_model)
        await AlarmTypeDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
        )
        await AlarmDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
            manager_instance=instance_pyd_model.manager_instance,
        )
        await AlarmDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
            manager_instance=instance_pyd_model.manager_instance,
        )

        # Make request
        response = await test_async_client.get("/nms/api/alarms")

        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        assert data[0]["type"] == AlarmType.SSH_CONNECTIVITY_FAILED
        assert data[1]["type"] == AlarmType.SSH_CONNECTIVITY_FAILED

    async def test_get_alarms_with_filter(self, test_async_client, test_async_session):
        """Test getting alarms with filter."""
        # Create test data
        instance_db_model = await InstanceDbFactory(async_session=test_async_session)
        instance_pyd_model = InstancePydModel.model_validate(instance_db_model)
        await AlarmTypeDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
        )
        await AlarmDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
            manager_instance=instance_pyd_model.manager_instance,
            severity=AlarmSeverityEnum.critical,
        )
        await AlarmDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
            manager_instance=instance_pyd_model.manager_instance,
            severity=AlarmSeverityEnum.major,
        )

        # Make request with filter
        response = await test_async_client.get(
            "/nms/api/alarms", params={"severity": AlarmSeverityEnum.critical.value}
        )

        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["severity"] == AlarmSeverityEnum.critical

    async def test_get_alarm_by_id(self, test_async_client, test_async_session):
        """Test getting alarm by ID."""
        # Create test data
        instance_db_model = await InstanceDbFactory(async_session=test_async_session)
        instance_pyd_model = InstancePydModel.model_validate(instance_db_model)
        await AlarmTypeDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
        )
        alarm_db_model = await AlarmDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
            manager_instance=instance_pyd_model.manager_instance,
        )

        # Make request
        response = await test_async_client.get(f"/nms/api/alarms/{alarm_db_model.id}")

        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == str(alarm_db_model.id)
        assert data["type"] == AlarmType.SSH_CONNECTIVITY_FAILED

    async def test_get_alarm_by_id_not_found(self, test_async_client):
        """Test getting alarm by ID when not found."""
        import uuid

        # Make request with non-existent ID
        response = await test_async_client.get(f"/nms/api/alarms/{uuid.uuid4()}")

        # Verify response
        assert response.status_code == 404

    async def test_acknowledge_alarm(self, test_async_client, test_async_session):
        """Test acknowledging an alarm."""
        # Create test data
        instance_db_model = await InstanceDbFactory(async_session=test_async_session)
        instance_pyd_model = InstancePydModel.model_validate(instance_db_model)
        await AlarmTypeDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
        )
        alarm_db_model = await AlarmDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
            manager_instance=instance_pyd_model.manager_instance,
        )

        # Make request
        acknowledge_data = {
            "acknowledger": "test_user",
            "acknowledged": datetime.datetime.now().isoformat(),
        }
        response = await test_async_client.put(
            f"/nms/api/alarms/{alarm_db_model.id}/acknowledge", json=acknowledge_data
        )

        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert data["acknowledger"] == "test_user"
        assert data["status"] == AlarmStatus.acknowledged

    async def test_resolve_alarm(self, test_async_client, test_async_session):
        """Test resolving an alarm."""
        # Create test data
        instance_db_model = await InstanceDbFactory(async_session=test_async_session)
        instance_pyd_model = InstancePydModel.model_validate(instance_db_model)
        await AlarmTypeDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
        )
        alarm_db_model = await AlarmDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
            manager_instance=instance_pyd_model.manager_instance,
        )

        # Make request
        resolve_data = {
            "resolver": "test_user",
            "resolved_at": datetime.datetime.now().isoformat(),
        }
        response = await test_async_client.put(
            f"/nms/api/alarms/{alarm_db_model.id}/resolve", json=resolve_data
        )

        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert data["resolver"] == "test_user"
        assert data["status"] == AlarmStatus.resolved

    async def test_get_alarm_types(self, test_async_client, test_async_session):
        """Test getting all alarm types."""
        # Create test data
        await AlarmTypeDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
        )
        await AlarmTypeDbFactory(
            async_session=test_async_session,
            type=AlarmType.INVENTORY_MANAGER_UNREACHABLE,
        )

        # Make request
        response = await test_async_client.get("/nms/api/alarm_types")

        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        type_names = [at["type"] for at in data]
        assert AlarmType.SSH_CONNECTIVITY_FAILED in type_names
        assert AlarmType.INVENTORY_MANAGER_UNREACHABLE in type_names

    async def test_get_alarm_type_by_type(self, test_async_client, test_async_session):
        """Test getting alarm type by type."""
        # Create test data
        await AlarmTypeDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
        )

        # Make request
        response = await test_async_client.get(
            f"/nms/api/alarm_types/{AlarmType.SSH_CONNECTIVITY_FAILED.value}"
        )

        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert data["type"] == AlarmType.SSH_CONNECTIVITY_FAILED
        assert data["severity"] == AlarmSeverityEnum.critical
        assert data["priority"] == AlarmPriority.high

    async def test_get_alarm_type_by_type_not_found(self, test_async_client):
        """Test getting alarm type by type when not found."""
        # Make request with non-existent type
        response = await test_async_client.get(
            f"/nms/api/alarm_types/{AlarmType.INVENTORY_MANAGER_UNREACHABLE}"
        )

        # Verify response
        assert response.status_code == 422

    async def test_update_alarm_type(self, test_async_client, test_async_session):
        """Test updating an alarm type."""
        # Create test data
        await AlarmTypeDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
        )

        # Make request
        update_data = {
            "severity": AlarmSeverityEnum.critical,
            "priority": AlarmPriority.medium,
            "repairs": ["Updated repair actions"],
        }
        response = await test_async_client.put(
            f"/nms/api/alarm_types/{AlarmType.SSH_CONNECTIVITY_FAILED.value}", json=update_data
        )

        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert data["severity"] == AlarmSeverityEnum.critical
        assert data["priority"] == AlarmPriority.medium
        assert data["repairs"] == ["Updated repair actions"]

    async def test_delete_alarm_type_success(self, test_async_client, test_async_session):
        """Test deleting an alarm type successfully."""
        # Create test data
        await AlarmTypeDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
        )

        # Make request
        response = await test_async_client.delete(
            f"/nms/api/alarm_types/{AlarmType.SSH_CONNECTIVITY_FAILED.value}"
        )

        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert "Successfully deleted" in data["message"]

    async def test_delete_alarm_type_with_existing_alarms(
        self, test_async_client, test_async_session
    ):
        """Test deleting an alarm type with existing alarms."""
        # Create test data
        instance_db_model = await InstanceDbFactory(async_session=test_async_session)
        instance_pyd_model = InstancePydModel.model_validate(instance_db_model)
        await AlarmTypeDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
        )
        await AlarmDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
            manager_instance=instance_pyd_model.manager_instance,
        )

        # Make request
        response = await test_async_client.delete(
            f"/nms/api/alarm_types/{AlarmType.SSH_CONNECTIVITY_FAILED.value}"
        )

        # Verify response
        assert response.status_code == 400
        data = response.json()
        assert "Cannot delete" in data["detail"]
        assert "alarms still reference it" in data["detail"]
