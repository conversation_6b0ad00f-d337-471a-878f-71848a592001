import datetime

import pytest

from airspan_acp_metrics.database.services.metric_audit_db_service import MetricsAuditDbService
from airspan_acp_metrics.pyd_models.utils import MetricsCollectionStatus
from airspan_acp_metrics.test.factories.metrics_audit_db_factory import MetricsAuditDbFactory
from airspan_acp_metrics.test.factories.metrics_db_factory import MetricsDbFactory


class TestMetricsAuditRouter:
    """Tests for the metrics audit router."""

    @pytest.mark.asyncio
    async def test_delete_metrics_audit_by_time_range_success(
        self, test_async_client, test_async_session_maker
    ):
        """Test successful deletion of metrics audit records by time range."""
        async with test_async_session_maker() as db_session:
            # Create test data
            manager_instance = "test-instance"
            metric_name = "test-metric"
            now = datetime.datetime.now(datetime.UTC)
            start_time = now - datetime.timedelta(days=1, minutes=30)
            end_time = now + datetime.timedelta(days=1)

            # Create metrics record first
            metrics_record = await MetricsDbFactory(
                async_session=db_session,
                manager_instance=manager_instance,
                metric_name=metric_name,
                gcs_file_path="gs://test-bucket/test-file.json",
                interval_start=start_time,
                interval_end=start_time + datetime.timedelta(minutes=5),
            )

            # Create audit records with different statuses
            await MetricsAuditDbFactory(
                async_session=db_session,
                manager_instance=manager_instance,
                metric_name=metric_name,
                collection_status=MetricsCollectionStatus.COLLECTED,
                interval_start=start_time,
                interval_end=start_time + datetime.timedelta(minutes=5),
                metrics_id=metrics_record.id,
            )

            await MetricsAuditDbFactory(
                async_session=db_session,
                manager_instance=manager_instance,
                metric_name=metric_name,
                collection_status=MetricsCollectionStatus.FAILED,
                interval_start=start_time + datetime.timedelta(minutes=10),
                interval_end=start_time + datetime.timedelta(minutes=15),
                metrics_id=None,
            )

            # Create a RUNNING audit that should NOT be deleted
            await MetricsAuditDbFactory(
                async_session=db_session,
                manager_instance=manager_instance,
                metric_name=metric_name,
                collection_status=MetricsCollectionStatus.RUNNING,
                interval_start=start_time + datetime.timedelta(minutes=20),
                interval_end=start_time + datetime.timedelta(minutes=25),
                metrics_id=None,
            )

            await db_session.commit()

            response = await test_async_client.post(
                "/nms/api/metrics-audit/republish",
                json={
                    "start_time": start_time.isoformat(),
                    "end_time": end_time.isoformat(),
                    "manager_instance": manager_instance,
                    "metric_name": metric_name,
                },
            )

            assert response.status_code == 200
            response_data = response.json()["message"]

            # Should delete 2 audit records and 1 metrics record
            assert (
                response_data
                == f"Successfully deleted 2 audit records and 1 metrics records from {start_time.isoformat()} to {end_time.isoformat()}, the metrics will be republished in the next collection cycle"
            )

            audit_service = MetricsAuditDbService(db_session)
            audit_records = await audit_service.get_instance_metrics_audits(
                manager_instance=manager_instance,
                metric_name=metric_name,
                interval_start=start_time,
                interval_end=end_time,
            )

            # check that only the running audit record is left
            assert len(audit_records) == 1
            assert audit_records[0].collection_status == MetricsCollectionStatus.RUNNING

    @pytest.mark.asyncio
    async def test_delete_metrics_audit_invalid_time_range(self, test_async_client):
        """Test validation error for invalid time range."""
        start_time = datetime.datetime(2025, 1, 15, 12, 0, 0, tzinfo=datetime.UTC)
        end_time = datetime.datetime(
            2025, 1, 15, 10, 0, 0, tzinfo=datetime.UTC
        )  # Before start_time

        response = await test_async_client.post(
            "/nms/api/metrics-audit/republish",
            json={
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "manager_instance": "test-instance",
                "metric_name": "test-metric",
            },
        )

        assert response.status_code == 422
        assert (
            response.json()["detail"][0]["msg"]
            == "Value error, End time must be at least 5 minutes after start time"
        )

    @pytest.mark.asyncio
    async def test_delete_metrics_audit_no_records_found(self, test_async_client):
        """Test when no records are found in the time range."""
        now = datetime.datetime.now(datetime.UTC)
        start_time = now - datetime.timedelta(days=1, minutes=30)
        end_time = now + datetime.timedelta(days=1)

        response = await test_async_client.post(
            "/nms/api/metrics-audit/republish",
            json={
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "manager_instance": "test-instance",
                "metric_name": "test-metric",
            },
        )

        assert response.status_code == 200
        response_data = response.json()

        assert (
            response_data["message"]
            == f"No audit records or metrics records found in the time range {start_time.isoformat()} to {end_time.isoformat()}"
        )

    @pytest.mark.asyncio
    async def test_delete_metrics_audit_only_running_status(
        self, test_async_client, test_async_session_maker
    ):
        """Test when only RUNNING status records exist in the time range."""
        async with test_async_session_maker() as db_session:
            now = datetime.datetime.now(datetime.UTC)
            start_time = now - datetime.timedelta(days=1, minutes=30)
            end_time = now + datetime.timedelta(days=1)
            # Create only RUNNING audit records
            await MetricsAuditDbFactory(
                async_session=db_session,
                manager_instance="test-instance",
                metric_name="test-metric",
                collection_status=MetricsCollectionStatus.RUNNING,
                interval_start=start_time,
                interval_end=start_time + datetime.timedelta(minutes=5),
                metrics_id=None,
            )

            await db_session.commit()

            response = await test_async_client.post(
                "/nms/api/metrics-audit/republish",
                json={
                    "start_time": start_time.isoformat(),
                    "end_time": end_time.isoformat(),
                    "manager_instance": "test-instance",
                    "metric_name": "test-metric",
                },
            )

            assert response.status_code == 200
            response_message = response.json()["message"]

            # Should not delete any records
            assert (
                response_message
                == f"No audit records or metrics records found in the time range {start_time.isoformat()} to {end_time.isoformat()}"
            )

    @pytest.mark.asyncio
    async def test_delete_metrics_audit_old_time_range(self, test_async_client):
        """Test when the time range is older than 30 days."""
        now = datetime.datetime.now(datetime.UTC)
        start_time = now - datetime.timedelta(days=31)
        end_time = now + datetime.timedelta(days=1)

        response = await test_async_client.post(
            "/nms/api/metrics-audit/republish",
            json={
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "manager_instance": "test-instance",
                "metric_name": "test-metric",
            },
        )

        assert response.status_code == 400
        assert (
            response.json()["detail"]
            == "Cannot republish metrics audit records older than 30 days"
        )
