import datetime
from unittest.mock import AsyncMock
from unittest.mock import MagicMock
from unittest.mock import patch

import pytest
from da_common.models import Status

from airspan_acp_metrics.database.services.metric_audit_db_service import MetricsAuditDbService
from airspan_acp_metrics.pyd_models.utils import MetricsCollectionStatus
from airspan_acp_metrics.test.factories.instance_db_factory import InstanceDbFactory
from airspan_acp_metrics.test.factories.metrics_audit_db_factory import MetricsAuditDbFactory
from airspan_acp_metrics.workers.metrics_audit_worker import MetricsAuditWorker


class TestMetricsAuditWorker:
    """Tests for the MetricsAuditWorker class including both success and exception scenarios."""

    @pytest.fixture
    def metrics_audit_worker(self, test_config):
        """Create a MetricsAuditWorker instance."""
        return MetricsAuditWorker(test_config)

    @pytest.fixture
    def mock_now(self):
        """Mock current time for consistent testing."""
        return datetime.datetime(2025, 1, 15, 12, 0, 0, tzinfo=datetime.UTC)

    # INITIALIZATION TESTS
    def test_metrics_audit_worker_initialization(self, test_config):
        """Test MetricsAuditWorker initialization with valid config."""
        worker = MetricsAuditWorker(test_config)

        assert worker.config == test_config
        assert worker.async_session_maker is not None
        assert worker.scheduler is not None
        assert worker._is_running is False
        assert worker.logger is not None
        assert worker.ssh_manager is not None
        assert worker.db_ops is not None
        assert isinstance(worker.metrics_configs, dict)
        assert isinstance(worker.any_backfill_enabled, bool)
        assert worker.max_concurrent_backfills == 10

    def test_metrics_audit_worker_initialization_no_metrics_config(self, test_config):
        """Test MetricsAuditWorker initialization when no metrics are configured."""
        # Mock config with no metrics
        test_config.data["metrics"] = {}
        worker = MetricsAuditWorker(test_config)

        assert worker.metrics_configs == {}
        assert worker.any_backfill_enabled is False

    def test_metrics_audit_worker_initialization_disabled_metrics(self, test_config):
        """Test MetricsAuditWorker initialization when all metrics are disabled."""
        # Mock config with disabled metrics
        test_config.data["metrics"] = {
            "stats": {"enabled": False, "metric_name": "Stats"},
            "performance": {"enabled": False, "metric_name": "Performance"},
        }
        worker = MetricsAuditWorker(test_config)

        assert worker.metrics_configs == {}
        assert worker.any_backfill_enabled is False

    # LOG PREFIX TESTS
    def test_get_log_prefix_no_params(self):
        """Test _get_log_prefix with no parameters."""
        result = MetricsAuditWorker._get_log_prefix()
        assert result == "MetricsAuditWorker"

    def test_get_log_prefix_with_instance_name(self):
        """Test _get_log_prefix with instance name."""
        result = MetricsAuditWorker._get_log_prefix(instance_name="test-instance")
        assert result == "MetricsAuditWorker test-instance"

    def test_get_log_prefix_with_metric_name(self):
        """Test _get_log_prefix with metric name."""
        result = MetricsAuditWorker._get_log_prefix(metric_name="Stats")
        assert result == "MetricsAuditWorker Stats"

    def test_get_log_prefix_with_intervals(self):
        """Test _get_log_prefix with intervals."""
        intervals = [
            (
                datetime.datetime(2025, 1, 1, 10, 0, 0, tzinfo=datetime.UTC),
                datetime.datetime(2025, 1, 1, 10, 5, 0, tzinfo=datetime.UTC),
            )
        ]
        result = MetricsAuditWorker._get_log_prefix(intervals=intervals)
        assert result == "MetricsAuditWorker ( 2025-01-01 10:00 - 10:05 )"

    def test_get_log_prefix_with_all_params(self):
        """Test _get_log_prefix with all parameters."""
        intervals = [
            (
                datetime.datetime(2025, 1, 1, 10, 0, 0, tzinfo=datetime.UTC),
                datetime.datetime(2025, 1, 1, 10, 5, 0, tzinfo=datetime.UTC),
            )
        ]
        result = MetricsAuditWorker._get_log_prefix(
            instance_name="test-instance", metric_name="Stats", intervals=intervals
        )
        assert "MetricsAuditWorker test-instance Stats" in result
        assert "( 2025-01-01 10:00 - 10:05 )" in result

    # START METHOD TESTS
    def test_start_already_running(self, metrics_audit_worker):
        """Test start method when worker is already running."""
        metrics_audit_worker._is_running = True

        with (
            patch.object(metrics_audit_worker.scheduler, "add_job") as mock_add_job,
            patch.object(metrics_audit_worker.scheduler, "start") as mock_start,
        ):
            metrics_audit_worker.start()

            mock_add_job.assert_not_called()
            mock_start.assert_not_called()

    def test_start_no_backfill_enabled(self, metrics_audit_worker):
        """Test start method when no backfill is enabled."""
        metrics_audit_worker.any_backfill_enabled = False

        with (
            patch.object(metrics_audit_worker.scheduler, "add_job") as mock_add_job,
            patch.object(metrics_audit_worker.scheduler, "start") as mock_start,
        ):
            metrics_audit_worker.start()

            mock_add_job.assert_not_called()
            mock_start.assert_not_called()

    def test_start_success(self, metrics_audit_worker):
        """Test successful start of MetricsAuditWorker."""
        metrics_audit_worker.any_backfill_enabled = True

        with (
            patch.object(metrics_audit_worker.scheduler, "add_job") as mock_add_job,
            patch.object(metrics_audit_worker.scheduler, "start") as mock_start,
        ):
            metrics_audit_worker.start()

            mock_add_job.assert_called_once()
            mock_start.assert_called_once()
            assert metrics_audit_worker._is_running is True

    # STATIC METHOD TESTS
    def test_is_metric_backfill_enabled_true(self):
        """Test _is_metric_backfill_enabled returns True when both enabled and backfill_enabled are True."""
        metric_config = {"enabled": True, "backfill_enabled": True}
        result = MetricsAuditWorker._is_metric_backfill_enabled(metric_config)
        assert result is True

    def test_is_metric_backfill_enabled_false_not_enabled(self):
        """Test _is_metric_backfill_enabled returns False when enabled is False."""
        metric_config = {"enabled": False, "backfill_enabled": True}
        result = MetricsAuditWorker._is_metric_backfill_enabled(metric_config)
        assert result is False

    def test_is_metric_backfill_enabled_false_backfill_disabled(self):
        """Test _is_metric_backfill_enabled returns False when backfill_enabled is False."""
        metric_config = {"enabled": True, "backfill_enabled": False}
        result = MetricsAuditWorker._is_metric_backfill_enabled(metric_config)
        assert result is False

    def test_is_metric_backfill_enabled_false_both_disabled(self):
        """Test _is_metric_backfill_enabled returns False when both are False."""
        metric_config = {"enabled": False, "backfill_enabled": False}
        result = MetricsAuditWorker._is_metric_backfill_enabled(metric_config)
        assert result is False

    def test_is_metric_backfill_enabled_default_values(self):
        """Test _is_metric_backfill_enabled with default values."""
        metric_config = {}
        result = MetricsAuditWorker._is_metric_backfill_enabled(metric_config)
        assert result is False

    def test_calculate_metric_time_range_valid(self, mock_now):
        """Test _calculate_metric_time_range with valid time range."""
        # Create a mock instance that was created 10 days ago
        instance_created = mock_now - datetime.timedelta(days=10)
        mock_instance = MagicMock()
        mock_instance.created_at = instance_created

        max_days_lookback = 30
        metric_config = {"max_days_lookback": max_days_lookback, "frequency": 5}

        with patch(
            "airspan_acp_metrics.workers.metrics_audit_worker.get_most_recent_metrics_time_interval"
        ) as mock_get_time:
            mock_get_time.return_value = (None, mock_now - datetime.timedelta(minutes=5))

            result = MetricsAuditWorker._calculate_metric_time_range(
                mock_instance, metric_config, mock_now
            )

            assert result is not None
            oldest_allowed, newest_allowed = result
            assert oldest_allowed == mock_now - datetime.timedelta(days=max_days_lookback)
            assert newest_allowed == mock_now - datetime.timedelta(minutes=5)

    def test_calculate_metric_time_range_instance_older_than_lookback(self, mock_now):
        """Test _calculate_metric_time_range when instance is older than lookback time."""
        # Create a mock instance that was created 60 days ago
        instance_created = mock_now - datetime.timedelta(days=60)
        mock_instance = MagicMock()
        mock_instance.created_at = instance_created

        metric_config = {"max_days_lookback": 30, "frequency": 5}

        with patch(
            "airspan_acp_metrics.workers.metrics_audit_worker.get_most_recent_metrics_time_interval"
        ) as mock_get_time:
            mock_get_time.return_value = (None, mock_now - datetime.timedelta(minutes=5))

            result = MetricsAuditWorker._calculate_metric_time_range(
                mock_instance, metric_config, mock_now
            )

            assert result is not None
            oldest_allowed, newest_allowed = result
            # Should use lookback time since instance is older
            expected_oldest = mock_now - datetime.timedelta(days=30)
            assert oldest_allowed == expected_oldest
            assert newest_allowed == mock_now - datetime.timedelta(minutes=5)

    def test_generate_expected_intervals_normal_case(self):
        """Test _generate_expected_intervals with normal case."""
        interval_start = datetime.datetime(2025, 1, 1, 10, 0, 0, tzinfo=datetime.UTC)
        interval_end = datetime.datetime(2025, 1, 1, 10, 15, 0, tzinfo=datetime.UTC)
        interval_minutes = 5

        result = MetricsAuditWorker._generate_expected_intervals(
            interval_start, interval_end, interval_minutes
        )

        expected = [
            (
                datetime.datetime(2025, 1, 1, 10, 0, 0, tzinfo=datetime.UTC),
                datetime.datetime(2025, 1, 1, 10, 5, 0, tzinfo=datetime.UTC),
            ),
            (
                datetime.datetime(2025, 1, 1, 10, 5, 0, tzinfo=datetime.UTC),
                datetime.datetime(2025, 1, 1, 10, 10, 0, tzinfo=datetime.UTC),
            ),
            (
                datetime.datetime(2025, 1, 1, 10, 10, 0, tzinfo=datetime.UTC),
                datetime.datetime(2025, 1, 1, 10, 15, 0, tzinfo=datetime.UTC),
            ),
        ]

        assert len(result) == 3
        assert result == expected

    def test_generate_expected_intervals_rounding_down(self):
        """Test _generate_expected_intervals with start time that needs rounding down."""
        # Start at 10:03, should round down to 10:00
        interval_start = datetime.datetime(2025, 1, 1, 10, 3, 30, tzinfo=datetime.UTC)
        interval_end = datetime.datetime(2025, 1, 1, 10, 10, 0, tzinfo=datetime.UTC)
        interval_minutes = 5

        result = MetricsAuditWorker._generate_expected_intervals(
            interval_start, interval_end, interval_minutes
        )

        # Should start from 10:00, not 10:03
        expected_first_start = datetime.datetime(2025, 1, 1, 10, 0, 0, tzinfo=datetime.UTC)
        assert result[0][0] == expected_first_start

    def test_generate_expected_intervals_empty_range(self):
        """Test _generate_expected_intervals with empty time range."""
        interval_start = datetime.datetime(2025, 1, 1, 10, 5, 0, tzinfo=datetime.UTC)
        interval_end = datetime.datetime(2025, 1, 1, 10, 5, 0, tzinfo=datetime.UTC)
        interval_minutes = 5

        result = MetricsAuditWorker._generate_expected_intervals(
            interval_start, interval_end, interval_minutes
        )

        assert result == []

    def test_attempts_exhausted_true(self):
        """Test attempts_exhausted returns True when max attempts exceeded."""
        mock_audit = MagicMock()
        mock_audit.attempt_count = 5
        max_retry_attempts = 3

        result = MetricsAuditWorker.attempts_exhausted(mock_audit, max_retry_attempts)
        assert result is True

    def test_attempts_exhausted_false(self):
        """Test attempts_exhausted returns False when max attempts not exceeded."""
        mock_audit = MagicMock()
        mock_audit.attempt_count = 2
        max_retry_attempts = 3

        result = MetricsAuditWorker.attempts_exhausted(mock_audit, max_retry_attempts)
        assert result is False

    def test_attempts_exhausted_equal(self):
        """Test attempts_exhausted returns True when attempts equal max."""
        mock_audit = MagicMock()
        mock_audit.attempt_count = 3
        max_retry_attempts = 3

        result = MetricsAuditWorker.attempts_exhausted(mock_audit, max_retry_attempts)
        assert result is True

    # ASYNC METHODS TESTS
    @pytest.mark.asyncio
    async def test_create_missing_metrics_audits_no_backfill_enabled(
        self, metrics_audit_worker
    ):
        """Test _create_missing_metrics_audits when no backfill is enabled."""
        metrics_audit_worker.any_backfill_enabled = False

        # Should return early without doing anything
        await metrics_audit_worker._create_missing_metrics_audits()
        # Test passes if no exception is raised

    @pytest.mark.asyncio
    async def test_create_missing_metrics_audits_no_instances(
        self, metrics_audit_worker, test_async_session_maker
    ):
        """Test _create_missing_metrics_audits when no instances are found."""
        metrics_audit_worker.any_backfill_enabled = True
        metrics_audit_worker.metrics_configs = {
            "stats": {"enabled": True, "backfill_enabled": True, "metric_name": "Stats"}
        }

        with patch(
            "airspan_acp_metrics.workers.metrics_audit_worker.get_db_session"
        ) as mock_get_session:
            mock_session = AsyncMock()
            mock_get_session.return_value.__aenter__.return_value = mock_session

            mock_instance_service = AsyncMock()
            mock_instance_service.get_all_acp_instances.return_value = []

            with patch(
                "airspan_acp_metrics.workers.metrics_audit_worker.InstanceDbService",
                return_value=mock_instance_service,
            ):
                await metrics_audit_worker._create_missing_metrics_audits()

                mock_instance_service.get_all_acp_instances.assert_called_once_with(
                    metrics_collection_status=Status.OK
                )

    @pytest.mark.asyncio
    async def test_create_missing_metrics_audits_success(
        self, metrics_audit_worker, test_async_session, mock_now
    ):
        """Test _create_missing_metrics_audits with successful execution."""
        metrics_audit_worker.any_backfill_enabled = True
        metrics_audit_worker.metrics_configs = {
            "stats": {
                "enabled": True,
                "backfill_enabled": True,
                "metric_name": "Stats",
                "frequency": 5,
                "max_days_lookback": 30,
            }
        }

        # Create a test instance
        await InstanceDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metrics_collection_status=Status.OK,
        )
        await test_async_session.commit()

        with patch(
            "airspan_acp_metrics.workers.metrics_audit_worker.get_db_session"
        ) as mock_get_session:
            mock_get_session.return_value.__aenter__.return_value = test_async_session

            with patch.object(
                metrics_audit_worker, "_create_instance_missing_metrics_audits"
            ) as mock_create_instance:
                mock_create_instance.return_value = None

                with patch("datetime.datetime") as mock_datetime:
                    mock_datetime.now.return_value = mock_now
                    mock_datetime.timezone = datetime.timezone

                    await metrics_audit_worker._create_missing_metrics_audits()

                    mock_create_instance.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_instance_missing_metrics_audits_no_time_range(
        self, metrics_audit_worker, test_async_session, mock_now
    ):
        """Test _create_instance_missing_metrics_audits when no valid time range."""
        instance = await InstanceDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metrics_collection_status=Status.OK,
        )
        await test_async_session.commit()
        await test_async_session.refresh(instance)

        metric_config = {"metric_name": "Stats", "frequency": 5, "max_days_lookback": 30}

        with patch.object(
            metrics_audit_worker, "_calculate_metric_time_range", return_value=None
        ):
            # Should return early without creating any audits
            await metrics_audit_worker._create_instance_missing_metrics_audits(
                instance_db_model=instance, metric_config=metric_config, now=mock_now
            )
            # Test passes if no exception is raised

    @pytest.mark.asyncio
    async def test_create_instance_missing_metrics_audits_exception_handling(
        self, metrics_audit_worker, test_async_session, mock_now
    ):
        """Test _create_instance_missing_metrics_audits handles exceptions gracefully."""
        instance = await InstanceDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metrics_collection_status=Status.OK,
        )
        await test_async_session.commit()
        await test_async_session.refresh(instance)

        metric_config = {"metric_name": "Stats", "frequency": 5, "max_days_lookback": 30}

        with patch.object(
            metrics_audit_worker,
            "_calculate_metric_time_range",
            side_effect=Exception("Test error"),
        ):
            # Should handle exception gracefully
            await metrics_audit_worker._create_instance_missing_metrics_audits(
                instance_db_model=instance, metric_config=metric_config, now=mock_now
            )
            # Test passes if no exception is raised

    @pytest.mark.asyncio
    async def test_get_existing_audit_intervals(self, metrics_audit_worker, test_async_session):
        """Test _get_existing_audit_intervals returns correct intervals with attempt counts."""
        # Create test audit records
        interval_start1 = datetime.datetime(2025, 1, 1, 10, 0, 0, tzinfo=datetime.UTC)
        interval_end1 = datetime.datetime(2025, 1, 1, 10, 5, 0, tzinfo=datetime.UTC)

        await MetricsAuditDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metric_name="Stats",
            interval_start=interval_start1,
            interval_end=interval_end1,
            collection_status=MetricsCollectionStatus.NOT_STARTED,
            attempt_count=1,
        )
        await test_async_session.commit()

        search_start = datetime.datetime(2025, 1, 1, 9, 0, 0, tzinfo=datetime.UTC)
        search_end = datetime.datetime(2025, 1, 1, 11, 0, 0, tzinfo=datetime.UTC)

        result = await metrics_audit_worker._get_existing_audit_intervals(
            db_session=test_async_session,
            instance_name="test-instance",
            metric_name="Stats",
            interval_start=search_start,
            interval_end=search_end,
            prefix_log="test",
        )

        assert len(result) == 1
        assert isinstance(result, dict)
        assert (interval_start1, interval_end1) in result
        assert result[(interval_start1, interval_end1)] == 1

    @pytest.mark.asyncio
    async def test_create_missing_audit_records_no_missing(
        self, metrics_audit_worker, test_async_session
    ):
        """Test _create_missing_audit_records when no missing audits."""
        # Set up metrics config for this test
        metrics_audit_worker.metrics_configs = {"Stats": {"max_retry_attempts": 3}}

        expected_intervals = [
            (
                datetime.datetime(2025, 1, 1, 10, 0, 0, tzinfo=datetime.UTC),
                datetime.datetime(2025, 1, 1, 10, 5, 0, tzinfo=datetime.UTC),
            )
        ]
        # Use dict format mapping intervals to attempt counts
        existing_intervals = {expected_intervals[0]: 1}

        await metrics_audit_worker._create_missing_audit_records(
            db_session=test_async_session,
            instance_name="test-instance",
            metric_name="Stats",
            expected_intervals=expected_intervals,
            existing_intervals=existing_intervals,
            prefix_log="test",
        )

        # Verify no new audits were created
        audit_service = MetricsAuditDbService(test_async_session)
        audits = await audit_service.get_instance_metrics_audits(
            metric_name="Stats",
            manager_instance="test-instance",
            interval_start=datetime.datetime(2025, 1, 1, 9, 0, 0, tzinfo=datetime.UTC),
            interval_end=datetime.datetime(2025, 1, 1, 11, 0, 0, tzinfo=datetime.UTC),
        )
        assert len(audits) == 0

    @pytest.mark.asyncio
    async def test_create_missing_audit_records_with_missing(
        self, metrics_audit_worker, test_async_session
    ):
        """Test _create_missing_audit_records creates missing audit records."""
        # Set up metrics config for this test
        metrics_audit_worker.metrics_configs = {"Stats": {"max_retry_attempts": 3}}

        expected_intervals = [
            (
                datetime.datetime(2025, 1, 1, 10, 0, 0, tzinfo=datetime.UTC),
                datetime.datetime(2025, 1, 1, 10, 5, 0, tzinfo=datetime.UTC),
            ),
            (
                datetime.datetime(2025, 1, 1, 10, 5, 0, tzinfo=datetime.UTC),
                datetime.datetime(2025, 1, 1, 10, 10, 0, tzinfo=datetime.UTC),
            ),
        ]
        # Only one interval exists with attempt_count=1
        existing_intervals = {expected_intervals[0]: 1}

        await metrics_audit_worker._create_missing_audit_records(
            db_session=test_async_session,
            instance_name="test-instance",
            metric_name="Stats",
            expected_intervals=expected_intervals,
            existing_intervals=existing_intervals,
            prefix_log="test",
        )

        # Verify one new audit was created
        audit_service = MetricsAuditDbService(test_async_session)
        audits = await audit_service.get_instance_metrics_audits(
            metric_name="Stats",
            manager_instance="test-instance",
            interval_start=datetime.datetime(2025, 1, 1, 9, 0, 0, tzinfo=datetime.UTC),
            interval_end=datetime.datetime(2025, 1, 1, 11, 0, 0, tzinfo=datetime.UTC),
        )
        assert len(audits) == 1
        assert audits[0].interval_start == expected_intervals[1][0]
        assert audits[0].interval_end == expected_intervals[1][1]
        assert audits[0].collection_status == MetricsCollectionStatus.NOT_STARTED
        assert audits[0].attempt_count == 1  # New audits should start with attempt_count=1

    # SHUTDOWN TESTS
    def test_shutdown_with_running_scheduler(self, metrics_audit_worker):
        """Test shutdown method when scheduler is running."""
        mock_scheduler = MagicMock()
        mock_scheduler.running = True
        metrics_audit_worker.scheduler = mock_scheduler
        metrics_audit_worker._is_running = True

        metrics_audit_worker.shutdown()

        mock_scheduler.shutdown.assert_called_once()
        assert metrics_audit_worker._is_running is False

    def test_shutdown_with_stopped_scheduler(self, metrics_audit_worker):
        """Test shutdown method when scheduler is not running."""
        mock_scheduler = MagicMock()
        mock_scheduler.running = False
        metrics_audit_worker.scheduler = mock_scheduler
        metrics_audit_worker._is_running = True

        metrics_audit_worker.shutdown()

        mock_scheduler.shutdown.assert_not_called()
        assert metrics_audit_worker._is_running is True

    def test_shutdown_no_scheduler(self, metrics_audit_worker):
        """Test shutdown method when scheduler doesn't exist."""
        if hasattr(metrics_audit_worker, "scheduler"):
            delattr(metrics_audit_worker, "scheduler")
        metrics_audit_worker._is_running = True

        # Should not raise exception
        metrics_audit_worker.shutdown()
        assert metrics_audit_worker._is_running is True
