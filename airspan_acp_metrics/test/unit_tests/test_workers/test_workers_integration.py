"""
Comprehensive integration tests for MetricsAuditWorker and MetricsCollectionWorker.
Tests the complete retry logic flow including max attempt handling, window function,
and retry scenarios specific to airspan-acp-metrics (SSH, file operations, GCS).

IMPORTANT: Following CLAUDE.md testing rules - only mock EXTERNAL dependencies:
- SSH connections (asyncssh library)
- GCS operations (google.cloud.storage)
- PubSub operations (google.cloud.pubsub)
- File system operations on remote servers

NEVER mock internal functions - use real database interactions and internal logic.
"""

import datetime
import io
import sys
from pathlib import Path
from unittest.mock import MagicMock
from unittest.mock import patch

import pytest
from da_common.models import Status

from airspan_acp_metrics.database.services.metric_audit_db_service import MetricsAuditDbService
from airspan_acp_metrics.managers import GCSOperationsManager
from airspan_acp_metrics.managers.metrics_collector_manager import MetricsCollector
from airspan_acp_metrics.pyd_models.utils import MetricsCollectionStatus
from airspan_acp_metrics.test.factories.instance_db_factory import InstanceDbFactory
from airspan_acp_metrics.test.factories.metrics_audit_db_factory import MetricsAuditDbFactory
from airspan_acp_metrics.test.unit_tests.test_managers.test_gcs_operations_manager import (
    read_zip_file_to_bytesio,
)
from airspan_acp_metrics.workers.metrics_audit_worker import MetricsAuditWorker
from airspan_acp_metrics.workers.metrics_collection_worker import MetricsCollectionWorker


# Mock external dependencies before any other imports
sys.modules["dal_pubsub"] = MagicMock()
sys.modules["dal_pubsub.pubsub"] = MagicMock()
sys.modules["google.cloud.storage"] = MagicMock()
sys.modules["google.cloud.pubsub_v1"] = MagicMock()
sys.modules["asyncssh"] = MagicMock()


class TestComprehensiveRetryIntegration:
    """Comprehensive integration tests for audit and collection workers retry logic."""

    @pytest.fixture
    def metrics_collector(self, test_config):
        """Create a MetricsCollector instance."""
        metrics_collector = MetricsCollector(test_config)
        metrics_collector.pub_sub = MagicMock()
        return metrics_collector

    @pytest.fixture
    def sample_zip_file_data(self) -> io.BytesIO:
        """Create sample zip file data with multiple files."""
        test_zip_path = (
            Path(__file__).parent.parent.parent
            / "test_data"
            / "acp-dauk-mrl-green-acp-Stats-20250607_0030_0035.zip"
        )
        return read_zip_file_to_bytesio(test_zip_path)

    @pytest.fixture
    def remote_file_metadata(self):
        """Create sample remote file metadata."""
        return {
            # Remote host metadata
            "file_size_bytes": 172335,
            "remote_file_mode": 33188,  # File permissions
            "remote_file_mtime_iso": "2025-06-07T00:35:11+00:00",  # ISO format
            "remote_file_atime_iso": "2025-06-07T00:35:11+00:00",  # ISO format
        }

    @pytest.mark.asyncio
    async def test_window_function_only_one_audit_per_interval_processed(
        self,
        test_async_session,
        test_async_session_maker,
        test_config,
    ):
        """
        Test window function ensures only ONE audit per interval is processed at a time.

        This is the core test for the window function logic that was implemented.
        Uses REAL internal functions, mocks only external dependencies.
        """
        # Create test instance with OK status
        instance_db_model = await InstanceDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance-window",
            metrics_collection_status=Status.OK,
        )
        await test_async_session.commit()
        await test_async_session.refresh(instance_db_model)

        audit_service = MetricsAuditDbService(test_async_session)

        # Create the SAME interval with multiple attempt_counts
        base_time = datetime.datetime(2025, 7, 10, 10, 0, 0, tzinfo=datetime.UTC)
        interval_start = base_time + datetime.timedelta(minutes=5)
        interval_end = interval_start + datetime.timedelta(minutes=5)

        # Create 3 audits for the SAME interval with different attempt_counts and statuses
        audit_1 = await MetricsAuditDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance-window",
            metric_name="Stats",
            interval_start=interval_start,
            interval_end=interval_end,
            collection_status=MetricsCollectionStatus.FAILED,
            attempt_count=1,
            reason="First attempt failed",
        )

        audit_2 = await MetricsAuditDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance-window",
            metric_name="Stats",
            interval_start=interval_start,
            interval_end=interval_end,
            collection_status=MetricsCollectionStatus.FAILED,
            attempt_count=2,
            reason="Second attempt failed",
        )

        audit_3 = await MetricsAuditDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance-window",
            metric_name="Stats",
            interval_start=interval_start,
            interval_end=interval_end,
            collection_status=MetricsCollectionStatus.NOT_STARTED,
            attempt_count=3,
        )

        await test_async_session.commit()
        await test_async_session.refresh(audit_1)
        await test_async_session.refresh(audit_2)
        await test_async_session.refresh(audit_3)

        # TEST: get_instance_audit_metrics_for_processing should return only audit_3 (highest attempt_count)
        audits_for_processing = await audit_service.get_instance_audit_metrics_for_processing(
            instance_name="test-instance-window", limit=10, max_retry_attempts=3
        )

        # Verify only ONE audit returned (the one with highest attempt_count=3)
        assert len(audits_for_processing) == 1
        returned_audit = audits_for_processing[0]
        assert returned_audit.id == audit_3.id
        assert returned_audit.attempt_count == 3
        assert returned_audit.collection_status == MetricsCollectionStatus.NOT_STARTED

        # Verify the other audits (with lower attempt_counts) are NOT returned
        returned_audit_ids = {audit.id for audit in audits_for_processing}
        assert audit_1.id not in returned_audit_ids
        assert audit_2.id not in returned_audit_ids

    @pytest.mark.asyncio
    async def test_complete_retry_cycle_with_max_attempts_exceeded(
        self,
        test_async_session,
        sample_instance_pyd_model,
        test_async_session_maker,
        test_config,
        sample_zip_file_data,
        metrics_collector,
        remote_file_metadata,
    ):
        """
        Test complete retry cycle - 3 failed attempts, then 4th attempt should be skipped.

        Uses REAL internal functions, mocks only external SSH/GCS/PubSub dependencies.
        """

        # Create test instance with OK status

        # Initialize workers - use REAL internal logic
        audit_worker = MetricsAuditWorker(test_config)
        audit_worker.async_session_maker = test_async_session_maker

        collection_worker = MetricsCollectionWorker(test_config)
        collection_worker.async_session_maker = test_async_session_maker

        audit_service = MetricsAuditDbService(test_async_session)

        # FIRST RUN: Audit worker creates audit (REAL function call)
        await audit_worker._create_missing_metrics_audits()
        await test_async_session.commit()

        # Verify audit created by REAL internal logic
        initial_audits = await audit_service.get_instance_metrics_audits(
            manager_instance=sample_instance_pyd_model.manager_instance,
            metric_name="Stats",
        )
        assert len(initial_audits) > 0
        # get second_audit, because sometimes first audit is skipped due to recent_buffer_minutes
        tracked_audit = initial_audits[1]
        assert tracked_audit.attempt_count == 1
        assert tracked_audit.collection_status == MetricsCollectionStatus.NOT_STARTED
        tracked_audit_interval = (tracked_audit.interval_start, tracked_audit.interval_end)

        # Mock successful external operations
        mock_file_metadata = {
            "file_count": 233,
            "file_hash_sha256": GCSOperationsManager._calculate_file_hash(sample_zip_file_data),
            "retry_attempts": 1,
            "successful_attempts": 1,
            **remote_file_metadata,
        }

        mock_gcs_path = "test-instance-1/Stats/20250101_1000_1005.zip"

        # Mock external dependencies at class level to affect all instances
        with (
            patch(
                "airspan_acp_metrics.managers.ssh_connection_manager.get_secret_data",
                return_value={"username": "test", "password": "test"},
            ),
            patch(
                "airspan_acp_metrics.managers.file_operations_manager.FileOperationsManager.download_file_from_remote",
                return_value=(None, "Exception while downloading remote file data", None),
            ),
            patch(
                "airspan_acp_metrics.managers.gcs_operations_manager.GCSOperationsManager.upload_file_to_gcs",
                return_value=(mock_file_metadata, mock_gcs_path),
            ),
        ):
            # Collection worker succeeds using REAL internal logic
            await collection_worker._execute_metrics_collection_cycle()
            await test_async_session.commit()

        # Verify first audit marked as FAILED by REAL internal logic
        await test_async_session.refresh(tracked_audit)
        assert tracked_audit.collection_status == MetricsCollectionStatus.FAILED
        assert "Exception while downloading remote file data" in tracked_audit.reason

        # ATTEMPT 2: Audit worker creates second audit (REAL function call)
        await audit_worker._create_missing_metrics_audits()
        await test_async_session.commit()

        # Verify second audit created with attempt_count=2 for the same interval
        audits_attempt_2 = await audit_service.get_instance_metrics_audits(
            manager_instance=sample_instance_pyd_model.manager_instance,
            metric_name="Stats",
        )
        second_attempt_audits = [
            a
            for a in audits_attempt_2
            if a.attempt_count == 2
            and (a.interval_start, a.interval_end) == tracked_audit_interval
        ]
        assert len(second_attempt_audits) > 0
        tracked_audit = second_attempt_audits[0]
        assert tracked_audit.collection_status == MetricsCollectionStatus.NOT_STARTED

        # Mock external dependencies at class level to affect all instances
        with (
            patch(
                "airspan_acp_metrics.managers.ssh_connection_manager.get_secret_data",
                return_value={"username": "test", "password": "test"},
            ),
            patch(
                "airspan_acp_metrics.managers.file_operations_manager.FileOperationsManager.download_file_from_remote",
                return_value=(None, "Exception while downloading remote file data", None),
            ),
            patch(
                "airspan_acp_metrics.managers.gcs_operations_manager.GCSOperationsManager.upload_file_to_gcs",
                return_value=(mock_file_metadata, mock_gcs_path),
            ),
        ):
            # Collection worker succeeds using REAL internal logic
            await collection_worker._execute_metrics_collection_cycle()
            await test_async_session.commit()

        # Verify second audit marked as FAILED by REAL internal logic
        await test_async_session.refresh(tracked_audit)
        assert tracked_audit.collection_status == MetricsCollectionStatus.FAILED
        assert "Exception while downloading remote file data" in tracked_audit.reason

        # ATTEMPT 3: Audit worker creates third audit (REAL function call)
        await audit_worker._create_missing_metrics_audits()
        await test_async_session.commit()

        # Verify third audit created with attempt_count=3 for the same interval
        audits_attempt_3 = await audit_service.get_instance_metrics_audits(
            manager_instance=sample_instance_pyd_model.manager_instance,
            metric_name="Stats",
        )
        third_attempt_audits = [
            a
            for a in audits_attempt_3
            if a.attempt_count == 3
            and (a.interval_start, a.interval_end) == tracked_audit_interval
        ]
        assert len(third_attempt_audits) > 0
        third_audit = third_attempt_audits[0]
        assert third_audit.collection_status == MetricsCollectionStatus.NOT_STARTED

        # Mock external dependencies at class level to affect all instances
        with (
            patch(
                "airspan_acp_metrics.managers.ssh_connection_manager.get_secret_data",
                return_value={"username": "test", "password": "test"},
            ),
            patch(
                "airspan_acp_metrics.managers.file_operations_manager.FileOperationsManager.download_file_from_remote",
                return_value=(None, "Exception while downloading remote file data", None),
            ),
            patch(
                "airspan_acp_metrics.managers.gcs_operations_manager.GCSOperationsManager.upload_file_to_gcs",
                return_value=(mock_file_metadata, mock_gcs_path),
            ),
        ):
            # Collection worker succeeds using REAL internal logic
            await collection_worker._execute_metrics_collection_cycle()
            await test_async_session.commit()

        # Verify third audit marked as FAILED by REAL internal logic
        await test_async_session.refresh(third_audit)
        assert third_audit.collection_status == MetricsCollectionStatus.FAILED
        assert "Exception while downloading remote file data" in third_audit.reason

        # ATTEMPT 4: Audit worker should now skip creating more audits (REAL logic checks max attempts)
        await audit_worker._create_missing_metrics_audits()
        await test_async_session.commit()

        # Verify no new audits created by REAL internal logic (since max attempts exceeded)
        final_audits = await audit_service.get_instance_metrics_audits(
            manager_instance=sample_instance_pyd_model.manager_instance,
            metric_name="Stats",
        )

        # Check audits for our tracked interval specifically
        interval_audits = [
            a
            for a in final_audits
            if (a.interval_start, a.interval_end) == tracked_audit_interval
        ]

        # We should have exactly 3 audits for this interval (attempts 1, 2, 3)
        # No 4th audit should be created since REAL logic prevents it
        assert len(interval_audits) == 3

        # All should be FAILED status
        failed_audits = [
            a for a in interval_audits if a.collection_status == MetricsCollectionStatus.FAILED
        ]
        assert len(failed_audits) == 3

        # Verify attempt counts
        attempt_counts = sorted([a.attempt_count for a in interval_audits])
        assert attempt_counts == [1, 2, 3]

    @pytest.mark.asyncio
    async def test_successful_collection_stops_retry_cycle(
        self,
        test_async_session,
        test_async_session_maker,
        test_config,
        sample_zip_file_data,
        sample_instance_pyd_model,
        metrics_collector,
        remote_file_metadata,
    ):
        """
        Test: When metrics collection succeeds, no more audits should be created for that interval.

        Uses REAL internal functions, mocks only external SSH/GCS/PubSub dependencies.
        """

        # Initialize workers - use REAL internal logic
        audit_worker = MetricsAuditWorker(test_config)
        audit_worker.async_session_maker = test_async_session_maker

        collection_worker = MetricsCollectionWorker(test_config)
        collection_worker.async_session_maker = test_async_session_maker

        audit_service = MetricsAuditDbService(test_async_session)

        # FIRST RUN: Audit worker creates audit (REAL function call)
        await audit_worker._create_missing_metrics_audits()
        await test_async_session.commit()

        # Verify audit created by REAL internal logic
        initial_audits = await audit_service.get_instance_metrics_audits(
            manager_instance=sample_instance_pyd_model.manager_instance,
            metric_name="Stats",
        )
        assert len(initial_audits) > 0
        # get second_audit, because sometimes first audit is skipped due to recent_buffer_minutes
        second_audit = initial_audits[1]
        assert second_audit.attempt_count == 1
        assert second_audit.collection_status == MetricsCollectionStatus.NOT_STARTED
        second_audit_interval = (second_audit.interval_start, second_audit.interval_end)

        # Mock successful external operations
        mock_file_metadata = {
            "file_count": 233,
            "file_hash_sha256": GCSOperationsManager._calculate_file_hash(sample_zip_file_data),
            "retry_attempts": 1,
            "successful_attempts": 1,
            **remote_file_metadata,
        }

        mock_gcs_path = "test-instance-1/Stats/20250101_1000_1005.zip"

        # Mock external dependencies at class level to affect all instances
        with (
            patch(
                "airspan_acp_metrics.managers.ssh_connection_manager.get_secret_data",
                return_value={"username": "test", "password": "test"},
            ),
            patch(
                "airspan_acp_metrics.managers.file_operations_manager.FileOperationsManager.download_file_from_remote",
                return_value=(sample_zip_file_data, "", remote_file_metadata),
            ),
            patch(
                "airspan_acp_metrics.managers.gcs_operations_manager.GCSOperationsManager.upload_file_to_gcs",
                return_value=(mock_file_metadata, mock_gcs_path),
            ),
        ):
            # Collection worker succeeds using REAL internal logic
            await collection_worker._execute_metrics_collection_cycle()
            await test_async_session.commit()

        # Verify audit marked as COLLECTED by REAL internal logic
        await test_async_session.refresh(second_audit)
        assert second_audit.collection_status == MetricsCollectionStatus.COLLECTED

        # SECOND RUN: Audit worker should NOT create more audits (REAL logic checks COLLECTED status)
        await audit_worker._create_missing_metrics_audits()
        await test_async_session.commit()

        # Verify no new audits created by REAL internal logic for the same intervals
        final_audits = await audit_service.get_instance_metrics_audits(
            manager_instance=sample_instance_pyd_model.manager_instance,
            metric_name="Stats",
        )

        # Check audits for our tracked interval specifically
        interval_audits = [
            a
            for a in final_audits
            if (a.interval_start, a.interval_end) == second_audit_interval
        ]

        # Should still only have 1 audit for this interval (the successful one)
        assert len(interval_audits) == 1
        assert interval_audits[0].collection_status == MetricsCollectionStatus.COLLECTED

    @pytest.mark.asyncio
    async def test_max_limit_reached_reason_appending(
        self,
        test_async_session,
        test_async_session_maker,
        test_config,
    ):
        """
        Test that 'max_limit reached' is properly appended to the reason when max attempts are reached.

        Uses REAL internal functions to test the actual reason appending logic.
        """
        # Create test instance
        await InstanceDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance-maxlimit",
            metrics_collection_status=Status.OK,
        )
        await test_async_session.commit()

        # Configure test with max_retry_attempts = 2 for faster testing
        test_config.data["metrics"] = {
            "stats": {
                "enabled": True,
                "metric_name": "Stats",
                "frequency": 5,
                "backfill_enabled": True,
                "max_retry_attempts": 2,
                "max_days_lookback": 1,
            }
        }

        collection_worker = MetricsCollectionWorker(test_config)
        collection_worker.async_session_maker = test_async_session_maker

        # Create an audit that has reached max attempts
        interval_start = datetime.datetime.now(datetime.UTC) - datetime.timedelta(minutes=10)
        interval_end = interval_start + datetime.timedelta(minutes=5)

        audit = await MetricsAuditDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance-maxlimit",
            metric_name="Stats",
            interval_start=interval_start,
            interval_end=interval_end,
            collection_status=MetricsCollectionStatus.FAILED,
            attempt_count=2,  # At max limit
            reason="Previous failure reason",
        )
        await test_async_session.commit()
        await test_async_session.refresh(audit)

        # Test the REAL max limit handling logic
        audit_service = MetricsAuditDbService(test_async_session)
        current_time = datetime.datetime.now(datetime.UTC)

        # Call REAL internal function
        await collection_worker._handle_exhausted_retry_attempts(
            audit_service=audit_service,
            audit_record=audit,
            max_retry_attempts=2,
            current_time=current_time,
            prefix_log="test",
        )
        await test_async_session.commit()

        # Verify the REAL logic updated the reason with max_limit reached
        await test_async_session.refresh(audit)
        assert audit.collection_status == MetricsCollectionStatus.SKIPPED, (
            "Status should be updated to SKIPPED"
        )
        assert "max_limit reached" in audit.reason
        assert "Previous failure reason" in audit.reason
