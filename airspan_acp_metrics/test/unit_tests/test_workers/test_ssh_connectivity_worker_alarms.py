from unittest.mock import MagicMock
from unittest.mock import patch

import pytest
from da_common.models import Status

from airspan_acp_metrics.database.services.alarm_db_service import AlarmDbService
from airspan_acp_metrics.pyd_models.alarm_enums import AlarmStatus
from airspan_acp_metrics.pyd_models.alarm_enums import AlarmType
from airspan_acp_metrics.pyd_models.instance_pyd_model import InstancePydModel
from airspan_acp_metrics.test.factories.alarm_db_factory import AlarmDbFactory
from airspan_acp_metrics.test.factories.alarm_type_db_factory import AlarmTypeDbFactory
from airspan_acp_metrics.test.factories.instance_db_factory import InstanceDbFactory
from airspan_acp_metrics.workers.ssh_connectivity_worker import SSHConnectivityWorker


@pytest.mark.asyncio
class TestSSHConnectivityWorkerAlarms:
    """Test cases for SSH connectivity worker alarm integration."""

    @pytest.fixture
    def mock_config(self):
        """Create a mock config."""
        config = MagicMock()
        config.data = {
            "db": {
                "host": "localhost",
                "port": 5432,
                "database": "test",
                "user": "test",
                "password": "test",
            },
            "security": {"apikeys": {"nms": {"key": "test-key"}}},
            "pubsub": {"alarm": {"topic": "test-alarm-topic"}},
        }
        config.build_clients = MagicMock(return_value={})
        return config

    @pytest.fixture
    def mock_pubsub(self):
        """Create a mock pubsub."""
        pubsub = MagicMock()
        pubsub.set_topic = MagicMock()
        pubsub.push_payload = MagicMock()
        return pubsub

    @pytest.fixture
    def ssh_worker(self, mock_config):
        """Create an SSH connectivity worker instance."""
        with patch(
            "airspan_acp_metrics.workers.ssh_connectivity_worker.PubSub"
        ) as mock_pubsub_class:
            mock_pubsub = MagicMock()
            mock_pubsub_class.return_value = mock_pubsub
            worker = SSHConnectivityWorker(mock_config)
            worker.pubsub = mock_pubsub
            return worker

    async def test_handle_connectivity_alarms_raise_alarm(self, test_async_session, ssh_worker):
        """Test handling connectivity alarms when raising an alarm."""
        # Create test instance and alarm type
        instance_db_model = await InstanceDbFactory(async_session=test_async_session)
        await AlarmTypeDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
        )

        await test_async_session.commit()
        await test_async_session.refresh(instance_db_model)
        instance_pyd_model = InstancePydModel.model_validate(instance_db_model)
        manager_instance = instance_pyd_model.manager_instance

        # Create connectivity result indicating failure
        connectivity_result = {
            "status": Status.ERROR.value,
            "reason": "SSH connection failed",
            "connection_successful": False,
        }

        # Call the method
        await ssh_worker._handle_connectivity_alarms(
            test_async_session,
            instance_pyd_model,
            connectivity_result,
            "TestPrefix",
        )

        alarm_service = AlarmDbService(test_async_session)
        active_alarms = await alarm_service.get_active_alarms_by_instance(manager_instance)

        assert len(active_alarms) == 1
        alarm = active_alarms[0]
        assert alarm.type == AlarmType.SSH_CONNECTIVITY_FAILED
        assert alarm.name == f"SSH connectivity failed for {manager_instance}"
        assert alarm.source == "ssh_connectivity_worker"
        assert alarm.description == "SSH connectivity test failed: SSH connection failed"
        assert alarm.status == AlarmStatus.new

        # Verify pubsub was called
        ssh_worker.pubsub.set_topic.assert_called_once_with("test-alarm-topic")
        ssh_worker.pubsub.push_payload.assert_called_once()

    async def test_handle_connectivity_alarms_clear_alarm(self, test_async_session, ssh_worker):
        """Test handling connectivity alarms when clearing an alarm."""
        # Create test instance and alarm type
        instance_db_model = await InstanceDbFactory(async_session=test_async_session)
        manager_instance = instance_db_model.manager_instance
        await AlarmTypeDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
        )
        await test_async_session.commit()
        await test_async_session.refresh(instance_db_model)
        instance_pyd_model = InstancePydModel.model_validate(instance_db_model)

        # Create existing alarm
        existing_alarm_db_model = await AlarmDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
            manager_instance=manager_instance,
            status=AlarmStatus.new,
        )

        # Create connectivity result indicating success
        connectivity_result = {
            "status": Status.OK.value,
            "reason": "Ready for metrics collection",
            "connection_successful": True,
        }

        # Call the method
        await ssh_worker._handle_connectivity_alarms(
            test_async_session,
            instance_pyd_model,
            connectivity_result,
            "TestPrefix",
        )

        # Refresh the alarm to get updated status
        await test_async_session.refresh(existing_alarm_db_model)
        assert existing_alarm_db_model.status == AlarmStatus.resolved
        assert existing_alarm_db_model.resolver == "ssh_connectivity_worker"
        assert existing_alarm_db_model.resolved_at is not None

        # Verify pubsub was called
        ssh_worker.pubsub.set_topic.assert_called_once_with("test-alarm-topic")
        ssh_worker.pubsub.push_payload.assert_called_once()

    async def test_handle_connectivity_alarms_exception(self, test_async_session, ssh_worker):
        """Test handling connectivity alarms when an exception occurs."""
        # Create test instance
        instance_db_model = await InstanceDbFactory(async_session=test_async_session)
        await test_async_session.commit()
        await test_async_session.refresh(instance_db_model)
        instance_pyd_model = InstancePydModel.model_validate(instance_db_model)

        # Create connectivity result
        connectivity_result = {
            "status": Status.ERROR.value,
            "reason": "SSH connection failed",
            "connection_successful": False,
        }

        # Mock the alarm manager's raise_alarm method to raise an exception
        with patch(
            "airspan_acp_metrics.managers.alarm_manager.AlarmManager.raise_alarm"
        ) as mock_raise_alarm:
            mock_raise_alarm.side_effect = Exception("Test exception")

            # Call the method (should not raise exception)
            await ssh_worker._handle_connectivity_alarms(
                test_async_session,
                instance_pyd_model,
                connectivity_result,
                "TestPrefix",
            )

            # Verify that the exception was caught and handled gracefully
            mock_raise_alarm.assert_called_once()

    async def test_check_instance_connectivity_with_alarm_integration(
        self, test_async_session, ssh_worker
    ):
        """Test the instance connectivity check with alarm integration."""
        # Create test instance and alarm type
        instance_db_model = await InstanceDbFactory(async_session=test_async_session)
        await AlarmTypeDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
        )
        await test_async_session.commit()
        await test_async_session.refresh(instance_db_model)
        instance_pyd_model = InstancePydModel.model_validate(instance_db_model)

        # Mock the SSH connection to fail
        with patch.object(ssh_worker, "_get_instance_connectivity_report") as mock_get_report:
            mock_get_report.return_value = {
                "status": Status.ERROR.value,
                "reason": "SSH connection failed",
                "connection_successful": False,
            }

            # Mock the database operations
            with patch.object(
                ssh_worker.db_ops, "update_instance_metrics_collection_status"
            ) as mock_update:
                mock_update.return_value = None

                # Call the method
                result = await ssh_worker._check_instance_connectivity(
                    test_async_session, instance_pyd_model
                )

                # Verify result
                assert result is True

                # Verify alarm was created in the database
                alarm_service = AlarmDbService(test_async_session)
                active_alarms = await alarm_service.get_active_alarms_by_instance(
                    instance_pyd_model.manager_instance
                )

                assert len(active_alarms) == 1
                alarm = active_alarms[0]
                assert alarm.type == AlarmType.SSH_CONNECTIVITY_FAILED
                assert alarm.source == "ssh_connectivity_worker"

    async def test_handle_connectivity_check_error_with_alarm(
        self, test_async_session, ssh_worker
    ):
        """Test handling connectivity check errors with alarm integration."""
        # Create test instance and alarm type
        instance_db_model = await InstanceDbFactory(async_session=test_async_session)
        await AlarmTypeDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
        )
        await test_async_session.commit()
        await test_async_session.refresh(instance_db_model)
        instance_pyd_model = InstancePydModel.model_validate(instance_db_model)

        # Mock the database operations
        with patch.object(
            ssh_worker.db_ops, "update_instance_metrics_collection_status"
        ) as mock_update:
            mock_update.return_value = None

            # Call the method
            result = await ssh_worker._handle_connectivity_check_error(
                test_async_session,
                instance_pyd_model,
                Exception("Test error"),
                "TestPrefix",
            )

            # Verify result
            assert result is True

            # Verify alarm was created in the database
            alarm_service = AlarmDbService(test_async_session)
            active_alarms = await alarm_service.get_active_alarms_by_instance(
                instance_pyd_model.manager_instance
            )

            assert len(active_alarms) == 1
            alarm = active_alarms[0]
            assert alarm.type == AlarmType.SSH_CONNECTIVITY_FAILED
            assert (
                alarm.name
                == f"SSH connectivity check failed for {instance_pyd_model.manager_instance}"
            )
            assert alarm.source == "ssh_connectivity_worker"
            assert "Connectivity check failed: Test error" in alarm.description

    async def test_handle_connectivity_alarms_no_duplicate_alarms(
        self, test_async_session, ssh_worker
    ):
        """Test that duplicate alarms are not created when one already exists."""
        # Create test instance and alarm type
        instance_db_model = await InstanceDbFactory(async_session=test_async_session)
        await AlarmTypeDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
        )
        await test_async_session.commit()
        await test_async_session.refresh(instance_db_model)
        instance_pyd_model = InstancePydModel.model_validate(instance_db_model)

        # Create existing alarm
        existing_alarm_db_model = await AlarmDbFactory(
            async_session=test_async_session,
            type=AlarmType.SSH_CONNECTIVITY_FAILED,
            manager_instance=instance_db_model.manager_instance,
            status=AlarmStatus.new,
        )

        # Create connectivity result indicating failure
        connectivity_result = {
            "status": Status.ERROR.value,
            "reason": "SSH connection failed",
            "connection_successful": False,
        }

        # Call the method
        await ssh_worker._handle_connectivity_alarms(
            test_async_session,
            instance_pyd_model,
            connectivity_result,
            "TestPrefix",
        )

        # Verify no duplicate alarm was created
        from airspan_acp_metrics.database.services.alarm_db_service import AlarmDbService

        alarm_service = AlarmDbService(test_async_session)
        active_alarms = await alarm_service.get_active_alarms_by_instance(
            instance_pyd_model.manager_instance
        )

        assert len(active_alarms) == 1
        assert active_alarms[0].id == existing_alarm_db_model.id

        # Verify pubsub was not called (no new alarm)
        ssh_worker.pubsub.set_topic.assert_not_called()
        ssh_worker.pubsub.push_payload.assert_not_called()
