from unittest.mock import AsyncMock
from unittest.mock import MagicMock
from unittest.mock import patch

import pytest
from da_common.models import Status

from airspan_acp_metrics.pyd_models.instance_pyd_model import InstancePydModel
from airspan_acp_metrics.test.factories.instance_db_factory import InstanceDbFactory
from airspan_acp_metrics.workers.ssh_connectivity_worker import SSHConnectivityWorker


class TestSSHConnectivityWorker:
    """Tests for the SSHConnectivityWorker class."""

    @pytest.fixture
    def ssh_connectivity_worker(self, test_config):
        """Create an SSHConnectivityWorker instance."""
        return SSHConnectivityWorker(test_config)

    @pytest.fixture
    def mock_ssh_connection(self):
        """Create a mock SSH connection."""
        connection = MagicMock()
        connection.__aenter__ = AsyncMock(return_value=connection)
        connection.__aexit__ = AsyncMock(return_value=None)
        return connection

    def test_get_log_prefix_with_instance_name(self, ssh_connectivity_worker):
        """Test _get_log_prefix with instance name."""
        result = ssh_connectivity_worker._get_log_prefix("test-instance")
        assert result == "SSHConnectivityChecker: test-instance,"

    def test_get_log_prefix_without_instance_name(self, ssh_connectivity_worker):
        """Test _get_log_prefix without instance name."""
        result = ssh_connectivity_worker._get_log_prefix()
        assert result == "SSHConnectivityChecker:"

    def test_start_when_not_running(self, ssh_connectivity_worker):
        """Test start method when worker is not running."""
        # Ensure worker is not running
        ssh_connectivity_worker._is_running = False

        with (
            patch.object(ssh_connectivity_worker.scheduler, "add_job") as mock_add_job,
            patch.object(ssh_connectivity_worker.scheduler, "start") as mock_start,
        ):
            ssh_connectivity_worker.start()

            # Verify scheduler setup
            mock_add_job.assert_called_once()
            mock_start.assert_called_once()
            assert ssh_connectivity_worker._is_running is True

    def test_start_when_already_running(self, ssh_connectivity_worker):
        """Test start method when worker is already running."""
        # Set worker as already running
        ssh_connectivity_worker._is_running = True

        with (
            patch.object(ssh_connectivity_worker.scheduler, "add_job") as mock_add_job,
            patch.object(ssh_connectivity_worker.scheduler, "start") as mock_start,
        ):
            ssh_connectivity_worker.start()

            # Verify scheduler methods were not called
            mock_add_job.assert_not_called()
            mock_start.assert_not_called()

    def test_shutdown(self, ssh_connectivity_worker):
        """Test shutdown method."""
        # Mock the scheduler to simulate it running
        mock_scheduler = MagicMock()
        mock_scheduler.running = True
        ssh_connectivity_worker.scheduler = mock_scheduler
        ssh_connectivity_worker._is_running = True

        ssh_connectivity_worker.shutdown()

        mock_scheduler.shutdown.assert_called_once()
        assert ssh_connectivity_worker._is_running is False

    def test_shutdown_when_scheduler_not_running(self, ssh_connectivity_worker):
        """Test shutdown method when scheduler is not running."""
        # Remove scheduler attribute to test hasattr check
        delattr(ssh_connectivity_worker, "scheduler")

        # Should not raise an exception
        ssh_connectivity_worker.shutdown()

    @pytest.mark.asyncio
    async def test_check_all_instances_connectivity_no_instances(self, ssh_connectivity_worker):
        """Test _check_all_instances_connectivity when no instances exist."""
        with patch(
            "airspan_acp_metrics.workers.ssh_connectivity_worker.get_db_session"
        ) as mock_get_session:
            mock_session = AsyncMock()
            mock_get_session.return_value.__aenter__.return_value = mock_session

            mock_instance_service = AsyncMock()
            mock_instance_service.get_all_acp_instances.return_value = []

            with (
                patch(
                    "airspan_acp_metrics.workers.ssh_connectivity_worker.InstanceDbService",
                    return_value=mock_instance_service,
                ),
                patch.object(
                    ssh_connectivity_worker.instance_reconciliation,
                    "reconcile_airspan_acp_agent_instances",
                ) as mock_reconcile,
            ):
                await ssh_connectivity_worker._check_all_instances_connectivity()

                mock_reconcile.assert_called_once()
                mock_instance_service.get_all_acp_instances.assert_called_once()

    @pytest.mark.asyncio
    async def test_check_all_instances_connectivity_with_instances_success(
        self, ssh_connectivity_worker, test_async_session
    ):
        """Test _check_all_instances_connectivity with instances and successful connectivity checks."""
        # Create test instances
        instance1 = await InstanceDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance-1",
            url="http://test-url-1",
            host_ip_address="***********",
            host_secret_id="secret-1",
            metrics_collection_status=Status.ERROR,
        )
        instance2 = await InstanceDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance-2",
            url="http://test-url-2",
            host_ip_address="***********",
            host_secret_id="secret-2",
            metrics_collection_status=Status.OK,
        )
        await test_async_session.commit()
        await test_async_session.refresh(instance1)
        await test_async_session.refresh(instance2)

        with patch(
            "airspan_acp_metrics.workers.ssh_connectivity_worker.get_db_session"
        ) as mock_get_session:
            mock_session = AsyncMock()
            mock_get_session.return_value.__aenter__.return_value = mock_session

            mock_instance_service = AsyncMock()
            mock_instance_service.get_all_acp_instances.return_value = [instance1, instance2]

            with (
                patch(
                    "airspan_acp_metrics.workers.ssh_connectivity_worker.InstanceDbService",
                    return_value=mock_instance_service,
                ),
                patch.object(
                    ssh_connectivity_worker.instance_reconciliation,
                    "reconcile_airspan_acp_agent_instances",
                ),
                patch.object(
                    ssh_connectivity_worker, "_check_instance_connectivity"
                ) as mock_check_connectivity,
            ):
                # Mock connectivity check results
                mock_check_connectivity.side_effect = [
                    True,
                    False,
                ]  # First changed, second didn't

                await ssh_connectivity_worker._check_all_instances_connectivity()

                mock_instance_service.get_all_acp_instances.assert_called_once()
                assert mock_check_connectivity.call_count == 2
                mock_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_check_all_instances_connectivity_with_exception(
        self, ssh_connectivity_worker, test_async_session
    ):
        """Test _check_all_instances_connectivity handles exceptions properly."""
        instance = await InstanceDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            url="http://test-url",
            host_ip_address="***********",
            host_secret_id="secret-1",
            metrics_collection_status=Status.OK,
        )
        await test_async_session.commit()
        await test_async_session.refresh(instance)

        with patch(
            "airspan_acp_metrics.workers.ssh_connectivity_worker.get_db_session"
        ) as mock_get_session:
            mock_session = AsyncMock()
            mock_get_session.return_value.__aenter__.return_value = mock_session

            mock_instance_service = AsyncMock()
            mock_instance_service.get_all_acp_instances.return_value = [instance]

            with (
                patch(
                    "airspan_acp_metrics.workers.ssh_connectivity_worker.InstanceDbService",
                    return_value=mock_instance_service,
                ),
                patch.object(
                    ssh_connectivity_worker.instance_reconciliation,
                    "reconcile_airspan_acp_agent_instances",
                ),
                patch.object(
                    ssh_connectivity_worker, "_check_instance_connectivity"
                ) as mock_check_connectivity,
            ):
                # Mock connectivity check to raise exception
                mock_check_connectivity.side_effect = Exception("Test exception")

                await ssh_connectivity_worker._check_all_instances_connectivity()

                mock_check_connectivity.assert_called_once()
                # Should still try to commit despite individual instance errors
                mock_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_check_all_instances_connectivity_database_exception(
        self, ssh_connectivity_worker
    ):
        """Test _check_all_instances_connectivity handles database exceptions."""
        with patch(
            "airspan_acp_metrics.workers.ssh_connectivity_worker.get_db_session"
        ) as mock_get_session:
            mock_session = AsyncMock()
            mock_get_session.return_value.__aenter__.return_value = mock_session

            mock_instance_service = AsyncMock()
            mock_instance_service.get_all_acp_instances.side_effect = Exception(
                "Database error"
            )

            with (
                patch(
                    "airspan_acp_metrics.workers.ssh_connectivity_worker.InstanceDbService",
                    return_value=mock_instance_service,
                ),
                patch.object(
                    ssh_connectivity_worker.instance_reconciliation,
                    "reconcile_airspan_acp_agent_instances",
                ),
            ):
                await ssh_connectivity_worker._check_all_instances_connectivity()

                mock_session.rollback.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_instance_connectivity_report_successful_connection(
        self, ssh_connectivity_worker, test_async_session, mock_ssh_connection
    ):
        """Test _get_instance_connectivity_report with successful SSH connection."""
        instance = await InstanceDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            url="http://test-url",
            host_ip_address="***********",
            host_secret_id="secret-1",
            metrics_collection_status=Status.ERROR,
        )
        await test_async_session.commit()
        await test_async_session.refresh(instance)
        instance_pyd_model = InstancePydModel.model_validate(instance)

        with (
            patch.object(
                ssh_connectivity_worker.ssh_manager, "get_connection"
            ) as mock_get_connection,
            patch.object(
                ssh_connectivity_worker.ssh_manager, "close_connection"
            ) as mock_close_connection,
        ):
            mock_get_connection.return_value = (mock_ssh_connection, {"status": Status.OK})

            result = await ssh_connectivity_worker._get_instance_connectivity_report(
                instance_pyd_model, "test-prefix"
            )

            assert result["status"] == Status.OK
            assert result["reason"] == "Ready for metrics collection"
            assert result["connection_successful"] is True

            mock_get_connection.assert_called_once()
            mock_close_connection.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_instance_connectivity_report_failed_connection(
        self, ssh_connectivity_worker, test_async_session
    ):
        """Test _get_instance_connectivity_report with failed SSH connection."""
        instance = await InstanceDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metrics_collection_status=Status.OK,
        )
        await test_async_session.commit()
        await test_async_session.refresh(instance)
        instance_pyd_model = InstancePydModel.model_validate(instance)

        error_reason = "Connection timeout"
        with patch.object(
            ssh_connectivity_worker.ssh_manager, "get_connection"
        ) as mock_get_connection:
            mock_get_connection.return_value = (None, {"reason": error_reason})

            result = await ssh_connectivity_worker._get_instance_connectivity_report(
                instance_pyd_model, "test-prefix"
            )

            assert result["status"] == Status.ERROR
            assert result["reason"] == error_reason
            assert result["connection_successful"] is False

            mock_get_connection.assert_called_once()

    def test_should_update_status_status_changed(
        self, ssh_connectivity_worker, test_async_session
    ):
        """Test _should_update_status when status has changed."""
        # Create a mock instance
        instance = MagicMock()
        instance.metrics_collection_status = Status.ERROR
        instance.reason = "Old reason"

        connectivity_result = {
            "status": Status.OK,
            "reason": "New reason",
            "connection_successful": True,
        }

        result = ssh_connectivity_worker._should_update_status(instance, connectivity_result)
        assert result is True

    def test_should_update_status_reason_changed(self, ssh_connectivity_worker):
        """Test _should_update_status when only reason has changed."""
        instance = MagicMock()
        instance.metrics_collection_status = Status.ERROR
        instance.reason = "Old reason"

        connectivity_result = {
            "status": Status.ERROR,
            "reason": "New reason",
            "connection_successful": False,
        }

        result = ssh_connectivity_worker._should_update_status(instance, connectivity_result)
        assert result is True

    def test_should_update_status_no_change(self, ssh_connectivity_worker):
        """Test _should_update_status when nothing has changed."""
        instance = MagicMock()
        instance.metrics_collection_status = Status.OK
        instance.reason = "Ready for metrics collection"

        connectivity_result = {
            "status": Status.OK.value,
            "reason": "Ready for metrics collection",
            "connection_successful": True,
        }

        result = ssh_connectivity_worker._should_update_status(instance, connectivity_result)
        assert result is False

    @pytest.mark.asyncio
    async def test_update_instance_status(self, ssh_connectivity_worker, test_async_session):
        """Test _update_instance_status calls database operations manager."""
        instance = await InstanceDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metrics_collection_status=Status.ERROR,
            reason="Old error",
        )
        await test_async_session.commit()
        await test_async_session.refresh(instance)

        connectivity_result = {
            "status": Status.OK.value,
            "reason": "Connection successful",
            "connection_successful": True,
        }

        await ssh_connectivity_worker._update_instance_status(
            test_async_session, "test-instance", connectivity_result, "test-prefix"
        )

        await test_async_session.refresh(instance)
        assert instance.metrics_collection_status == Status.OK.value
        assert instance.reason == "Connection successful"

    def test_log_status_change_to_ok(self, ssh_connectivity_worker):
        """Test _log_status_change when status changes to OK."""
        connectivity_result = {
            "status": Status.OK,
            "reason": "Ready for metrics collection",
            "connection_successful": True,
        }

        # This method just logs, so we verify it doesn't raise exceptions
        ssh_connectivity_worker._log_status_change(
            "test-prefix", connectivity_result, Status.ERROR
        )

    def test_log_status_change_to_error(self, ssh_connectivity_worker):
        """Test _log_status_change when status changes to ERROR."""
        connectivity_result = {
            "status": Status.ERROR,
            "reason": "Connection failed",
            "connection_successful": False,
        }

        # This method just logs, so we verify it doesn't raise exceptions
        ssh_connectivity_worker._log_status_change(
            "test-prefix", connectivity_result, Status.OK
        )

    @pytest.mark.asyncio
    async def test_check_instance_connectivity_status_changed(
        self, ssh_connectivity_worker, test_async_session, mock_ssh_connection
    ):
        """Test _check_instance_connectivity when status changes."""
        instance = await InstanceDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metrics_collection_status=Status.ERROR,
            reason="Old error",
        )
        await test_async_session.commit()
        await test_async_session.refresh(instance)
        instance_pyd_model = InstancePydModel.model_validate(instance)

        with (
            patch.object(
                ssh_connectivity_worker, "_get_instance_connectivity_report"
            ) as mock_get_report,
            patch.object(
                ssh_connectivity_worker, "_should_update_status"
            ) as mock_should_update,
            patch.object(
                ssh_connectivity_worker, "_update_instance_status"
            ) as mock_update_status,
            patch.object(ssh_connectivity_worker, "_log_status_change") as mock_log_change,
        ):
            # Mock the connectivity report and status update decision
            connectivity_report = {"status": Status.OK, "reason": "Connection successful"}
            mock_get_report.return_value = connectivity_report
            mock_should_update.return_value = True

            result = await ssh_connectivity_worker._check_instance_connectivity(
                test_async_session, instance_pyd_model
            )

            assert result is True
            mock_get_report.assert_called_once()
            mock_should_update.assert_called_once()
            mock_update_status.assert_called_once()
            mock_log_change.assert_called_once()

    @pytest.mark.asyncio
    async def test_check_instance_connectivity_status_unchanged(
        self, ssh_connectivity_worker, test_async_session
    ):
        """Test _check_instance_connectivity when status doesn't change."""
        instance = await InstanceDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metrics_collection_status=Status.OK,
        )
        await test_async_session.commit()
        await test_async_session.refresh(instance)
        instance_pyd_model = InstancePydModel.model_validate(instance)

        with (
            patch.object(
                ssh_connectivity_worker, "_get_instance_connectivity_report"
            ) as mock_get_report,
            patch.object(
                ssh_connectivity_worker, "_should_update_status"
            ) as mock_should_update,
            patch.object(
                ssh_connectivity_worker, "_update_instance_status"
            ) as mock_update_status,
        ):
            # Mock the connectivity report and status update decision
            connectivity_report = {
                "status": Status.OK,
                "reason": "Ready for metrics collection",
            }
            mock_get_report.return_value = connectivity_report
            mock_should_update.return_value = False

            result = await ssh_connectivity_worker._check_instance_connectivity(
                test_async_session, instance_pyd_model
            )

            assert result is False
            mock_get_report.assert_called_once()
            mock_should_update.assert_called_once()
            mock_update_status.assert_not_called()

    @pytest.mark.asyncio
    async def test_check_instance_connectivity_exception_handling(
        self, ssh_connectivity_worker, test_async_session
    ):
        """Test _check_instance_connectivity handles exceptions properly."""
        instance = await InstanceDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metrics_collection_status=Status.OK,
        )
        await test_async_session.commit()
        await test_async_session.refresh(instance)
        instance_pyd_model = InstancePydModel.model_validate(instance)

        with (
            patch.object(
                ssh_connectivity_worker, "_get_instance_connectivity_report"
            ) as mock_get_report,
            patch.object(
                ssh_connectivity_worker, "_handle_connectivity_check_error"
            ) as mock_handle_error,
        ):
            # Mock exception in connectivity report
            test_exception = Exception("Test exception")
            mock_get_report.side_effect = test_exception
            mock_handle_error.return_value = True

            result = await ssh_connectivity_worker._check_instance_connectivity(
                test_async_session, instance_pyd_model
            )

            assert result is True
            mock_get_report.assert_called_once()
            mock_handle_error.assert_called_once_with(
                test_async_session,
                instance_pyd_model,
                test_exception,
                "SSHConnectivityChecker: test-instance,",
            )

    @pytest.mark.asyncio
    async def test_handle_connectivity_check_error_new_error(
        self, ssh_connectivity_worker, test_async_session
    ):
        """Test _handle_connectivity_check_error with new error condition."""
        instance = await InstanceDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metrics_collection_status=Status.OK,
            reason="Ready for metrics collection",
        )
        await test_async_session.commit()
        await test_async_session.refresh(instance)
        instance_pyd_model = InstancePydModel.model_validate(instance)

        test_error = Exception("Network timeout")

        with patch.object(
            ssh_connectivity_worker.db_ops, "update_instance_metrics_collection_status"
        ) as mock_update:
            result = await ssh_connectivity_worker._handle_connectivity_check_error(
                test_async_session, instance_pyd_model, test_error, "test-prefix"
            )

            assert result is True
            mock_update.assert_called_once_with(
                db_session=test_async_session,
                manager_instance="test-instance",
                status=Status.ERROR,
                error_reason="Connectivity check failed: Network timeout",
                prefix_log="test-prefix",
            )

    @pytest.mark.asyncio
    async def test_handle_connectivity_check_error_same_error(
        self, ssh_connectivity_worker, test_async_session
    ):
        """Test _handle_connectivity_check_error with same error condition."""
        error_reason = "Connectivity check failed: Network timeout"
        instance = await InstanceDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metrics_collection_status=Status.ERROR,
            reason=error_reason,
        )
        await test_async_session.commit()
        await test_async_session.refresh(instance)
        instance_pyd_model = InstancePydModel.model_validate(instance)

        test_error = Exception("Network timeout")

        with patch.object(
            ssh_connectivity_worker.db_ops, "update_instance_metrics_collection_status"
        ) as mock_update:
            result = await ssh_connectivity_worker._handle_connectivity_check_error(
                test_async_session, instance_pyd_model, test_error, "test-prefix"
            )

            assert result is False
            mock_update.assert_not_called()

    @pytest.mark.asyncio
    async def test_handle_connectivity_check_error_update_fails(
        self, ssh_connectivity_worker, test_async_session
    ):
        """Test _handle_connectivity_check_error when database update fails."""
        instance = await InstanceDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metrics_collection_status=Status.OK,
        )
        await test_async_session.commit()
        await test_async_session.refresh(instance)
        instance_pyd_model = InstancePydModel.model_validate(instance)

        test_error = Exception("Network timeout")

        with patch.object(
            ssh_connectivity_worker.db_ops, "update_instance_metrics_collection_status"
        ) as mock_update:
            mock_update.side_effect = Exception("Database update failed")

            result = await ssh_connectivity_worker._handle_connectivity_check_error(
                test_async_session, instance_pyd_model, test_error, "test-prefix"
            )

            assert result is False
            mock_update.assert_called_once()

    @pytest.mark.asyncio
    async def test_complete_connectivity_check_cycle_integration(
        self, ssh_connectivity_worker, test_async_session, mock_ssh_connection
    ):
        """Integration test for complete connectivity check cycle."""
        # Create instances with different statuses
        instance1 = await InstanceDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance-1",
            metrics_collection_status=Status.ERROR,
            host_ip_address=None,
            host_secret_id=None,
            reason="Missing host ip address and host secret id from inventory or nms-orchestrator",
        )
        instance2 = await InstanceDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance-2",
            metrics_collection_status=Status.OK,
            reason="Ready for metrics collection",
        )
        await test_async_session.commit()
        await test_async_session.refresh(instance1)
        await test_async_session.refresh(instance2)

        with patch(
            "airspan_acp_metrics.workers.ssh_connectivity_worker.get_db_session"
        ) as mock_get_session:
            mock_session = AsyncMock()
            mock_get_session.return_value.__aenter__.return_value = mock_session

            mock_instance_service = AsyncMock()
            mock_instance_service.get_all_acp_instances.return_value = [instance1, instance2]

            with (
                patch(
                    "airspan_acp_metrics.workers.ssh_connectivity_worker.InstanceDbService",
                    return_value=mock_instance_service,
                ),
                patch.object(
                    ssh_connectivity_worker.instance_reconciliation,
                    "reconcile_airspan_acp_agent_instances",
                ),
                patch.object(
                    ssh_connectivity_worker.ssh_manager, "get_connection"
                ) as mock_get_connection,
                patch.object(ssh_connectivity_worker.ssh_manager, "close_connection"),
                patch.object(
                    ssh_connectivity_worker.db_ops, "update_instance_metrics_collection_status"
                ) as mock_update_status,
            ):
                # Mock SSH connections - only instance2 (OK status) should be checked
                mock_get_connection.side_effect = [
                    (None, {"reason": "Connection timeout"}),
                ]

                await ssh_connectivity_worker._check_all_instances_connectivity()

                # Verify only instance2 (with OK status) was checked for SSH connectivity
                assert mock_get_connection.call_count == 1

                # Verify database updates - only instance2 should change from OK to ERROR
                # instance1 (ERROR status) should be skipped
                assert mock_update_status.call_count == 1

                # Verify session commit was called
                mock_session.commit.assert_called_once()
