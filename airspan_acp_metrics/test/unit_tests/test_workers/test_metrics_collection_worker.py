import datetime
from unittest.mock import AsyncMock
from unittest.mock import MagicMock
from unittest.mock import patch

import pytest
from da_common.models import Status

from airspan_acp_metrics.pyd_models.instance_pyd_model import InstancePydModel
from airspan_acp_metrics.pyd_models.utils import MetricsCollectionStatus
from airspan_acp_metrics.test.factories.instance_db_factory import InstanceDbFactory
from airspan_acp_metrics.test.factories.metrics_audit_db_factory import MetricsAuditDbFactory
from airspan_acp_metrics.workers.metrics_collection_worker import MetricsCollectionWorker


class TestMetricsCollectionWorker:
    """Tests for the MetricsCollectionWorker class including both success and exception scenarios."""

    @pytest.fixture
    def metrics_collection_worker(self, test_config):
        """Create a MetricsCollectionWorker instance."""
        return MetricsCollectionWorker(test_config)

    @pytest.fixture
    def sample_metric_config(self):
        """Create a sample metric configuration."""
        return {
            "metric_name": "Stats",
            "frequency": 5,
            "enabled": True,
            "backfill_enabled": True,
            "max_days_lookback": 30,
            "max_retry_attempts": 3,
        }

    @pytest.fixture
    def mock_now(self):
        """Mock current time for consistent testing."""
        return datetime.datetime(2025, 1, 15, 12, 0, 0, tzinfo=datetime.UTC)

    @pytest.fixture
    def mock_ssh_connection(self):
        """Create a mock SSH connection."""
        connection = MagicMock()
        connection.__aenter__ = AsyncMock(return_value=connection)
        connection.__aexit__ = AsyncMock(return_value=None)
        return connection

    # INITIALIZATION TESTS
    def test_metrics_collection_worker_initialization(self, test_config):
        """Test MetricsCollectionWorker initialization with valid config."""
        worker = MetricsCollectionWorker(test_config)

        assert worker.config == test_config
        assert worker.async_session_maker is not None
        assert worker.scheduler is not None
        assert worker._is_running is False
        assert worker.logger is not None
        assert worker.metrics_collector is not None
        assert worker.ssh_manager is not None
        assert worker.db_ops is not None
        assert isinstance(worker.metrics_configs, dict)
        assert isinstance(worker.any_backfill_enabled, bool)
        assert worker.max_concurrent_backfills == 10
        assert worker.worker_name == "MetricsCollectionWorker"

    def test_metrics_collection_worker_initialization_no_metrics_config(self, test_config):
        """Test MetricsCollectionWorker initialization when no metrics are configured."""
        # Mock config with no metrics
        test_config.data["metrics"] = {}
        worker = MetricsCollectionWorker(test_config)

        assert worker.metrics_configs == {}
        assert worker.any_backfill_enabled is False

    def test_metrics_collection_worker_initialization_disabled_metrics(self, test_config):
        """Test MetricsCollectionWorker initialization when all metrics are disabled."""
        # Mock config with disabled metrics
        test_config.data["metrics"] = {
            "stats": {"enabled": False, "metric_name": "Stats"},
            "performance": {"enabled": False, "metric_name": "Performance"},
        }
        worker = MetricsCollectionWorker(test_config)

        assert worker.metrics_configs == {}
        assert worker.any_backfill_enabled is False

    # LOG PREFIX TESTS
    def test_get_log_prefix_no_params(self, metrics_collection_worker):
        """Test _get_log_prefix with no parameters."""
        result = metrics_collection_worker._get_log_prefix()
        assert result == "MetricsCollectionWorker"

    def test_get_log_prefix_with_instance_name(self, metrics_collection_worker):
        """Test _get_log_prefix with instance name."""
        result = metrics_collection_worker._get_log_prefix(instance_name="test-instance")
        assert result == "MetricsCollectionWorker test-instance"

    def test_get_log_prefix_with_metric_name(self, metrics_collection_worker):
        """Test _get_log_prefix with metric name."""
        result = metrics_collection_worker._get_log_prefix(metric_name="Stats")
        assert result == "MetricsCollectionWorker Stats"

    def test_get_log_prefix_with_intervals(self, metrics_collection_worker):
        """Test _get_log_prefix with intervals."""
        intervals = [
            (
                datetime.datetime(2025, 1, 1, 10, 0, 0, tzinfo=datetime.UTC),
                datetime.datetime(2025, 1, 1, 10, 5, 0, tzinfo=datetime.UTC),
            )
        ]
        result = metrics_collection_worker._get_log_prefix(intervals=intervals)
        assert result == "MetricsCollectionWorker ( 2025-01-01 10:00 - 10:05 )"

    def test_get_log_prefix_with_all_params(self, metrics_collection_worker):
        """Test _get_log_prefix with all parameters."""
        intervals = [
            (
                datetime.datetime(2025, 1, 1, 10, 0, 0, tzinfo=datetime.UTC),
                datetime.datetime(2025, 1, 1, 10, 5, 0, tzinfo=datetime.UTC),
            )
        ]
        result = metrics_collection_worker._get_log_prefix(
            instance_name="test-instance", metric_name="Stats", intervals=intervals
        )
        assert "MetricsCollectionWorker test-instance Stats" in result
        assert "( 2025-01-01 10:00 - 10:05 )" in result

    # START METHOD TESTS
    def test_start_success(self, metrics_collection_worker):
        """Test successful start of MetricsCollectionWorker."""
        with (
            patch.object(metrics_collection_worker.scheduler, "add_job") as mock_add_job,
            patch.object(metrics_collection_worker.scheduler, "start") as mock_start,
        ):
            metrics_collection_worker.start()

            mock_add_job.assert_called_once()
            mock_start.assert_called_once()

    # STATIC METHOD TESTS
    def test_is_metric_backfill_enabled_true(self):
        """Test _is_metric_backfill_enabled returns True when both enabled and backfill_enabled are True."""
        metric_config = {"enabled": True, "backfill_enabled": True}
        result = MetricsCollectionWorker._is_metric_backfill_enabled(metric_config)
        assert result is True

    def test_is_metric_backfill_enabled_false_not_enabled(self):
        """Test _is_metric_backfill_enabled returns False when enabled is False."""
        metric_config = {"enabled": False, "backfill_enabled": True}
        result = MetricsCollectionWorker._is_metric_backfill_enabled(metric_config)
        assert result is False

    def test_is_metric_backfill_enabled_false_backfill_disabled(self):
        """Test _is_metric_backfill_enabled returns False when backfill_enabled is False."""
        metric_config = {"enabled": True, "backfill_enabled": False}
        result = MetricsCollectionWorker._is_metric_backfill_enabled(metric_config)
        assert result is False

    def test_is_metric_backfill_enabled_false_both_disabled(self):
        """Test _is_metric_backfill_enabled returns False when both are False."""
        metric_config = {"enabled": False, "backfill_enabled": False}
        result = MetricsCollectionWorker._is_metric_backfill_enabled(metric_config)
        assert result is False

    def test_is_metric_backfill_enabled_default_values(self):
        """Test _is_metric_backfill_enabled with default values."""
        metric_config = {}
        result = MetricsCollectionWorker._is_metric_backfill_enabled(metric_config)
        assert result is False

    def test_calculate_metric_time_range_valid(self, mock_now):
        """Test _calculate_metric_time_range with valid time range."""
        # Create a mock instance that was created 10 days ago
        instance_created = mock_now - datetime.timedelta(days=10)
        mock_instance = MagicMock()
        mock_instance.created_at = instance_created

        metric_config = {"max_days_lookback": 30, "frequency": 5}

        with patch(
            "airspan_acp_metrics.workers.metrics_collection_worker.get_most_recent_metrics_time_interval"
        ) as mock_get_time:
            mock_get_time.return_value = (None, mock_now - datetime.timedelta(minutes=5))

            result = MetricsCollectionWorker._calculate_metric_time_range(
                mock_instance, metric_config, mock_now
            )

            assert result is not None
            oldest_allowed, newest_allowed = result
            assert oldest_allowed == instance_created
            assert newest_allowed == mock_now - datetime.timedelta(minutes=5)

    def test_calculate_metric_time_range_instance_too_recent(self, mock_now):
        """Test _calculate_metric_time_range when instance is newer than lookback time."""
        # Create a mock instance that was created 5 days ago
        instance_created = mock_now - datetime.timedelta(days=5)
        mock_instance = MagicMock()
        mock_instance.created_at = instance_created

        metric_config = {"max_days_lookback": 30, "frequency": 5}

        with patch(
            "airspan_acp_metrics.workers.metrics_collection_worker.get_most_recent_metrics_time_interval"
        ) as mock_get_time:
            mock_get_time.return_value = (None, mock_now - datetime.timedelta(minutes=5))

            result = MetricsCollectionWorker._calculate_metric_time_range(
                mock_instance, metric_config, mock_now
            )

            assert result is not None
            oldest_allowed, newest_allowed = result
            # Should use instance creation time since it's newer than lookback
            assert oldest_allowed == instance_created
            assert newest_allowed == mock_now - datetime.timedelta(minutes=5)

    def test_calculate_metric_time_range_instance_older_than_lookback(self, mock_now):
        """Test _calculate_metric_time_range when instance is older than lookback time."""
        # Create a mock instance that was created 60 days ago
        instance_created = mock_now - datetime.timedelta(days=60)
        mock_instance = MagicMock()
        mock_instance.created_at = instance_created

        metric_config = {"max_days_lookback": 30, "frequency": 5}

        with patch(
            "airspan_acp_metrics.workers.metrics_collection_worker.get_most_recent_metrics_time_interval"
        ) as mock_get_time:
            mock_get_time.return_value = (None, mock_now - datetime.timedelta(minutes=5))

            result = MetricsCollectionWorker._calculate_metric_time_range(
                mock_instance, metric_config, mock_now
            )

            assert result is not None
            oldest_allowed, newest_allowed = result
            # Should use lookback time since instance is older
            expected_oldest = mock_now - datetime.timedelta(days=30)
            assert oldest_allowed == expected_oldest
            assert newest_allowed == mock_now - datetime.timedelta(minutes=5)

    def test_calculate_metric_time_range_invalid_range(self, mock_now):
        """Test _calculate_metric_time_range when time range is invalid."""
        # Create a mock instance that was created in the future
        instance_created = mock_now + datetime.timedelta(days=1)
        mock_instance = MagicMock()
        mock_instance.created_at = instance_created

        metric_config = {"max_days_lookback": 30, "frequency": 5}

        with patch(
            "airspan_acp_metrics.workers.metrics_collection_worker.get_most_recent_metrics_time_interval"
        ) as mock_get_time:
            mock_get_time.return_value = (None, mock_now - datetime.timedelta(minutes=5))

            result = MetricsCollectionWorker._calculate_metric_time_range(
                mock_instance, metric_config, mock_now
            )

            assert result is None

    def test_attempts_exhausted_true(self):
        """Test attempts_exhausted returns True when max attempts exceeded."""
        mock_audit = MagicMock()
        mock_audit.attempt_count = 5
        max_retry_attempts = 3

        result = MetricsCollectionWorker.attempts_exhausted(mock_audit, max_retry_attempts)
        assert result is True

    def test_attempts_exhausted_false(self):
        """Test attempts_exhausted returns False when max attempts not exceeded."""
        mock_audit = MagicMock()
        mock_audit.attempt_count = 2
        max_retry_attempts = 3

        result = MetricsCollectionWorker.attempts_exhausted(mock_audit, max_retry_attempts)
        assert result is False

    def test_attempts_exhausted_equal(self):
        """Test attempts_exhausted returns True when attempts is greater than max."""
        mock_audit = MagicMock()
        mock_audit.attempt_count = 4
        max_retry_attempts = 3

        result = MetricsCollectionWorker.attempts_exhausted(mock_audit, max_retry_attempts)
        assert result is True

    # ASYNC METHODS TESTS
    @pytest.mark.asyncio
    async def test_execute_metrics_collection_cycle_already_running(
        self, metrics_collection_worker
    ):
        """Test _execute_metrics_collection_cycle when worker is already running."""
        metrics_collection_worker._is_running = True
        metrics_collection_worker.any_backfill_enabled = True

        # Should return early without doing anything
        await metrics_collection_worker._execute_metrics_collection_cycle()
        # Test passes if no exception is raised

    @pytest.mark.asyncio
    async def test_execute_metrics_collection_cycle_no_backfill_enabled(
        self, metrics_collection_worker
    ):
        """Test _execute_metrics_collection_cycle when no backfill is enabled."""
        metrics_collection_worker._is_running = False
        metrics_collection_worker.any_backfill_enabled = False

        # Should return early without doing anything
        await metrics_collection_worker._execute_metrics_collection_cycle()
        # Test passes if no exception is raised

    @pytest.mark.asyncio
    async def test_execute_metrics_collection_cycle_no_instances(
        self, metrics_collection_worker
    ):
        """Test _execute_metrics_collection_cycle when no instances are found."""
        metrics_collection_worker._is_running = False
        metrics_collection_worker.any_backfill_enabled = True
        metrics_collection_worker.metrics_configs = {
            "stats": {"enabled": True, "backfill_enabled": True, "metric_name": "Stats"}
        }

        with patch(
            "airspan_acp_metrics.workers.metrics_collection_worker.get_db_session"
        ) as mock_get_session:
            mock_session = AsyncMock()
            mock_get_session.return_value.__aenter__.return_value = mock_session

            mock_instance_service = AsyncMock()
            mock_instance_service.get_all_acp_instances.return_value = []

            with patch(
                "airspan_acp_metrics.workers.metrics_collection_worker.InstanceDbService",
                return_value=mock_instance_service,
            ):
                await metrics_collection_worker._execute_metrics_collection_cycle()

                mock_instance_service.get_all_acp_instances.assert_called_once_with(
                    metrics_collection_status=Status.OK
                )

    @pytest.mark.asyncio
    async def test_execute_metrics_collection_cycle_for_specific_metrics_type_no_instances(
        self, metrics_collection_worker, test_async_session
    ):
        """Test _execute_metrics_collection_cycle_for_specific_metrics_type with no instances."""
        metric_config = {"metric_name": "Stats", "backfill_enabled": True, "enabled": True}

        result = await metrics_collection_worker._collect_metrics_for_metric_type(
            db_session=test_async_session,
            metric_name="Stats",
            metric_config=metric_config,
            ssh_enabled_instances=[],
        )

        assert result == 0

    @pytest.mark.asyncio
    async def test_execute_metrics_collection_cycle_for_specific_metrics_type_ssh_failure(
        self, metrics_collection_worker, test_async_session
    ):
        """Test _execute_metrics_collection_cycle_for_specific_metrics_type with SSH connection failure."""
        # Create a test instance
        instance = await InstanceDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metrics_collection_status=Status.OK,
        )
        await test_async_session.commit()
        await test_async_session.refresh(instance)

        metric_config = {"metric_name": "Stats", "backfill_enabled": True, "enabled": True}

        # Mock SSH connection failure
        with patch.object(
            metrics_collection_worker.ssh_manager, "get_connection"
        ) as mock_get_connection:
            mock_get_connection.return_value = (None, {"reason": "Connection failed"})

            result = await metrics_collection_worker._collect_metrics_for_metric_type(
                db_session=test_async_session,
                metric_name="Stats",
                metric_config=metric_config,
                ssh_enabled_instances=[instance],
            )

            assert result == 0

    @pytest.mark.asyncio
    async def test_update_metrics_collection_status_to_running(
        self, metrics_collection_worker, test_async_session, mock_now
    ):
        """Test update_metrics_collection_status_to_running updates audit statuses."""
        # Create test audit records
        audit1 = await MetricsAuditDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metric_name="Stats",
            interval_start=mock_now - datetime.timedelta(minutes=10),
            interval_end=mock_now - datetime.timedelta(minutes=5),
            collection_status=MetricsCollectionStatus.NOT_STARTED,
        )

        audit2 = await MetricsAuditDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metric_name="Stats",
            interval_start=mock_now - datetime.timedelta(minutes=5),
            interval_end=mock_now,
            collection_status=MetricsCollectionStatus.NOT_STARTED,
        )

        await test_async_session.commit()
        await test_async_session.refresh(audit1)
        await test_async_session.refresh(audit2)

        pending_audits = [audit1, audit2]
        metric_config = {"metric_name": "Stats"}

        with patch("datetime.datetime") as mock_datetime:
            mock_datetime.now.return_value = mock_now
            mock_datetime.timezone = datetime.timezone

            result = await metrics_collection_worker._preprocess_audits(
                db_session=test_async_session,
                instance_name="test-instance",
                pending_audits=pending_audits,
                metric_config=metric_config,
            )

            # Verify status was updated for the first audit (older than 90 seconds)
            await test_async_session.refresh(audit1)
            assert audit1.collection_status == MetricsCollectionStatus.RUNNING
            assert audit1.last_attempt is not None

            # The second audit should be skipped (too recent)
            await test_async_session.refresh(audit2)
            # Status might remain unchanged if it's too recent
            assert len(result) <= 2

    @pytest.mark.asyncio
    async def test_process_instance_metrics_collection_no_pending_audits(
        self, metrics_collection_worker, test_async_session, mock_ssh_connection
    ):
        """Test _process_instance_metrics_collection when no pending audits exist."""
        # Create a test instance
        instance = await InstanceDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metrics_collection_status=Status.OK,
        )
        await test_async_session.commit()
        await test_async_session.refresh(instance)
        metric_config = {"metric_name": "Stats"}

        result = await metrics_collection_worker._collect_metrics_for_instance(
            db_session=test_async_session,
            metric_config=metric_config,
            instance_db_model=instance,
            connection=mock_ssh_connection,
        )

        assert result == 0

    @pytest.mark.asyncio
    async def test_process_instance_metrics_collection_with_audits(
        self, metrics_collection_worker, test_async_session, mock_ssh_connection, mock_now
    ):
        """Test _process_instance_metrics_collection processes audits successfully."""
        # Create a test instance
        instance = await InstanceDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metrics_collection_status=Status.OK,
        )

        # Create test audit records
        audit1 = await MetricsAuditDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metric_name="Stats",
            interval_start=mock_now - datetime.timedelta(minutes=10),
            interval_end=mock_now - datetime.timedelta(minutes=5),
            collection_status=MetricsCollectionStatus.NOT_STARTED,
            attempt_count=1,
        )

        await test_async_session.commit()
        await test_async_session.refresh(audit1)
        await test_async_session.refresh(instance)
        metric_config = {"metric_name": "Stats", "max_retry_attempts": 3}

        with patch.object(
            metrics_collection_worker.metrics_collector, "collect_instance_metrics_task"
        ) as mock_collect_task:
            mock_collect_task.return_value = AsyncMock()

            with patch("datetime.datetime") as mock_datetime:
                mock_datetime.now.return_value = mock_now
                mock_datetime.timezone = datetime.timezone
                mock_datetime.timedelta = datetime.timedelta

                result = await metrics_collection_worker._collect_metrics_for_instance(
                    db_session=test_async_session,
                    metric_config=metric_config,
                    instance_db_model=instance,
                    connection=mock_ssh_connection,
                )

                assert result == 1
                mock_collect_task.assert_called_once()

    @pytest.mark.asyncio
    async def test_process_instance_metrics_collection_exhausted_attempts(
        self, metrics_collection_worker, test_async_session, mock_ssh_connection, mock_now
    ):
        """Test _process_instance_metrics_collection skips audits with exhausted attempts."""
        # Create a test instance
        instance = await InstanceDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metrics_collection_status=Status.OK,
        )

        # Create test audit record with exhausted attempts
        audit1 = await MetricsAuditDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metric_name="Stats",
            interval_start=mock_now - datetime.timedelta(minutes=10),
            interval_end=mock_now - datetime.timedelta(minutes=5),
            collection_status=MetricsCollectionStatus.NOT_STARTED,
            attempt_count=4,  # reached max attempts
        )

        await test_async_session.commit()
        await test_async_session.refresh(audit1)
        await test_async_session.refresh(instance)
        metric_config = {"metric_name": "Stats", "max_retry_attempts": 3}

        with patch("datetime.datetime") as mock_datetime:
            mock_datetime.now.return_value = mock_now
            mock_datetime.timezone = datetime.timezone
            mock_datetime.timedelta = datetime.timedelta

            instance_pyd_model = InstancePydModel.model_validate(instance)
            result = await metrics_collection_worker._preprocess_audits(
                db_session=test_async_session,
                instance_name=instance_pyd_model.manager_instance,
                metric_config=metric_config,
                pending_audits=[audit1],
            )

            # Should skip the audit due to exhausted attempts
            assert result == []

    @pytest.mark.asyncio
    async def test_process_instance_metrics_collection_empty_audit_list(
        self, metrics_collection_worker, test_async_session, mock_ssh_connection, mock_now
    ):
        """Test _process_instance_metrics_collection handles exceptions gracefully."""
        # Create a test instance
        instance = await InstanceDbFactory(
            async_session=test_async_session,
            manager_instance="test-instance",
            metrics_collection_status=Status.OK,
        )

        await test_async_session.commit()
        await test_async_session.refresh(instance)

        metric_config = {"metric_name": "Stats", "max_retry_attempts": 3}

        result = await metrics_collection_worker._collect_metrics_for_instance(
            db_session=test_async_session,
            metric_config=metric_config,
            instance_db_model=instance,
            connection=mock_ssh_connection,
        )

        # Should return 0 due to exception
        assert result == 0

    # SHUTDOWN TESTS
    def test_shutdown_with_running_scheduler(self, metrics_collection_worker):
        """Test shutdown method when scheduler is running."""
        mock_scheduler = MagicMock()
        mock_scheduler.running = True
        metrics_collection_worker.scheduler = mock_scheduler
        metrics_collection_worker._is_running = True

        metrics_collection_worker.shutdown()

        mock_scheduler.shutdown.assert_called_once()
        assert metrics_collection_worker._is_running is False

    def test_shutdown_with_stopped_scheduler(self, metrics_collection_worker):
        """Test shutdown method when scheduler is not running."""
        mock_scheduler = MagicMock()
        mock_scheduler.running = False
        metrics_collection_worker.scheduler = mock_scheduler
        metrics_collection_worker._is_running = True

        metrics_collection_worker.shutdown()

        mock_scheduler.shutdown.assert_not_called()
        assert metrics_collection_worker._is_running is True

    def test_shutdown_no_scheduler(self, metrics_collection_worker):
        """Test shutdown method when scheduler doesn't exist."""
        if hasattr(metrics_collection_worker, "scheduler"):
            delattr(metrics_collection_worker, "scheduler")
        metrics_collection_worker._is_running = True

        # Should not raise exception
        metrics_collection_worker.shutdown()
        assert metrics_collection_worker._is_running is True
