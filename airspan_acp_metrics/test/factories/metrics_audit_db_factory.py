import datetime
import uuid

import factory

from airspan_acp_metrics.database.models import MetricsAuditDbModel
from airspan_acp_metrics.pyd_models.utils import MetricsCollectionStatus
from airspan_acp_metrics.test.factories.base import BaseFactory
from airspan_acp_metrics.test.factories.metrics_db_factory import MetricsDbFactory
from airspan_acp_metrics.utils import get_most_recent_metrics_time_interval


current_time = datetime.datetime.now(datetime.UTC)
interval_start, interval_end = get_most_recent_metrics_time_interval(current_time)


# fmt: off
# flake8: noqa

class MetricsAuditDbFactory(BaseFactory):
    class Meta:
        model = MetricsAuditDbModel
        sqlalchemy_session_persistence = "commit"


    id                 = factory.LazyFunction(lambda: uuid.uuid4())
    manager_instance   = "test-instance"
    metric_name        = "test-metric"
    file_metadata      = {"file_size_bytes": 1024, "file_count": 1}
    interval_start     = interval_start
    interval_end       = interval_end
    collection_status = MetricsCollectionStatus.COLLECTED
    last_attempt       = factory.LazyFunction(lambda: datetime.datetime.now(datetime.UTC))
    attempt_count      = 1
    reason             = ""
    created_at         = factory.LazyFunction(lambda: datetime.datetime.now(datetime.UTC))
    updated_at         = factory.LazyFunction(lambda: datetime.datetime.now(datetime.UTC))

    @classmethod
    async def _create(cls, model_class, async_session, *args, **kwargs):
        """Override _create to auto-create metrics if not provided."""
        # If metrics_id is not provided and collection_status is COLLECTED, create a metrics record
        if (
            kwargs.get("metrics_id") is None
            and kwargs.get("collection_status") == MetricsCollectionStatus.COLLECTED
        ):
            # Create a metrics record first
            metrics_kwargs = {
                "manager_instance": kwargs.get("manager_instance", cls.manager_instance),
                "metric_name": "test-metric",  # Default metric name
                "gcs_file_path": f"gs://test-bucket/{kwargs.get('manager_instance', cls.manager_instance)}/metrics.json",
                "interval_start": kwargs.get("interval_start", cls.interval_start),
                "interval_end": kwargs.get("interval_end", cls.interval_end),
            }

            # Create metrics record first
            metrics_record = await MetricsDbFactory._create(
                MetricsDbFactory._meta.model,
                async_session=async_session,
                **metrics_kwargs
            )
            kwargs["metrics_id"] = metrics_record.id

        return await super()._create(model_class, async_session, *args, **kwargs)
