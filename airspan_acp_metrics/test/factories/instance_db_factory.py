import datetime

import factory
import pytest
from da_common.models import Status

from airspan_acp_metrics.database.models import InstanceDbModel
from airspan_acp_metrics.test.factories.base import BaseFactory

# flake8: noqa
# fmt: off

@pytest.mark.asyncio
class InstanceDbFactory(BaseFactory):
    class Meta:
        model = InstanceDbModel
        sqlalchemy_session_persistence = "commit"

    manager_instance          = "test-acp-instance"
    url                       = "http://test_acp_instance_host"
    host_ip_address           = "127.0.0.1"
    host_secret_id            = "test-acp-instance"
    metrics_collection_status = Status.OK
    reason                    = ""

    created_at                = factory.LazyFunction(lambda: datetime.datetime.now(datetime.UTC) - datetime.timedelta(hours=1))
    updated_at                = factory.LazyFunction(lambda: datetime.datetime.now(datetime.UTC) - datetime.timedelta(hours=1))
