import pytest
from metrics_collector.api_schema.models import Severity as AlarmSeverityEnum

from airspan_acp_metrics.database.models.alarm_type_db_model import AlarmTypeDBModel
from airspan_acp_metrics.pyd_models.alarm_enums import AlarmPriority
from airspan_acp_metrics.pyd_models.alarm_enums import AlarmType
from airspan_acp_metrics.test.factories.base import BaseFactory


@pytest.mark.asyncio
class AlarmTypeDbFactory(BaseFactory):
    class Meta:
        model = AlarmTypeDBModel
        sqlalchemy_session_persistence = "commit"

    type = AlarmType.SSH_CONNECTIVITY_FAILED
    severity = AlarmSeverityEnum.critical
    priority = AlarmPriority.high
    repairs = ["Check SSH connectivity", "Verify credentials", "Check network connectivity"]
