import factory as factory_boy
import pytest
from sqlalchemy.ext.asyncio import AsyncSession


@pytest.mark.asyncio
class BaseFactory(factory_boy.alchemy.SQLAlchemyModelFactory):
    @classmethod
    async def _create(cls, model_class, async_session: AsyncSession, *args, **kwargs):
        instance = model_class(*args, **kwargs)
        async_session.add(instance)
        try:
            await async_session.commit()
            await async_session.refresh(instance)
            return instance
        except Exception as e:
            await async_session.rollback()
            raise e
