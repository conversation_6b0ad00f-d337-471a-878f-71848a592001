import datetime
import uuid

import factory
import pytest

from airspan_acp_metrics.database.models import MetricsDbModel
from airspan_acp_metrics.test.factories.base import BaseFactory
from airspan_acp_metrics.utils import get_most_recent_metrics_time_interval


current_time = datetime.datetime.now(datetime.UTC)
interval_start, interval_end = get_most_recent_metrics_time_interval(current_time)


# fmt: off
# flake8: noqa

@pytest.mark.asyncio
class MetricsDbFactory(BaseFactory):
    class Meta:
        model = MetricsDbModel
        sqlalchemy_session_persistence = "commit"

    id     = factory.LazyFunction(lambda: uuid.uuid4())
    manager_instance     = "test-instance"
    metric_name          = "test-metric"
    gcs_file_path        = "test-gcs-file-path"
    interval_start  = interval_start
    interval_end    = interval_end
    created_at = datetime.datetime.now(datetime.UTC)
    updated_at = datetime.datetime.now(datetime.UTC)
