import datetime
import uuid

import factory
import pytest
from metrics_collector.api_schema.models import Severity as AlarmSeverityEnum

from airspan_acp_metrics.database.models.alarm_db_model import AlarmDBModel
from airspan_acp_metrics.pyd_models.alarm_enums import AlarmPriority
from airspan_acp_metrics.pyd_models.alarm_enums import AlarmStatus
from airspan_acp_metrics.pyd_models.alarm_enums import AlarmType
from airspan_acp_metrics.test.factories.base import BaseFactory


@pytest.mark.asyncio
class AlarmDbFactory(BaseFactory):
    class Meta:
        model = AlarmDBModel
        sqlalchemy_session_persistence = "commit"

    id = factory.LazyFunction(lambda: uuid.uuid4())
    type = AlarmType.SSH_CONNECTIVITY_FAILED
    name = "SSH connectivity failed for test-instance"
    severity = AlarmSeverityEnum.major
    priority = AlarmPriority.high
    source = "test"
    created_at = datetime.datetime.now(datetime.UTC)
    updated_at = None
    resolved_at = None
    resolver = None
    acknowledged = None
    acknowledger = None
    status = AlarmStatus.new
    description = "Test alarm description"
    repairs = ["Check SSH connectivity", "Verify credentials"]
    manager_instance = "test-instance"
