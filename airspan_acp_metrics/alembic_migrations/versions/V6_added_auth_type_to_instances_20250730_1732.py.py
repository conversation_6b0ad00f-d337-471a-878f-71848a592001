"""added auth_type to instances

Revision ID: 1414adf56197
Revises: 9a7b8c9d0e1f
Create Date: 2025-07-30 17:32:32.267129

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op


# revision identifiers, used by Alembic.
revision: str = "1414adf56197"
down_revision: str | None = "9a7b8c9d0e1f"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "instances",
        sa.Column("auth_type", sa.String(), nullable=True, server_default="password"),
    )

    # Update all existing records with default value "password"
    op.execute("UPDATE instances SET auth_type = 'password' WHERE auth_type IS NULL")

    # Make the column non-nullable
    op.alter_column("instances", "auth_type", nullable=False)

    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###

    op.drop_column("instances", "auth_type")

    # ### end Alembic commands ###
