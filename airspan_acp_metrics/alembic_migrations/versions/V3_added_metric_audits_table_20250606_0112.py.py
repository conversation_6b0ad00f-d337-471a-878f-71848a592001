"""added metric_audits table

Revision ID: 8f8e76862a01
Revises: d651bb86b365
Create Date: 2025-06-06 01:12:21.286590

"""

import uuid
from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op


# revision identifiers, used by Alembic.
revision: str = "8f8e76862a01"
down_revision: str | None = "d651bb86b365"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "metric_audits",
        sa.Column("id", sa.UUID(), nullable=False, primary_key=True, default=uuid.uuid4),
        sa.Column("manager_instance", sa.String(), nullable=False),
        sa.Column("metric_name", sa.String(), nullable=False),
        sa.Column("interval_start", sa.DateTime(timezone=True), nullable=False),
        sa.Column("interval_end", sa.DateTime(timezone=True), nullable=False),
        sa.Column("file_metadata", sa.JSON(), nullable=True),
        sa.Column("collection_status", sa.String(), nullable=False),
        sa.Column(
            "last_attempt",
            sa.DateTime(timezone=True),
            nullable=True,
        ),
        sa.Column("attempt_count", sa.Integer(), nullable=False),
        sa.Column("reason", sa.String(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("metrics_id", sa.UUID(), nullable=True),
        sa.ForeignKeyConstraint(
            ["metrics_id"],
            ["metrics.id"],
        ),
    )
    op.create_index(
        op.f("ix_metric_audits_manager_instance"),
        "metric_audits",
        ["manager_instance"],
        unique=False,
    )
    op.create_index(
        op.f("ix_metric_audits_interval_end"), "metric_audits", ["interval_end"], unique=False
    )
    op.create_index(
        op.f("ix_metric_audits_interval_start"),
        "metric_audits",
        ["interval_start"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###

    op.drop_index(op.f("ix_metric_audits_interval_start"), table_name="metric_audits")
    op.drop_index(op.f("ix_metric_audits_interval_end"), table_name="metric_audits")
    op.drop_index(op.f("ix_metric_audits_manager_instance"), table_name="metric_audits")
    op.drop_table("metric_audits")
    # ### end Alembic commands ###
