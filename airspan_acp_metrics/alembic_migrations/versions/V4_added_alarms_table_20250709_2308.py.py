"""added alarms table

Revision ID: 3b54fd2004bf
Revises: 8f8e76862a01
Create Date: 2025-07-09 23:08:31.622757

"""

import json
from collections.abc import Sequence
from uuid import uuid4

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision: str = "3b54fd2004bf"
down_revision: str | None = "8f8e76862a01"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade schema."""
    # Create alarm_type table
    op.create_table(
        "alarm_type",
        sa.Column("type", sa.String(), nullable=False),
        sa.Column("severity", sa.String(), nullable=False),
        sa.Column("priority", sa.String(), nullable=False),
        sa.Column("repairs", sa.JSON(), nullable=True),
        sa.PrimaryKeyConstraint("type"),
    )

    # Create alarm table
    op.create_table(
        "alarm",
        sa.Column(
            "id", postgresql.UUID(as_uuid=True), primary_key=True, nullable=False, default=uuid4
        ),
        sa.Column("type", sa.String(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("severity", sa.String(), nullable=False),
        sa.Column("priority", sa.String(), nullable=False),
        sa.Column("source", sa.String(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("resolved_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("resolver", sa.String(), nullable=True),
        sa.Column("acknowledged", sa.DateTime(timezone=True), nullable=True),
        sa.Column("acknowledger", sa.String(), nullable=True),
        sa.Column("status", sa.String(), nullable=False),
        sa.Column("description", sa.String(), nullable=True),
        sa.Column("repairs", sa.JSON(), nullable=True),
        sa.Column("manager_instance", sa.String(), nullable=False),
    )

    # Prepopulate alarm types
    alarm_types = [
        {
            "type": "ssh_connectivity_failed",
            "severity": "critical",
            "priority": "high",
            "repairs": [
                "Check SSH connectivity",
                "Verify credentials",
                "Check network connectivity",
            ],
        },
        {
            "type": "airspan_acp_agent_unreachable",
            "severity": "critical",
            "priority": "high",
            "repairs": [
                "Check Airspan ACP Agent service",
                "Verify service is running",
                "Check network connectivity",
            ],
        },
        {
            "type": "inventory_manager_unreachable",
            "severity": "critical",
            "priority": "high",
            "repairs": [
                "Check Inventory Manager service",
                "Verify service is running",
                "Check network connectivity",
            ],
        },
        {
            "type": "nms_orchestrator_unreachable",
            "severity": "critical",
            "priority": "high",
            "repairs": [
                "Check NMS Orchestrator service",
                "Verify service is running",
                "Check network connectivity",
            ],
        },
        {
            "type": "inventory_data_missing",
            "severity": "major",
            "priority": "medium",
            "repairs": [
                "Check inventory data",
                "Verify data synchronization",
                "Check data integrity",
            ],
        },
        {
            "type": "orchestrator_data_missing",
            "severity": "major",
            "priority": "medium",
            "repairs": [
                "Check orchestrator data",
                "Verify data synchronization",
                "Check data integrity",
            ],
        },
        {
            "type": "airspan_instance_unreachable",
            "severity": "critical",
            "priority": "high",
            "repairs": [
                "Check Airspan instance",
                "Verify instance is running",
                "Check network connectivity",
            ],
        },
    ]

    # Insert alarm types
    connection = op.get_bind()
    for alarm_type in alarm_types:
        connection.execute(
            sa.text(
                """
                INSERT INTO alarm_type (type, severity, priority, repairs)
                VALUES (:type, :severity, :priority, :repairs)
                """
            ),
            parameters={
                "type": alarm_type["type"],
                "severity": alarm_type["severity"],
                "priority": alarm_type["priority"],
                "repairs": json.dumps(alarm_type["repairs"]),
            },
        )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("alarm")
    op.drop_table("alarm_type")
