"""added metrics table

Revision ID: d651bb86b365
Revises: 872d0a282ee7
Create Date: 2025-06-06 00:51:48.636039

"""

import uuid
from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op


# revision identifiers, used by Alembic.
revision: str = "d651bb86b365"
down_revision: str | None = "872d0a282ee7"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "metrics",
        sa.Column("id", sa.UUID(), nullable=False, default=uuid.uuid4, primary_key=True),
        sa.Column("manager_instance", sa.String(), nullable=False),
        sa.Column("metric_name", sa.String(), nullable=False),
        sa.Column("gcs_file_path", sa.String(), nullable=False),
        sa.Column("interval_start", sa.DateTime(timezone=True), nullable=False),
        sa.Column("interval_end", sa.DateTime(timezone=True), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.UniqueConstraint("manager_instance", "interval_start", "interval_end"),
    )
    op.create_index(op.f("ix_metrics_interval_end"), "metrics", ["interval_end"], unique=False)
    op.create_index(
        op.f("ix_metrics_manager_instance"), "metrics", ["manager_instance"], unique=False
    )
    op.create_index(
        op.f("ix_metrics_interval_start"), "metrics", ["interval_start"], unique=False
    )

    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_metrics_interval_start"), table_name="metrics")
    op.drop_index(op.f("ix_metrics_manager_instance"), table_name="metrics")
    op.drop_index(op.f("ix_metrics_interval_end"), table_name="metrics")
    op.drop_table("metrics")
    # ### end Alembic commands ###
