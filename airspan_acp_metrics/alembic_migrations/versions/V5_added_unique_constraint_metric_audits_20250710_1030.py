"""added unique constraint to metric_audits table

Revision ID: 9a7b8c9d0e1f
Revises: 8f8e76862a01
Create Date: 2025-07-10 10:30:00.000000

"""

from collections.abc import Sequence

from alembic import op


# revision identifiers, used by Alembic.
revision: str = "9a7b8c9d0e1f"
down_revision: str | None = "3b54fd2004bf"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint(
        "uix_metrics_audit",
        "metric_audits",
        ["manager_instance", "interval_start", "interval_end", "attempt_count"],
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        "uix_metrics_audit",
        "metric_audits",
        type_="unique",
    )
    # ### end Alembic commands ###
