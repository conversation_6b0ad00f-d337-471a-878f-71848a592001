"""added acp_instances table

Revision ID: 872d0a282ee7
Revises:
Create Date: 2025-06-05 13:33:03.197793

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op


# revision identifiers, used by Alembic.
revision: str = "872d0a282ee7"
down_revision: str | None = None
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "instances",
        sa.Column(
            "manager_instance", sa.String(), nullable=False, unique=True, primary_key=True
        ),
        sa.Column("url", sa.String(), nullable=False),
        sa.Column("host_ip_address", sa.String(), nullable=True),
        sa.Column("host_secret_id", sa.String(), nullable=True),
        sa.Column("metrics_collection_status", sa.String(), nullable=False),
        sa.Column("reason", sa.String(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("instances")
    # ### end Alembic commands ###
