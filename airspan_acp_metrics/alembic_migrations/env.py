import asyncio
import importlib
import importlib.resources
import logging
from logging.config import fileConfig
from pathlib import Path

from alembic import context
from da_common.config import Config
from sqlalchemy import text
from sqlalchemy.engine import Connection
from sqlalchemy.ext.asyncio import create_async_engine

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
from airspan_acp_metrics.database.connection import Base
from airspan_acp_metrics.database.connection import get_db_url
from airspan_acp_metrics.database.models import *  # noqa


config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
# from myapp import mymodel
# target_metadata = mymodel.Base.metadata
target_metadata = Base.metadata


# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def do_run_migrations(connection: Connection) -> None:
    context.configure(
        connection=connection, target_metadata=target_metadata, version_table_schema="metrics"
    )

    with context.begin_transaction():
        context.run_migrations()


async def validate_schema_state(connection) -> None:
    # Validate alembic_migration table exist with clean empty state
    logger = logging.getLogger(
        "airspan_acp_metrics.alembic_migrations.env.validate_schema_state"
    )
    result = await connection.execute(text("SELECT * FROM alembic_version"))
    rows = result.fetchall()
    if rows:
        logger.info(f"Migration: alembic_version table not empty, rows: {rows}")
        # delete the rows
        await connection.execute(text("DELETE FROM alembic_version"))
    logger.info("Migration: alembic_version table is empty.")


async def run_async_migrations() -> None:
    """In this scenario we need to create an Engine
    and associate a connection with the context.
    """

    logger = logging.getLogger("airspan_acp_metrics.alembic_migrations.env")
    config_file = importlib.resources.files("airspan_acp_metrics.cfg").joinpath("starter.toml")
    config_file = Path(str(config_file))
    config = await Config.get_config(config_file)

    async_db_url = get_db_url(config)
    # Don't set search_path in connect_args initially
    async_engine = create_async_engine(async_db_url)

    # First, create the schema in a separate transaction
    async with async_engine.begin() as connection:
        await connection.execute(text("CREATE SCHEMA IF NOT EXISTS metrics"))
        logger.info("Migration: Schema 'metrics' created successfully.")

    # Now run migrations with the schema in place
    async with async_engine.begin() as connection:
        await connection.execute(text("SET search_path TO metrics"))
        logger.info("Migration: Search path set to 'metrics'.")

        # Validate schema state before migrations
        if config.data["db"]["database"] == "test_airspan_acp_metrics":
            await validate_schema_state(connection)
        await connection.run_sync(do_run_migrations)

    await async_engine.dispose()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode."""
    # Since command.upgrade is now run in an executor thread from perform_db_migration,
    # this env.py context is effectively "new" from an asyncio perspective within that thread.
    # We can directly use asyncio.run() here for the async part of migrations.
    asyncio.run(run_async_migrations())


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
