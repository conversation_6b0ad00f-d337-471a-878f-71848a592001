import logging
from datetime import datetime
from uuid import uuid4

from dal_pubsub.pubsub import PubSub
from metrics_collector.api_schema.models import EventData
from metrics_collector.api_schema.models import EventHeader
from metrics_collector.api_schema.models import MetricsEvent

from airspan_acp_metrics.pyd_models.instance_pyd_model import InstancePydModel


def generate_metrics_payload(
    instance_pyd_model: InstancePydModel,
    interval_start: datetime,
    interval_end: datetime,
    gcs_file_path: str,
    file_metadata: dict,
) -> dict:
    event_header = EventHeader(
        domain="nms",
        eventId=uuid4().hex,
        eventName=instance_pyd_model.manager_instance,
        eventType=instance_pyd_model.manager_instance,
        priority="low",
        reportingEntityName="server-crawler",
        sourceName=instance_pyd_model.manager_instance,
        sourceId=instance_pyd_model.manager_instance,
        eventTime=interval_start,
        eventDuration=(interval_end - interval_start).seconds,
        systemDN="airspan-acp-metrics",
    )
    event_data = EventData(
        objectId=instance_pyd_model.manager_instance,
        objectType="ACP",
        streetCellId="",
        uri=instance_pyd_model.host_ip_address,
        type=f"{instance_pyd_model.manager_instance}-metrics",
        cause="",
        perceivedSeverity="none",
        specificProblem="",
        trendIndication="",
        monitoredAttributes=[{"gcs_file_path": gcs_file_path, **file_metadata}],
        proposedRepairActions=[],
        additionalText="",
        additionalInformation="",
    )
    event = MetricsEvent(
        header=event_header,
        data=event_data,
    )
    return event.model_dump(mode="json")


def publish_metrics(
    *,
    pub_sub: PubSub,
    topic: str,
    instance_pyd_model: InstancePydModel,
    interval_start: datetime,
    interval_end: datetime,
    gcs_file_path: str,
    file_metadata: dict,
    prefix_log: str,
) -> None:
    logger = logging.getLogger("airspan_acp_metrics.publishing.publish_metrics")
    pub_sub.set_topic(topic)
    metrics = generate_metrics_payload(
        instance_pyd_model,
        interval_start,
        interval_end,
        gcs_file_path,
        file_metadata,
    )
    pub_sub.push_payload(metrics)
    logger.info(f"{prefix_log}, Metrics published successfully")
