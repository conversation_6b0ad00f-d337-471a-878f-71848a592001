import datetime


DATETIME_FORMAT = "%Y-%m-%d %H:%M:%S"


def get_most_recent_metrics_time_interval(current_time, interval_minutes=5):
    """
    Get the current 5-minute interval and the previous one.

    Args:
        current_time: datetime object
        interval_minutes: Size of the interval in minutes (default: 5)

    Returns:
        tuple of (interval_start, interval_end) as datetime objects
    """

    # Calculate minutes since the hour
    minutes = current_time.minute

    # Calculate which interval we're in
    current_interval = minutes // interval_minutes

    # Calculate the end time (current interval)
    end_minute = current_interval * interval_minutes
    end_time = current_time.replace(minute=end_minute, second=0, microsecond=0)

    # If we're exactly at the start of an interval, we want the previous completed interval
    if (
        minutes % interval_minutes == 0
        and current_time.second == 0
        and current_time.microsecond == 0
    ):
        # We're at 02:10:00, so we want 02:05:00 to 02:10:00
        interval_start = end_time - datetime.timedelta(minutes=interval_minutes)
        return interval_start, end_time

    # For any other time (e.g., 02:07:23), we want the previous completed interval
    # So if we're in the 02:05:00-02:10:00 interval, we want 02:00:00-02:05:00
    interval_start = end_time - datetime.timedelta(minutes=interval_minutes)
    interval_end = interval_start + datetime.timedelta(minutes=interval_minutes)

    return interval_start, interval_end


def format_time(dt, format_str=DATETIME_FORMAT):
    """Format a datetime object as a string."""
    return dt.strftime(format_str)


def get_metrics_formatted_interval(
    current_time, interval_minutes=5, format_str=DATETIME_FORMAT
):
    """
    Get the current and previous 5-minute interval as formatted strings.

    Args:
        current_time: Optional datetime object (uses current time if None)
        interval_minutes: Size of the interval in minutes (default: 5)
        format_str: String format for the datetime (default: "%Y-%m-%d %H:%M:%S")

    Returns:
        tuple of (formatted_interval_start, formatted_interval_end)
    """

    interval_start, interval_end = get_most_recent_metrics_time_interval(
        current_time, interval_minutes
    )
    return format_time(interval_start, format_str), format_time(interval_end, format_str)


def merge_and_format_intervals(
    intervals: list[tuple[datetime.datetime, datetime.datetime]],
) -> str | list[str]:
    """
    Merge adjacent or overlapping intervals and return formatted interval strings.

    Args:
        intervals: list of (interval_start, interval_end) tuples where each dt is a datetime object.
                  The intervals should be sorted by interval_start for optimal results.

    Returns:
        - Single formatted interval string if only one merged interval
        - list of formatted interval strings if multiple merged intervals
        Format:
        - Same day: "(YYYY-MM-DD HH:MM - HH:MM )"
        - Different days: "(YYYY-MM-DD HH:MM - YYYY-MM-DD HH:MM)"

    Example:
        Input: [(2025-06-10 09:30:00, 2025-06-10 09:35:00),
                (2025-06-10 09:35:00, 2025-06-10 09:40:00),
                (2025-06-11 09:30:00, 2025-06-11 09:35:00)]
        Output: ["(2025-06-10 09:30-09:40 )", "(2025-06-11 09:30-09:35 )"]

        Single interval input: [(2025-06-10 23:55:00, 2025-06-11 00:05:00)]
        Output: "(2025-06-10 23:55 - 2025-06-11 00:05)"
    """
    if not intervals:
        return []

    # Sort intervals by start time to ensure proper merging
    sorted_intervals = sorted(intervals, key=lambda x: x[0])

    merged: list[tuple[datetime.datetime, datetime.datetime]] = []
    current_start, current_end = sorted_intervals[0]

    for start, end in sorted_intervals[1:]:
        # Merge if intervals are adjacent or overlapping (regardless of date)
        if current_end >= start:
            # Extend the current interval to include the new end time
            current_end = max(current_end, end)
        else:
            # No merge possible, save current interval and start a new one
            merged.append((current_start, current_end))
            current_start, current_end = start, end

    # Don't forget the last interval
    merged.append((current_start, current_end))

    # Format intervals with parentheses as requested
    result: list[str] = []
    for start, end in merged:
        if start.date() == end.date():
            # Same day format: ( YYYY-MM-DD HH:MM - HH:MM )
            result.append(
                f"( {start.date()} {start.hour:02d}:{start.minute:02d} - {end.hour:02d}:{end.minute:02d} )"
            )
        else:
            # Different days format: ( YYYY-MM-DD HH:MM - YYYY-MM-DD HH:MM )
            result.append(
                f"( {start.date()} {start.hour:02d}:{start.minute:02d} - {end.date()} {end.hour:02d}:{end.minute:02d} )"
            )

    # Return single string if only one result, otherwise return list
    return result[0] if len(result) == 1 else result
