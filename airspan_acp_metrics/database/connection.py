import asyncio
import contextlib
import functools
import logging


try:
    from importlib import resources as importlib_resources
except ImportError:
    import importlib_resources

from alembic import command
from alembic.config import Config as alembic_config
from da_common.config import Config
from sqlalchemy.engine import URL
from sqlalchemy.exc import DBAPIError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.ext.asyncio import async_sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy.orm import declarative_base
from sqlalchemy.sql import text


Base = declarative_base()


def get_db_url(config: Config) -> URL:
    config_db = config.data["db"].copy()
    if "user" in config_db:
        config_db["username"] = config_db["user"]
        del config_db["user"]
    return URL.create(drivername="postgresql+asyncpg", **config_db)


def get_async_engine(async_db_url):
    return create_async_engine(
        async_db_url,
        pool_pre_ping=True,
        pool_size=32,
        max_overflow=64,
        # connect to metrics schema
        connect_args={"server_settings": {"search_path": "metrics"}},
    )


def get_async_session_maker(config: Config) -> async_sessionmaker[AsyncSession]:
    async_db_url = get_db_url(config)
    async_engine = get_async_engine(async_db_url)
    async_session_maker = async_sessionmaker(
        async_engine, expire_on_commit=False, class_=AsyncSession
    )
    return async_session_maker


async def create_database(config: Config) -> bool:
    """
    Ensures the database specified in the config exists.
    Connects to a maintenance database (e.g., 'postgres') to issue the CREATE DATABASE command.

    Args:
        config: The application configuration.

    Returns:
        True if the database was newly created by this function.
        False if the database already existed.

    Raises:
        ValueError: If the database name is missing in the configuration.
        Exception: If any other unexpected error occurs during the database creation process.
    """
    # Work on a copy of the original DB parameters to avoid modifying the input config
    original_db_params_copy = config.data["db"].copy()
    target_database_name = original_db_params_copy.pop("database", None)

    if not target_database_name:
        logging.error("Database name not found in configuration for creation.")
        raise ValueError("Database name missing in configuration for creation.")

    # Prepare connection parameters for the maintenance database
    # Handle 'user' vs 'username' for URL creation, ensuring 'username' is used by URL.create
    if "user" in original_db_params_copy and "username" not in original_db_params_copy:
        original_db_params_copy["username"] = original_db_params_copy.pop("user")

    # Connect to the default 'postgres' maintenance database
    maintenance_db_connect_params = {**original_db_params_copy, "database": "postgres"}

    maintenance_url = URL.create(
        drivername="postgresql+asyncpg", **maintenance_db_connect_params
    )
    maintenance_engine = None
    try:
        maintenance_engine = get_async_engine(maintenance_url)
        logging.info(
            f"Attempting to create database '{target_database_name}' if it does not exist."
        )
        async with maintenance_engine.connect() as connection:
            # For PostgreSQL, CREATE DATABASE cannot run inside a transaction block.
            # We get a version of the connection configured for AUTOCOMMIT.
            # The execution_options method itself seems to return an awaitable in this context.
            autocommit_connection = await connection.execution_options(
                isolation_level="AUTOCOMMIT"
            )
            await autocommit_connection.execute(
                text(f'CREATE DATABASE "{target_database_name}"')
            )
        logging.info(f"Database '{target_database_name}' created successfully.")
        return True  # Database was newly created
    except DBAPIError as e:
        # Check for PostgreSQL specific error code '42P04' (duplicate_database)
        with contextlib.suppress(AttributeError):
            if (
                hasattr(e, "orig")
                and e.orig is not None
                and hasattr(e.orig, "pgcode")
                and e.orig.pgcode == "42P04"
            ):
                logging.warning(
                    f"Database '{target_database_name}' already exists (detected by pgcode '42P04'). No action taken."
                )
                return False  # Database already existed

        # If it's a DBAPIError but not the specific one we're handling, re-raise it.
        logging.error(
            f"A DBAPIError occurred while trying to create database '{target_database_name}': {e}"
        )
        raise
    except Exception as e:
        logging.error(
            f"An unexpected error occurred while trying to create database '{target_database_name}': {e}"
        )
        raise
    finally:
        if maintenance_engine:
            await maintenance_engine.dispose()

    # This should never be reached due to the try/except structure above
    return False


async def create_metrics_schema(config: Config) -> bool:
    """
    Ensures the 'metrics' schema exists in the target database.

    Args:
        config: The application configuration.

    Returns:
        True if the schema was newly created by this function.
        False if the schema already existed.

    Raises:
        Exception: If any error occurs during schema creation.
    """
    async_db_url = get_db_url(config)
    # Create engine without search_path to connect to default schema
    engine = create_async_engine(
        async_db_url,
        pool_pre_ping=True,
        pool_size=10,
        max_overflow=20,
    )

    try:
        logging.info("Checking if 'metrics' schema exists...")
        async with engine.connect() as connection:
            # Check if metrics schema exists
            result = await connection.execute(
                text(
                    "SELECT schema_name FROM information_schema.schemata WHERE schema_name = 'metrics'"
                )
            )
            schema_exists = result.fetchone() is not None

            if not schema_exists:
                logging.info("Creating 'metrics' schema...")
                await connection.execute(text("CREATE SCHEMA metrics"))
                await connection.commit()
                logging.info("Schema 'metrics' created successfully.")
                return True
            else:
                logging.info("Schema 'metrics' already exists.")
                return False

    except Exception as e:
        logging.error(f"Error creating metrics schema: {e}")
        raise
    finally:
        await engine.dispose()


async def apply_db_migration(config: Config):
    """
    Apply database migrations synchronously to ensure they complete before the application starts.

    This function blocks until all migrations are complete, ensuring that the database schema
    is in a consistent state before any workers start processing requests.

    Args:
        config: The application configuration containing database connection details.
    """

    # First, create the database if it doesn't exist
    await create_database(config)

    # Second, create the metrics schema if it doesn't exist
    await create_metrics_schema(config)

    # Set up Alembic configuration
    alembic_ini_path = importlib_resources.files("airspan_acp_metrics").joinpath("alembic.ini")
    alembic_migrations_path = importlib_resources.files("airspan_acp_metrics").joinpath(
        "alembic_migrations"
    )

    alembic_cfg = alembic_config(str(alembic_ini_path))
    alembic_cfg.set_main_option("script_location", str(alembic_migrations_path))

    # Convert the async URL to a synchronous one for Alembic
    async_url = get_db_url(config)
    # Replace asyncpg with psycopg2 for synchronous operations
    sync_url = str(async_url).replace("postgresql+asyncpg", "postgresql")
    alembic_cfg.set_main_option("sqlalchemy.url", sync_url)

    # Run Alembic migrations synchronously in a thread pool to avoid blocking the event loop
    # This effectively makes the migration process synchronous from the application's perspective
    logging.info("Starting database migrations...")
    loop = asyncio.get_running_loop()
    await loop.run_in_executor(None, functools.partial(command.upgrade, alembic_cfg, "head"))
    logging.info("Database migration completed successfully")
