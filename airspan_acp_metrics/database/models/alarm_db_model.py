import datetime
import uuid
from uuid import UUI<PERSON>

from sqlalchemy import <PERSON><PERSON><PERSON>
from sqlalchemy import DateTime
from sqlalchemy import String
from sqlalchemy import func
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column

from airspan_acp_metrics.database.connection import Base


class AlarmDBModel(Base):
    """Database model for storing alarm/alert information."""

    __tablename__ = "alarm"

    id: Mapped[UUID] = mapped_column(nullable=False, primary_key=True, default=uuid.uuid4)
    type: Mapped[str] = mapped_column(String, nullable=False)
    name: Mapped[str] = mapped_column(String, nullable=False)
    severity: Mapped[str] = mapped_column(String, nullable=False)
    priority: Mapped[str] = mapped_column(String, nullable=False)

    source: Mapped[str] = mapped_column(String, nullable=False)
    created_at: Mapped[datetime.datetime] = mapped_column(
        DateTime(timezone=True), nullable=False, server_default=func.now()
    )
    updated_at: Mapped[datetime.datetime | None] = mapped_column(
        DateTime(timezone=True), nullable=True
    )
    resolved_at: Mapped[datetime.datetime | None] = mapped_column(
        DateTime(timezone=True), nullable=True
    )
    resolver: Mapped[str | None] = mapped_column(String, nullable=True)

    acknowledged: Mapped[datetime.datetime | None] = mapped_column(
        DateTime(timezone=True), nullable=True
    )
    acknowledger: Mapped[str | None] = mapped_column(String, nullable=True)

    status: Mapped[str] = mapped_column(String, nullable=False)
    description: Mapped[str | None] = mapped_column(String, nullable=True)
    repairs: Mapped[list[str] | None] = mapped_column(JSON, nullable=True)

    manager_instance: Mapped[str] = mapped_column(String, nullable=False)

    def __repr__(self):
        return f"<AlarmDBModel(id={self.id}, name='{self.name}', severity='{self.severity}', status='{self.status}')>"
