import uuid
from datetime import datetime

from sqlalchemy import UUID
from sqlalchemy import DateTime
from sqlalchemy import func
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column
from sqlalchemy.orm import relationship

from airspan_acp_metrics.database.connection import Base

# flake8: noqa
# fmt: off


class MetricsDbModel(Base):
    __tablename__ = "metrics"

    id:                Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    manager_instance:  Mapped[str]       = mapped_column(nullable=False, index=True)
    metric_name:       Mapped[str]       = mapped_column(nullable=False, index=True)
    gcs_file_path:     Mapped[str]       = mapped_column(nullable=False, index=True)
    interval_start:    Mapped[datetime]  = mapped_column(DateTime(timezone=True), nullable=False, index=True)
    interval_end:      Mapped[datetime]  = mapped_column(DateTime(timezone=True), nullable=False, index=True)
    created_at:        Mapped[datetime]  = mapped_column(DateTime(timezone=True), nullable=False, server_default=func.now())
    updated_at:        Mapped[datetime]  = mapped_column(DateTime(timezone=True), nullable=False, server_default=func.now(), onupdate=func.now())

    # relationship to metric_audits table
    audit = relationship("MetricsAuditDbModel", back_populates="metric")

    def __repr__(self):
        return f"<MetricsDbModel(id='{self.id}', manager_instance='{self.manager_instance}', interval_start='{self.interval_start}', interval_end='{self.interval_end})>"
