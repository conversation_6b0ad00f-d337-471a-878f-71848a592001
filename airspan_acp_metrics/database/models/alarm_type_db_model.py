from sqlalchemy import <PERSON>SO<PERSON>
from sqlalchemy import Column
from sqlalchemy import String

from airspan_acp_metrics.database.connection import Base


class AlarmTypeDBModel(Base):
    """Database model for alarm types configuration."""

    __tablename__ = "alarm_type"

    type = Column(String, primary_key=True, nullable=False)
    severity = Column(String, nullable=False)
    priority = Column(String, nullable=False)
    repairs = Column(JSON, nullable=True)

    def __repr__(self):
        return f"<AlarmTypeDBModel(type='{self.type}', severity='{self.severity}', priority='{self.priority}')>"
