from datetime import datetime

from sqlalchemy import DateTime
from sqlalchemy import func
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column
from sqlalchemy.orm import relationship

from airspan_acp_metrics.database.connection import Base

# flake8: noqa
# fmt: off


class InstanceDbModel(Base):
    __tablename__ = "instances"

    manager_instance:          Mapped[str]      = mapped_column(primary_key=True, nullable=False)
    url:                       Mapped[str]      = mapped_column(nullable=False)
    host_ip_address:           Mapped[str]      = mapped_column(nullable=True)
    host_secret_id:            Mapped[str]      = mapped_column(nullable=True)
    auth_type:                 Mapped[str]      = mapped_column(nullable=False, server_default="password")  # password or ssh_key
    metrics_collection_status: Mapped[str]      = mapped_column(nullable=False)
    reason:                    Mapped[str]      = mapped_column(nullable=True)  # Track SSH and connection errors
    created_at:                Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False, server_default=func.now())
    updated_at:                Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False, server_default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<InstanceDbModel(manager_instance='{self.manager_instance}')>"
