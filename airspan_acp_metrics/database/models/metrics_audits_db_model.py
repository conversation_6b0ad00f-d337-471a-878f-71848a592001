import uuid
from datetime import datetime

from sqlalchemy import UUI<PERSON>
from sqlalchemy import DateTime
from sqlalchemy import <PERSON><PERSON><PERSON>
from sqlalchemy import func
from sqlalchemy import UniqueConstraint
from sqlalchemy.dialects.postgresql.json import JSO<PERSON>
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column
from sqlalchemy.orm import relationship

from airspan_acp_metrics.database.connection import Base


# flake8: noqa
# fmt: off

class MetricsAuditDbModel(Base):
    __tablename__ = "metric_audits"

    __table_args__ = (
        UniqueConstraint(
            "manager_instance",
            "interval_start",
            "interval_end",
            "attempt_count",
            name="uix_metrics_audit"
        ),
    )

    id:                 Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    manager_instance:   Mapped[str]       = mapped_column(nullable=False, index=True)
    metric_name:        Mapped[str]       = mapped_column(nullable=False, index=True)
    file_metadata:      Mapped[dict]      = mapped_column(JSON, nullable=True)
    interval_start:     Mapped[datetime]  = mapped_column(DateTime(timezone=True), nullable=False, index=True)
    interval_end:       Mapped[datetime]  = mapped_column(DateTime(timezone=True), nullable=False, index=True)
    collection_status:  Mapped[str]       = mapped_column(nullable=False)  # COLLECTED, MISSING, BACKFILLED, FAILED
    last_attempt:       Mapped[datetime]  = mapped_column(DateTime(timezone=True), nullable=False, server_default=func.now())
    attempt_count:      Mapped[int]       = mapped_column(nullable=False, default=0)  # Track retry attempts
    reason:             Mapped[str]       = mapped_column(nullable=True)   # Store error details if collection failed
    created_at:         Mapped[datetime]  = mapped_column(DateTime(timezone=True), nullable=False, server_default=func.now())
    updated_at:         Mapped[datetime]  = mapped_column(DateTime(timezone=True), nullable=False, server_default=func.now(), onupdate=func.now())

    # foreign key to metrics table
    metrics_id:         Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("metrics.id"), nullable=True)

    # relationship to metrics table
    metric = relationship("MetricsDbModel", back_populates="audit")

    def __repr__(self):
        return f"<MetricsAuditDbModel(id='{self.id}', manager_instance='{self.manager_instance}', interval_start='{self.interval_start}', interval_end='{self.interval_end}', status='{self.collection_status}', updated_at='{self.updated_at}')>"
