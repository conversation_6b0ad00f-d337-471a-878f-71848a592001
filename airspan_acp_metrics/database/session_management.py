import asyncio
import logging
import random
import time
from collections.abc import Async<PERSON>enerator
from collections.abc import Callable
from contextlib import asynccontextmanager
from typing import Any
from typing import Generic
from typing import TypeV<PERSON>

from sqlalchemy.exc import OperationalError
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession


# Type variable for session type
SessionType = TypeVar("SessionType", bound=AsyncSession)

# Default retriable exceptions for session creation
DEFAULT_RETRIABLE_EXCEPTIONS: tuple[type[Exception], ...] = (
    OperationalError,  # Commonly wraps DB connection errors
    asyncio.TimeoutError,  # If session creation involves network I/O that might time out
)


class AsyncSessionManager(Generic[SessionType]):
    """A robust asynchronous context manager for SQLAlchemy sessions.

    This class provides a safe and reliable way to handle database sessions
    with proper error handling, connection cleanup, transaction management,
    and retry mechanisms for transient connection issues.

    Features:
    - Automatic commit on successful block exit.
    - Automatic rollback on exceptions within the block or during commit.
    - Proper session closing in a finally block to prevent connection leaks.
    - Detailed logging for session lifecycle events, errors, and performance.
    - Configurable retry mechanism with exponential backoff and jitter for session creation.
    - Type-safe session handling using generics.

    Usage:
        async_session_factory = async_sessionmaker(engine, expire_on_commit=False)

        async with AsyncSessionManager(async_session_factory) as session:
            # Use the session object
            result = await session.execute(select(MyTable))
            # ...
    """

    def __init__(
        self,
        session_factory: Callable[[], SessionType],
        max_retries: int = 3,
        initial_retry_delay: float = 0.5,
        max_retry_delay: float = 5.0,  # Maximum delay between retries
        backoff_factor: float = 2.0,
        jitter_factor: float = 0.1,  # e.g., 0.1 means delay can vary by +/- 10%
        retriable_exceptions: tuple[type[Exception], ...] = DEFAULT_RETRIABLE_EXCEPTIONS,
        logger: logging.Logger | None = None,
    ):
        """Initialize the session context manager.

        Args:
            session_factory: A callable that returns a new SQLAlchemy AsyncSession.
            max_retries: Maximum number of retry attempts for session creation.
            initial_retry_delay: Initial delay in seconds between retry attempts.
            max_retry_delay: Maximum delay in seconds for a single retry.
            backoff_factor: Multiplier for increasing retry delay (e.g., 2 for exponential).
            jitter_factor: Factor to introduce randomness in retry delay, reducing contention.
                        Actual delay = delay ± (delay * jitter_factor * random_uniform(-1, 1)).
            retriable_exceptions: A tuple of exception types that should trigger a retry
                                during session creation.
            logger: Optional custom logger instance. If None, a default logger is used.
        """
        if not callable(session_factory):
            raise TypeError("session_factory must be a callable")
        if max_retries < 0:
            raise ValueError("max_retries cannot be negative")
        if initial_retry_delay <= 0:
            raise ValueError("initial_retry_delay must be positive")
        if max_retry_delay < initial_retry_delay:
            raise ValueError("max_retry_delay cannot be less than initial_retry_delay")

        self.session_factory: Callable[[], SessionType] = session_factory
        self.session: SessionType | None = None
        self.max_retries = max_retries
        self.initial_retry_delay = initial_retry_delay
        self.max_retry_delay = max_retry_delay
        self.backoff_factor = backoff_factor
        self.jitter_factor = jitter_factor
        self.retriable_exceptions = retriable_exceptions

        self.logger = logger or logging.getLogger(__name__ + ".AsyncSessionManager")
        self.start_time: float = 0.0
        # self._session_id = uuid.uuid4() # For more advanced tracing if needed

    async def _create_session_with_retries(self) -> SessionType:
        """Attempts to create a session, retrying on specified transient errors."""
        last_exception: Exception | None = None
        current_delay = self.initial_retry_delay

        for attempt in range(self.max_retries + 1):
            try:
                session = self.session_factory()
                # You might want to perform a quick lightweight query to truly test connection
                # e.g., await session.execute(select(1))
                # However, this adds overhead and might not always be desirable.
                # For now, assume session_factory() failing is the main indicator.
                self.logger.debug(
                    f"Database session created successfully (attempt {attempt + 1})."
                )
                return session
            except self.retriable_exceptions as e:
                last_exception = e
                if attempt >= self.max_retries:
                    self.logger.error(
                        f"Failed to create database session after {self.max_retries + 1} attempts. "
                        f"Last error: {type(e).__name__}: {e}",
                        exc_info=True,  # Log stack trace for the final failure
                    )
                    break  # Exit loop to raise the last_exception

                self.logger.warning(
                    f"Failed to create database session (attempt {attempt + 1}/{self.max_retries + 1}): "
                    f"{type(e).__name__}: {e}. Retrying in {current_delay:.2f}s..."
                )

                # Calculate jitter: delay ± (delay * jitter_factor * random_uniform(-1, 1))
                # Simplified: delay * (1 + jitter_factor * random_uniform(-1, 1))
                jitter = current_delay * self.jitter_factor * (random.random() * 2 - 1)
                actual_delay = max(0.0, current_delay + jitter)  # Ensure delay is not negative

                await asyncio.sleep(actual_delay)

                current_delay = min(self.max_retry_delay, current_delay * self.backoff_factor)

            except SQLAlchemyError as e:  # Non-retriable SQLAlchemy errors
                last_exception = e
                self.logger.error(
                    f"Non-retriable SQLAlchemyError during session creation: {type(e).__name__}: {e}",
                    exc_info=True,
                )
                break  # Exit loop to raise this error immediately
            except Exception as e:  # Other unexpected errors
                last_exception = e
                self.logger.error(
                    f"Unexpected error during session creation: {type(e).__name__}: {e}",
                    exc_info=True,
                )
                break  # Exit loop to raise this error immediately

        assert last_exception is not None, "Loop finished without setting last_exception"
        # If we've exhausted retries or encountered a non-retriable error
        raise last_exception

    async def __aenter__(self) -> SessionType:
        """Enters the asynchronous context, creating and returning a new session."""
        self.start_time = time.perf_counter()
        self.logger.debug("Attempting to acquire database session...")

        try:
            self.session = await self._create_session_with_retries()
        except Exception as e:
            # _create_session_with_retries already logs sufficiently
            # No session to clean up here as self.session would be None or already handled
            elapsed_time = time.perf_counter() - self.start_time
            self.logger.error(
                f"Session acquisition failed after {elapsed_time:.3f}s. Error: {type(e).__name__}: {e}"
            )
            raise  # Re-raise the error from _create_session_with_retries

        self.logger.debug(f"Database session {id(self.session)} acquired.")
        return self.session

    async def __aexit__(
        self,
        exc_type: type[BaseException] | None,
        exc_val: BaseException | None,
        exc_tb: Any | None,  # TracebackType
    ) -> bool:
        """Exits the asynchronous context, handling transaction and closing the session."""
        if self.session is None:
            self.logger.warning(
                "No active session found upon exiting context. This is unusual."
            )
            return False

        try:
            await self._handle_transaction(exc_type, exc_val, exc_tb)
        finally:
            await self._close_session()

        # Do not suppress exceptions
        return False

    async def _handle_transaction(
        self,
        exc_type: type[BaseException] | None,
        exc_val: BaseException | None,
        exc_tb: Any | None,
    ) -> None:
        """Handle transaction commit or rollback based on context exit state."""
        session_id = id(self.session)

        if exc_type is None:
            # No exception in the 'with' block, try to commit
            await self._try_commit(session_id)
        else:
            # An exception occurred, perform rollback
            error_msg = f"{exc_type.__name__}: {exc_val!s}" if exc_val else exc_type.__name__
            self.logger.error(
                f"Exception in session context for session {session_id}. "
                f"Attempting rollback. Error: {error_msg}",
                exc_info=bool(exc_type),
            )
            await self._try_rollback(session_id, "context error")

    async def _try_commit(self, session_id: int) -> None:
        """Attempt to commit the transaction, rolling back on failure."""
        try:
            if self.session is None:
                raise RuntimeError(f"Cannot commit transaction - session {session_id} is None")

            self.logger.debug(f"Attempting to commit transaction for session {session_id}.")
            await self.session.commit()
            elapsed = time.perf_counter() - self.start_time
            self.logger.debug(
                f"Transaction committed successfully for session {session_id} in {elapsed:.3f}s."
            )
        except SQLAlchemyError as commit_error:
            self.logger.error(
                f"Failed to commit transaction for session {session_id}: {commit_error}",
                exc_info=True,
            )
            await self._try_rollback(session_id, "commit failure")
            raise commit_error
        except Exception as e:
            self.logger.error(
                f"Unexpected error during commit for session {session_id}: {e}",
                exc_info=True,
            )
            await self._try_rollback(session_id, "unexpected commit error")
            raise

    async def _try_rollback(self, session_id: int, reason: str) -> None:
        """Attempt to roll back the transaction, logging any failures."""
        try:
            if self.session is None:
                raise RuntimeError(
                    f"Cannot rollback transaction - session {session_id} is None"
                )

            await self.session.rollback()
            self.logger.debug(
                f"Transaction rolled back for session {session_id} after {reason}."
            )
        except Exception as rollback_error:
            self.logger.critical(
                f"CRITICAL: Failed to rollback transaction for session {session_id} "
                f"after {reason}: {rollback_error}",
                exc_info=True,
            )

    async def _close_session(self) -> None:
        """Close the session and clear the reference."""
        if not self.session:
            return

        session_id = id(self.session)
        try:
            self.logger.debug(f"Closing database session {session_id}.")
            await self.session.close()
            self.logger.debug(f"Database session {session_id} closed.")
        except Exception as close_error:
            self.logger.error(
                f"Error closing database session {session_id}: {close_error}",
                exc_info=True,
            )
        finally:
            self.session = None


@asynccontextmanager  # type: ignore
async def get_db_session(
    session_factory: Callable[[], SessionType],
    **kwargs: Any,
) -> AsyncGenerator[SessionType, None]:
    """A convenience async context manager for acquiring a database session.

    This wraps AsyncSessionManager for a slightly simpler syntax if defaults
    for retry mechanisms are acceptable or if kwargs are used for customization.

    Usage:
        async_session_factory = async_sessionmaker(engine)
        async with get_db_session(async_session_factory) as session:
            # ... use session ...

        # With custom retry settings
        async with get_db_session(async_session_factory, max_retries=5) as session:
            # ... use session ...

    Args:
        session_factory: A callable that returns a new SQLAlchemy AsyncSession.
        **kwargs: Additional keyword arguments to pass to AsyncSessionManager.

    Yields:
        An active SQLAlchemy AsyncSession (or a subtype specified by SessionType).
    """
    async with AsyncSessionManager(session_factory, **kwargs) as session:
        yield session
