import logging
from collections.abc import Sequence

from sqlalchemy import func
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from airspan_acp_metrics.database.models.alarm_db_model import AlarmDBModel
from airspan_acp_metrics.database.models.alarm_type_db_model import AlarmTypeDBModel
from airspan_acp_metrics.pyd_models.alarm_enums import AlarmStatus
from airspan_acp_metrics.pyd_models.alarm_enums import AlarmType
from airspan_acp_metrics.pyd_models.alarm_pyd_model import AlarmTypePydModel
from airspan_acp_metrics.pyd_models.alarm_pyd_model import AlarmTypeUpdatePydModel


class AlarmTypeDbService:
    """Service for managing alarm type database operations."""

    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session
        self.logger = logging.getLogger(
            "airspan_acp_metrics.database.services.alarm_type_db_service"
        )

    async def get_all_alarm_types(self) -> Sequence[AlarmTypeDBModel]:
        """Get all alarm types."""
        try:
            result = await self.db_session.execute(select(AlarmTypeDBModel))
            return result.scalars().all()
        except Exception as e:
            self.logger.error(f"Error getting all alarm types: {e}")
            raise

    async def get_alarm_type_by_type(self, alarm_type: AlarmType) -> AlarmTypeDBModel | None:
        """Get alarm type by type."""
        try:
            result = await self.db_session.execute(
                select(AlarmTypeDBModel).where(AlarmTypeDBModel.type == alarm_type)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            self.logger.error(f"Error getting alarm type {alarm_type}: {e}")
            raise

    async def create_alarm_type(self, alarm_type_data: AlarmTypePydModel) -> AlarmTypeDBModel:
        """Create a new alarm type."""
        try:
            new_alarm_type = AlarmTypeDBModel(
                type=alarm_type_data.type,
                severity=alarm_type_data.severity,
                priority=alarm_type_data.priority,
                repairs=alarm_type_data.repairs,
            )

            self.db_session.add(new_alarm_type)
            await self.db_session.commit()
            await self.db_session.refresh(new_alarm_type)

            self.logger.info(f"Created alarm type {alarm_type_data.type}")
            return new_alarm_type

        except Exception as e:
            self.logger.error(f"Error creating alarm type {alarm_type_data.type}: {e}")
            await self.db_session.rollback()
            raise

    async def update_alarm_type(
        self, alarm_type: AlarmType, update_data: AlarmTypeUpdatePydModel
    ) -> AlarmTypeDBModel | None:
        """Update an existing alarm type."""
        try:
            alarm_type_record = await self.get_alarm_type_by_type(alarm_type)
            if not alarm_type_record:
                return None

            # Update fields
            alarm_type_record.severity = update_data.severity
            alarm_type_record.priority = update_data.priority
            alarm_type_record.repairs = update_data.repairs

            await self.db_session.commit()
            await self.db_session.refresh(alarm_type_record)

            self.logger.info(f"Updated alarm type {alarm_type}")
            return alarm_type_record

        except Exception as e:
            self.logger.error(f"Error updating alarm type {alarm_type}: {e}")
            await self.db_session.rollback()
            raise

    async def delete_alarm_type(self, alarm_type: AlarmType) -> bool:
        """Delete an alarm type if no existing alarms reference it."""
        try:
            # Check if there are any alarms referencing this alarm type
            alarm_count = await self.db_session.execute(
                select(func.count(AlarmDBModel.id)).where(AlarmDBModel.type == alarm_type)
            )
            count = alarm_count.scalar()

            if count > 0:
                self.logger.warning(
                    f"Cannot delete alarm type {alarm_type}: {count} alarms still reference it"
                )
                return False

            # Delete the alarm type
            alarm_type_record = await self.get_alarm_type_by_type(alarm_type)
            if not alarm_type_record:
                self.logger.warning(f"Alarm type {alarm_type} not found for deletion")
                return False

            await self.db_session.delete(alarm_type_record)
            await self.db_session.commit()

            self.logger.info(f"Deleted alarm type {alarm_type}")
            return True

        except Exception as e:
            self.logger.error(f"Error deleting alarm type {alarm_type}: {e}")
            await self.db_session.rollback()
            raise

    async def get_alarm_type_usage_count(self, alarm_type: AlarmType) -> int:
        """Get count of alarms using this alarm type."""
        try:
            result = await self.db_session.execute(
                select(func.count(AlarmDBModel.id)).where(AlarmDBModel.type == alarm_type)
            )
            return result.scalar()

        except Exception as e:
            self.logger.error(f"Error getting usage count for alarm type {alarm_type}: {e}")
            raise

    async def get_alarm_type_active_usage_count(self, alarm_type: AlarmType) -> int:
        """Get count of active alarms using this alarm type."""
        try:
            result = await self.db_session.execute(
                select(func.count(AlarmDBModel.id)).where(
                    AlarmDBModel.type == alarm_type,
                    AlarmDBModel.status.in_(
                        [AlarmStatus.new, AlarmStatus.acknowledged, AlarmStatus.updated]
                    ),
                )
            )
            return result.scalar()

        except Exception as e:
            self.logger.error(
                f"Error getting active usage count for alarm type {alarm_type}: {e}"
            )
            raise
