import datetime
import logging
from collections.abc import Sequence

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from airspan_acp_metrics.custom_exceptions import NotFoundError
from airspan_acp_metrics.database.models.metrics_db_model import MetricsDbModel
from airspan_acp_metrics.pyd_models.metrics_pyd_model import MetricsPydModel


class MetricsDbService:
    def __init__(self, async_session: AsyncSession) -> None:
        self.async_session = async_session
        self.model = MetricsDbModel
        self.logger = logging.getLogger(
            "airspan_acp_metrics.database.services.metrics_db_service"
        )

    async def create_metrics(
        self, metrics_pyd_model: MetricsPydModel, prefix_log: str = ""
    ) -> MetricsDbModel:
        metrics_db_model = MetricsDbModel(**metrics_pyd_model.model_dump())
        self.async_session.add(metrics_db_model)
        await self.async_session.flush()
        await self.async_session.refresh(metrics_db_model)
        self.logger.info(
            f"{prefix_log}, MetricsDbService: Creating metrics record: {metrics_db_model.id} in the db"
        )
        return metrics_db_model

    async def create_metrics_bulk(
        self, metrics_pyd_models: list[MetricsPydModel]
    ) -> list[MetricsDbModel]:
        metrics_db_models = [
            MetricsDbModel(**metrics_pyd_model.model_dump())
            for metrics_pyd_model in metrics_pyd_models
        ]
        self.async_session.add_all(metrics_db_models)
        await self.async_session.flush()
        self.logger.info(f"MetricsDbService: Created {len(metrics_db_models)} metrics records")
        return metrics_db_models

    async def get_metrics_by_interval(
        self,
        manager_instance: str,
        metric_name: str,
        interval_start: datetime.datetime,
        interval_end: datetime.datetime,
    ) -> Sequence[MetricsDbModel]:
        query = select(self.model).where(
            self.model.manager_instance == manager_instance,
            self.model.metric_name == metric_name,
            self.model.interval_start == interval_start,
            self.model.interval_end == interval_end,
        )
        result = await self.async_session.execute(query)
        metrics_db_model = result.scalars().all()
        if not metrics_db_model:
            msg = f"MetricsDbService: No metrics found for {manager_instance} metric_name: {metric_name} interval_start: {interval_start} interval_end: {interval_end}"
            self.logger.error(msg)
            raise NotFoundError(msg)
        self.logger.info(
            f"MetricsDbService: Found metrics for {manager_instance} metric_name: {metric_name} interval_start: {interval_start} interval_end: {interval_end}"
        )
        return metrics_db_model
