import datetime
import logging
import uuid

from sqlalchemy import delete
from sqlalchemy import func
from sqlalchemy import or_
from sqlalchemy import select

from airspan_acp_metrics.custom_exceptions import NotFoundError
from airspan_acp_metrics.database.models.metrics_audits_db_model import MetricsAuditDbModel
from airspan_acp_metrics.database.models.metrics_db_model import MetricsDbModel
from airspan_acp_metrics.pyd_models.metrics_audit_pyd_model import MetricsAuditPydModel
from airspan_acp_metrics.pyd_models.metrics_audit_pyd_model import UpdateMetricsAuditPydModel
from airspan_acp_metrics.pyd_models.utils import MetricsCollectionStatus
from airspan_acp_metrics.utils import merge_and_format_intervals


class MetricsAuditDbService:
    """Service for interacting with metric audit records."""

    def __init__(self, async_session):
        self.async_session = async_session
        self.metrics_audit_db_model = MetricsAuditDbModel
        self.logger = logging.getLogger(
            "airspan_acp_metrics.database.services.metrics_audit_db_service"
        )

    async def create_audit(
        self,
        metrics_audit_pyd_model: MetricsAuditPydModel,
    ):
        """Create a new audit record."""
        audit = self.metrics_audit_db_model(**metrics_audit_pyd_model.model_dump())

        self.async_session.add(audit)
        await self.async_session.flush()
        self.logger.info(
            f"MetricsAuditDbService: Created audit record for {audit.manager_instance} from {merge_and_format_intervals([(audit.interval_start, audit.interval_end)])}"
        )
        await self.async_session.refresh(audit)
        return audit

    async def create_audits(self, metrics_audit_pyd_models: list[MetricsAuditPydModel]):
        """Create multiple audit records."""
        audits = [
            self.metrics_audit_db_model(**metrics_audit_pyd_model.model_dump())
            for metrics_audit_pyd_model in metrics_audit_pyd_models
        ]

        self.async_session.add_all(audits)
        await self.async_session.flush()
        self.logger.info(f"MetricsAuditDbService: Created {len(audits)} audit records")
        return audits

    async def update_audit(
        self,
        audit_db_model: MetricsAuditDbModel,
        update_metrics_audit_pyd_model: UpdateMetricsAuditPydModel,
        prefix_log: str = "",
    ):
        """Update an existing audit record."""

        kwargs = update_metrics_audit_pyd_model.model_dump(exclude_unset=True)
        for key, value in kwargs.items():
            setattr(audit_db_model, key, value)

        self.async_session.add(audit_db_model)
        await self.async_session.flush()
        self.logger.info(
            f"{prefix_log}, MetricsAuditDbService: Updated metrics audit record: {audit_db_model.id} with kwargs: {kwargs}"
        )
        await self.async_session.refresh(audit_db_model)
        return audit_db_model

    async def get_instance_metrics_audits(
        self,
        *,
        manager_instance: str,
        metric_name: str,
        interval_start: datetime.datetime | None = None,
        interval_end: datetime.datetime | None = None,
    ):
        """Get audit records for an instance within a time range."""
        query = select(self.metrics_audit_db_model).where(
            self.metrics_audit_db_model.manager_instance == manager_instance,
            self.metrics_audit_db_model.metric_name == metric_name,
        )

        if interval_start:
            query = query.where(self.metrics_audit_db_model.interval_end >= interval_start)

        if interval_end:
            query = query.where(self.metrics_audit_db_model.interval_start <= interval_end)

        query = query.order_by(self.metrics_audit_db_model.interval_start.desc())
        result = await self.async_session.execute(query)
        audits = result.scalars().all()
        msg = f"MetricsAuditDbService: Instance {manager_instance}, found {len(audits)} audits"
        if interval_start and interval_end:
            msg += f" from {merge_and_format_intervals([(interval_start, interval_end)])}"
        self.logger.info(msg)
        return audits

    async def get_pending_metrics_audits(
        self, manager_instance, max_audits_to_process, max_retry_attempts, prefix_log: str = ""
    ) -> list[MetricsAuditDbModel]:
        """Get audit records that need backfilling."""

        # flake8: noqa: E501
        query = (
            select(self.metrics_audit_db_model)
            .where(
                self.metrics_audit_db_model.manager_instance == manager_instance,
                or_(
                    self.metrics_audit_db_model.collection_status
                    == MetricsCollectionStatus.NOT_STARTED,
                    self.metrics_audit_db_model.collection_status
                    == MetricsCollectionStatus.FAILED,
                    self.metrics_audit_db_model.collection_status
                    == MetricsCollectionStatus.METRICS_EMPTY,
                ),
                self.metrics_audit_db_model.attempt_count <= max_retry_attempts,
            )
            .order_by(
                # Process newest records first
                self.metrics_audit_db_model.interval_start.desc()
            )
            .limit(max_audits_to_process)
        )  # Process in batches to avoid overloading

        result = await self.async_session.execute(query)
        pending_backfills = result.scalars().all()
        self.logger.info(
            f"{prefix_log}, MetricsAuditDbService found {len(pending_backfills)} pending metrics audits"
        )
        return pending_backfills

    async def get_instance_audit_metrics_for_processing(
        self, instance_name: str, limit: int = 10, max_retry_attempts: int = 3
    ) -> list[MetricsAuditDbModel]:
        """Get audit records that need backfilling for a specific instance.

        This query ensures only ONE audit per interval is returned (the one with highest attempt_count):
        1. Uses window function to rank audits by attempt_count per interval
        2. Only returns the audit with the highest attempt_count for each interval
        3. Includes FAILED, METRICS_EMPTY, and NOT_STARTED statuses
        4. Excludes RUNNING and COLLECTED statuses
        """

        # First subquery: Get ALL audits for the instance and rank by attempt_count
        # This includes COLLECTED and RUNNING audits in the ranking
        # flake8: noqa: E501
        ranked_audits = (
            select(
                self.metrics_audit_db_model,
                func.row_number()
                .over(
                    partition_by=[
                        self.metrics_audit_db_model.interval_start,
                        self.metrics_audit_db_model.interval_end,
                    ],
                    order_by=self.metrics_audit_db_model.attempt_count.desc(),
                )
                .label("rn"),
            ).where(
                self.metrics_audit_db_model.manager_instance == instance_name,
            )
        ).subquery()

        # Second query: Filter to only get intervals where the highest attempt (rn=1) is eligible
        # We need to check the status from the subquery, not from the joined table
        # flake8: noqa: E501
        query = (
            select(self.metrics_audit_db_model)
            .select_from(
                ranked_audits.join(
                    self.metrics_audit_db_model,
                    self.metrics_audit_db_model.id == ranked_audits.c.id,
                )
            )
            .where(
                ranked_audits.c.rn == 1,  # Only the highest attempt_count per interval
                # Check status from the ranked_audits subquery to ensure we're checking the highest attempt
                ranked_audits.c.collection_status.notin_(
                    [MetricsCollectionStatus.RUNNING, MetricsCollectionStatus.COLLECTED]
                ),
                # Now apply the eligible status filtering
                or_(
                    # Include NOT_STARTED audits with attempt_count <= max_retry
                    (ranked_audits.c.collection_status == MetricsCollectionStatus.NOT_STARTED)
                    & (ranked_audits.c.attempt_count <= max_retry_attempts),
                    # Include FAILED and METRICS_EMPTY audits with attempt_count < max_retry
                    (
                        ranked_audits.c.collection_status.in_(
                            [
                                MetricsCollectionStatus.FAILED,
                                MetricsCollectionStatus.METRICS_EMPTY,
                            ]
                        )
                    )
                    & (ranked_audits.c.attempt_count < max_retry_attempts),
                ),
            )
            .order_by(
                # Process most recent intervals first
                self.metrics_audit_db_model.interval_start.desc()
            )
            .limit(limit)
        )

        result = await self.async_session.execute(query)
        # refresh metric audit db model
        metrics_audit_db_models = result.scalars().all()
        for metrics_audit_db_model in metrics_audit_db_models:
            await self.async_session.refresh(metrics_audit_db_model)

        self.logger.info(
            f"MetricsAuditDbService: Found {len(metrics_audit_db_models)} audit records for processing for instance {instance_name}"
        )
        return metrics_audit_db_models

    async def get_metrics_audit_by_id(self, audit_id: uuid.UUID) -> MetricsAuditDbModel:
        stmt = select(self.metrics_audit_db_model).where(
            self.metrics_audit_db_model.id == audit_id
        )
        result = await self.async_session.execute(stmt)
        audit = result.scalar_one_or_none()
        if not audit:
            msg = f"MetricsAuditDbService: No audit found for id {audit_id}"
            self.logger.error(msg)
            raise NotFoundError(msg)

        self.logger.info(f"MetricsAuditDbService: Found audit for id {audit_id}")
        await self.async_session.refresh(audit)
        return audit

    async def republish_metrics(
        self,
        manager_instance: str,
        metric_name: str,
        start_time: datetime.datetime,
        end_time: datetime.datetime,
    ) -> tuple[list[uuid.UUID], list[uuid.UUID]]:
        """Delete audit records and their associated metrics within a time range where status is not RUNNING.

        Args:
            manager_instance: Manager instance
            metric_name: Metric name
            start_time: Start of the time range for deletion
            end_time: End of the time range for deletion

        Returns:
            Tuple of (deleted_audits, deleted_metrics) with details of deleted records
        """

        # First, find all audit records in the time range that are not RUNNING
        audits_to_delete_stmt = select(
            self.metrics_audit_db_model.id,
            self.metrics_audit_db_model.metrics_id,
        ).where(
            self.metrics_audit_db_model.manager_instance == manager_instance,
            self.metrics_audit_db_model.metric_name == metric_name,
            self.metrics_audit_db_model.interval_start >= start_time,
            self.metrics_audit_db_model.interval_end <= end_time,
            self.metrics_audit_db_model.collection_status != MetricsCollectionStatus.RUNNING,
        )

        result = await self.async_session.execute(audits_to_delete_stmt)
        audits_to_delete = result.fetchall()

        if not audits_to_delete:
            self.logger.info(
                f"MetricsAuditDbService: No Non-RUNNING audit records found in time range {start_time} to {end_time}"
            )
            return [], []

        # Extract audit ids to delete
        audit_ids_to_delete = [audit[0] for audit in audits_to_delete]

        if audit_ids_to_delete:
            # delete audit records first to avoid foreign key constraint errors
            delete_audits_stmt = delete(self.metrics_audit_db_model).where(
                self.metrics_audit_db_model.id.in_(audit_ids_to_delete)
            )
            await self.async_session.execute(delete_audits_stmt)
            self.logger.info(
                f"MetricsAuditDbService: Deleted {len(audit_ids_to_delete)} audit records in time range {start_time} to {end_time}"
            )

        # Extract metrics ids to delete
        metrics_ids_to_delete = [audit[1] for audit in audits_to_delete if audit[1]]

        # delete metrics records
        if metrics_ids_to_delete:
            metrics_to_delete_stmt = delete(MetricsDbModel).where(
                MetricsDbModel.id.in_(metrics_ids_to_delete)
            )
            await self.async_session.execute(metrics_to_delete_stmt)
            self.logger.info(
                f"MetricsAuditDbService: Deleted {len(metrics_ids_to_delete)} metrics records in time range {start_time} to {end_time}"
            )

        return audit_ids_to_delete, metrics_ids_to_delete
