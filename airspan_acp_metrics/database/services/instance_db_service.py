import contextlib
import logging
from collections.abc import Sequence

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from airspan_acp_metrics.custom_exceptions import AlreadyExistsError
from airspan_acp_metrics.custom_exceptions import NotFoundError
from airspan_acp_metrics.database.models.instance_db_model import InstanceDbModel
from airspan_acp_metrics.pyd_models.instance_pyd_model import InstancePydModel
from airspan_acp_metrics.pyd_models.instance_pyd_model import InstanceUpdatePydModel


class InstanceDbService:
    def __init__(self, async_session: AsyncSession) -> None:
        self.async_session = async_session
        self.model = InstanceDbModel
        self.logger = logging.getLogger(
            "airspan_acp_metrics.database.services.instance_service"
        )

    async def get_all_acp_instances(
        self,
        metrics_collection_status: str | None = None,
    ) -> Sequence[InstanceDbModel]:
        stmt = select(self.model)
        if metrics_collection_status:
            stmt = stmt.where(
                self.model.metrics_collection_status == metrics_collection_status,
                # below conditions are a safety net to avoid getting instances that are not ready for metrics collection
                self.model.host_ip_address.isnot(None),
                self.model.host_ip_address != "",
                self.model.host_secret_id.isnot(None),
                self.model.host_secret_id != "",
            )

        result = await self.async_session.execute(stmt)
        return result.scalars().all()

    async def get_acp_instance_by_manager_instance(
        self, manager_instance: str
    ) -> InstanceDbModel | None:
        stmt = select(self.model).where(self.model.manager_instance == manager_instance)
        result = await self.async_session.execute(stmt)
        instance_db_model = result.scalar_one_or_none()
        if not instance_db_model:
            msg = f"Instance: [ {manager_instance} ] does not exist"
            self.logger.error(msg)
            return None

        self.logger.info(f"Instance: [ {manager_instance} ] found")
        await self.async_session.refresh(instance_db_model)
        return instance_db_model

    async def create_acp_instance(
        self, acp_instance_pyd_model: InstancePydModel
    ) -> InstanceDbModel:
        with contextlib.suppress(NotFoundError):
            if await self.get_acp_instance_by_manager_instance(
                acp_instance_pyd_model.manager_instance
            ):
                msg = f"Instance: [ {acp_instance_pyd_model.manager_instance} ] already exists"
                self.logger.error(msg)
                raise AlreadyExistsError(msg)

        instance_db_model = self.model(**acp_instance_pyd_model.model_dump())
        self.async_session.add(instance_db_model)
        await self.async_session.flush()
        self.logger.info(f"Instance: [ {instance_db_model.manager_instance} ] created")
        await self.async_session.refresh(instance_db_model)
        return instance_db_model

    async def update_acp_instance(
        self,
        instance_pyd_model: InstanceUpdatePydModel,
        instance_db_model: InstanceDbModel | None = None,
    ) -> InstanceDbModel:
        if not instance_db_model:
            instance_db_model = await self.get_acp_instance_by_manager_instance(
                instance_pyd_model.manager_instance
            )

        if not instance_db_model:
            return None

        # Only update fields that should be updatable (exclude timestamps)
        update_fields = instance_pyd_model.model_dump(exclude={"manager_instance"})
        for field, value in update_fields.items():
            if value is not None:
                setattr(instance_db_model, field, value)

        await self.async_session.flush()
        await self.async_session.refresh(instance_db_model)
        self.logger.info(f"Instance: [ {instance_db_model.manager_instance} ] updated")
        return instance_db_model

    async def update_metrics_collection_status(
        self, manager_instance: str, status: str, error_reason: str | None = None
    ) -> InstanceDbModel:
        """Update the metrics collection status and error reason for an instance."""
        instance_db_model = await self.get_acp_instance_by_manager_instance(manager_instance)

        if not instance_db_model:
            return None

        instance_db_model.metrics_collection_status = status
        instance_db_model.reason = error_reason

        await self.async_session.flush()
        await self.async_session.refresh(instance_db_model)

        status_msg = (
            f"Instance: [ {manager_instance} ] metrics collection status updated to {status}"
        )
        if error_reason:
            status_msg += f" with error: {error_reason}"
        self.logger.info(status_msg)

        return instance_db_model

    async def delete_acp_instance(self, manager_instance: str) -> None:
        instance_db_model = await self.get_acp_instance_by_manager_instance(manager_instance)

        if not instance_db_model:
            return None

        await self.async_session.delete(instance_db_model)
        await self.async_session.flush()
        self.logger.info(f"Instance: [ {manager_instance} ] deleted")
