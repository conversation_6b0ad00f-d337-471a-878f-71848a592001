import logging
from collections.abc import Sequence
from uuid import UUID

from sqlalchemy import and_
from sqlalchemy import func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from airspan_acp_metrics.database.models.alarm_db_model import AlarmDBModel
from airspan_acp_metrics.pyd_models.alarm_enums import AlarmStatus
from airspan_acp_metrics.pyd_models.alarm_pyd_model import AlarmAcknowledgePydModel
from airspan_acp_metrics.pyd_models.alarm_pyd_model import AlarmCreatePydModel
from airspan_acp_metrics.pyd_models.alarm_pyd_model import AlarmFilterPydModel
from airspan_acp_metrics.pyd_models.alarm_pyd_model import AlarmResolvePydModel
from airspan_acp_metrics.pyd_models.alarm_pyd_model import AlarmUpdatePydModel


class AlarmDbService:
    """Service for managing alarm database operations."""

    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session
        self.logger = logging.getLogger(
            "airspan_acp_metrics.database.services.alarm_db_service"
        )

    async def create_alarm(self, alarm_data: AlarmCreatePydModel) -> AlarmDBModel:
        """Create a new alarm in the database."""
        try:
            # Get alarm type details for severity and priority
            from airspan_acp_metrics.database.services.alarm_type_db_service import (
                AlarmTypeDbService,
            )

            alarm_type_service = AlarmTypeDbService(self.db_session)
            alarm_type = await alarm_type_service.get_alarm_type_by_type(alarm_data.type)

            if not alarm_type:
                raise ValueError(f"Alarm type {alarm_data.type} not found")

            new_alarm = AlarmDBModel(
                type=alarm_data.type,
                name=alarm_data.name,
                severity=alarm_type.severity,
                priority=alarm_type.priority,
                source=alarm_data.source,
                description=alarm_data.description,
                status=AlarmStatus.new,
                repairs=alarm_type.repairs,
                manager_instance=alarm_data.manager_instance,
            )

            self.db_session.add(new_alarm)
            await self.db_session.commit()
            await self.db_session.refresh(new_alarm)

            self.logger.info(f"Created alarm {new_alarm.id} of type {alarm_data.type}")
            return new_alarm

        except Exception as e:
            self.logger.error(f"Error creating alarm: {e}")
            await self.db_session.rollback()
            raise

    async def get_alarm_by_id(self, alarm_id: UUID) -> AlarmDBModel | None:
        """Get an alarm by ID."""
        try:
            result = await self.db_session.execute(
                select(AlarmDBModel).where(AlarmDBModel.id == alarm_id)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            self.logger.error(f"Error getting alarm by ID {alarm_id}: {e}")
            raise

    async def get_alarms_by_filter(
        self, filter_data: AlarmFilterPydModel
    ) -> Sequence[AlarmDBModel]:
        """Get alarms by filter criteria."""
        try:
            query = select(AlarmDBModel)

            # Apply filters
            conditions = []
            if filter_data.type:
                conditions.append(AlarmDBModel.type == filter_data.type)
            if filter_data.severity:
                conditions.append(AlarmDBModel.severity == filter_data.severity)
            if filter_data.priority:
                conditions.append(AlarmDBModel.priority == filter_data.priority)
            if filter_data.status:
                conditions.append(AlarmDBModel.status == filter_data.status)
            if filter_data.manager_instance:
                conditions.append(AlarmDBModel.manager_instance == filter_data.manager_instance)
            if filter_data.source:
                conditions.append(AlarmDBModel.source == filter_data.source)

            if conditions:
                query = query.where(and_(*conditions))

            # Apply ordering (newest first)
            query = query.order_by(AlarmDBModel.created_at.desc())

            # Apply pagination
            query = query.offset(filter_data.offset).limit(filter_data.limit)

            result = await self.db_session.execute(query)
            return result.scalars().all()

        except Exception as e:
            self.logger.error(f"Error getting alarms by filter: {e}")
            raise

    async def update_alarm(
        self, alarm_id: UUID, update_data: AlarmUpdatePydModel
    ) -> AlarmDBModel | None:
        """Update an existing alarm."""
        try:
            alarm = await self.get_alarm_by_id(alarm_id)
            if not alarm:
                return None

            # Update fields
            if update_data.description is not None:
                alarm.description = update_data.description
            if update_data.updated_at is not None:
                alarm.updated_at = update_data.updated_at

            alarm.status = AlarmStatus.updated

            await self.db_session.commit()
            await self.db_session.refresh(alarm)

            self.logger.info(f"Updated alarm {alarm_id}")
            return alarm

        except Exception as e:
            self.logger.error(f"Error updating alarm {alarm_id}: {e}")
            await self.db_session.rollback()
            raise

    async def acknowledge_alarm(
        self, alarm_id: UUID, acknowledge_data: AlarmAcknowledgePydModel
    ) -> AlarmDBModel | None:
        """Acknowledge an alarm."""
        try:
            alarm = await self.get_alarm_by_id(alarm_id)
            if not alarm:
                return None

            alarm.acknowledged = acknowledge_data.acknowledged
            alarm.acknowledger = acknowledge_data.acknowledger
            alarm.status = AlarmStatus.acknowledged

            await self.db_session.commit()
            await self.db_session.refresh(alarm)

            self.logger.info(f"Acknowledged alarm {alarm_id}")
            return alarm

        except Exception as e:
            self.logger.error(f"Error acknowledging alarm {alarm_id}: {e}")
            await self.db_session.rollback()
            raise

    async def resolve_alarm(
        self, alarm_id: UUID, resolve_data: AlarmResolvePydModel
    ) -> AlarmDBModel | None:
        """Resolve an alarm."""
        try:
            alarm = await self.get_alarm_by_id(alarm_id)
            if not alarm:
                return None

            alarm.resolved_at = resolve_data.resolved_at
            alarm.resolver = resolve_data.resolver
            alarm.status = AlarmStatus.resolved

            await self.db_session.commit()
            await self.db_session.refresh(alarm)

            self.logger.info(f"Resolved alarm {alarm_id}")
            return alarm

        except Exception as e:
            self.logger.error(f"Error resolving alarm {alarm_id}: {e}")
            await self.db_session.rollback()
            raise

    async def get_active_alarms_by_instance(
        self, manager_instance: str
    ) -> Sequence[AlarmDBModel]:
        """Get active alarms for a specific instance."""
        try:
            result = await self.db_session.execute(
                select(AlarmDBModel)
                .where(
                    and_(
                        AlarmDBModel.manager_instance == manager_instance,
                        AlarmDBModel.status.in_(
                            [AlarmStatus.new, AlarmStatus.acknowledged, AlarmStatus.updated]
                        ),
                    )
                )
                .order_by(AlarmDBModel.created_at.desc())
            )
            return result.scalars().all()

        except Exception as e:
            self.logger.error(
                f"Error getting active alarms for instance {manager_instance}: {e}"
            )
            raise

    async def get_active_alarm_by_type_and_instance(
        self, alarm_type: str, manager_instance: str
    ) -> AlarmDBModel | None:
        """Get active alarm by type and instance."""
        try:
            result = await self.db_session.execute(
                select(AlarmDBModel)
                .where(
                    and_(
                        AlarmDBModel.type == alarm_type,
                        AlarmDBModel.manager_instance == manager_instance,
                        AlarmDBModel.status.in_(
                            [AlarmStatus.new, AlarmStatus.acknowledged, AlarmStatus.updated]
                        ),
                    )
                )
                .order_by(AlarmDBModel.created_at.desc())
            )
            return result.scalar_one_or_none()

        except Exception as e:
            self.logger.error(
                f"Error getting active alarm by type {alarm_type} and instance {manager_instance}: {e}"
            )
            raise

    async def get_alarm_count_by_severity(self) -> dict:
        """Get count of alarms by severity."""
        try:
            result = await self.db_session.execute(
                select(AlarmDBModel.severity, func.count(AlarmDBModel.id))
                .where(
                    AlarmDBModel.status.in_(
                        [AlarmStatus.new, AlarmStatus.acknowledged, AlarmStatus.updated]
                    )
                )
                .group_by(AlarmDBModel.severity)
            )
            return dict(result.all())

        except Exception as e:
            self.logger.error(f"Error getting alarm count by severity: {e}")
            raise
