import logging
from contextlib import asynccontextmanager

import sentry_sdk
from da_common.health_check import health_router
from da_common.middleware import NMSMiddleware
from da_common.security import security_router
from dal_pubsub.pubsub import PubSub
from fastapi import FastAPI
from sentry_sdk.integrations.fastapi import FastApiIntegration
from sentry_sdk.integrations.logging import LoggingIntegration
from sentry_sdk.integrations.sqlalchemy import SqlalchemyIntegration

import airspan_acp_metrics
from airspan_acp_metrics.database.connection import apply_db_migration
from airspan_acp_metrics.database.connection import get_async_session_maker
from airspan_acp_metrics.routers.alarm_router import alarms_router
from airspan_acp_metrics.routers.alarm_type_router import alarm_types_router
from airspan_acp_metrics.routers.metrics_audit_router import router as metrics_audit_router
from airspan_acp_metrics.workers.metrics_audit_worker import MetricsAuditWorker
from airspan_acp_metrics.workers.metrics_collection_worker import MetricsCollectionWorker
from airspan_acp_metrics.workers.ssh_connectivity_worker import SSHConnectivityWorker


def _setup_sentry(config) -> None:
    """
    Setup Sentry error tracking.

    Args:
        config: Application configuration
    """
    # Skip Sentry setup if in test environment
    config_db = config.data.get("db", {})
    if "test" in config_db.get("database", ""):
        return

    sentry_config = config.data.get("sentry", {})
    if not sentry_config.get("dsn"):
        return

    # Extract environment prefix from project name
    project_id = config.data.get("google", {}).get("project")
    if project_id:
        project_env_prefix = "-".join(project_id.split("-")[1:3])
    else:
        project_env_prefix = "environment-not-set"

    # Initialize Sentry
    sentry_sdk.init(
        dsn=sentry_config.get("dsn"),
        environment=project_env_prefix,
        release=f"{airspan_acp_metrics.__version__}",
        sample_rate=sentry_config.get("sample_rate", 0.1),
        traces_sample_rate=sentry_config.get("traces_sample_rate", 0.1),
        profiles_sample_rate=0.1,
        integrations=[
            FastApiIntegration(),
            LoggingIntegration(level=logging.INFO, event_level=logging.ERROR),
            SqlalchemyIntegration(),
        ],
        enable_tracing=True,
    )


def register_routers(app: FastAPI) -> None:
    """
    Register all routers
    Args:
        app: FastAPI app

    Returns:
        None

    """

    app.include_router(health_router)
    app.include_router(security_router)
    app.include_router(metrics_audit_router)
    app.include_router(alarms_router)
    app.include_router(alarm_types_router)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    get database url
    create async_engine and then create session maker
    and then add it to the app state

    close engine

    """

    config = app.state.config

    # Perform database migration on startup
    # This will block until migrations are complete, ensuring database consistency
    # The function uses run_in_executor internally
    # to run migrations without blocking the event loop
    await apply_db_migration(config)

    # Set up the async session maker after migrations are complete
    app.state.async_session_maker = get_async_session_maker(config)

    # Initialize PubSub and other components AFTER database migrations are complete
    app.state.pub_sub = PubSub(config=config)
    app.state.metrics_topic = config.data["pubsub"]["metrics"]["topic"]
    app.state.alarm_topic = config.data["pubsub"].get("alarm", {}).get("topic", "nms-alarms")

    # Initialize and start the workers AFTER database migrations are complete
    app.state.metrics_collection_worker = MetricsCollectionWorker(config=config)
    app.state.metrics_collection_worker.start()

    app.state.metrics_audit_worker = MetricsAuditWorker(config=config)
    app.state.metrics_audit_worker.start()

    app.state.ssh_connectivity_worker = SSHConnectivityWorker(config=config)
    app.state.ssh_connectivity_worker.start()

    yield

    # Shutdown the workers if it was started
    if hasattr(app.state, "metrics_collection_worker"):
        app.state.metrics_collection_worker.shutdown()
        logging.getLogger("airspan_acp_metrics").info(
            "MetricsCollectionWorker shutdown complete"
        )

    if hasattr(app.state, "metrics_audit_worker"):
        app.state.metrics_audit_worker.shutdown()
        logging.getLogger("airspan_acp_metrics").info("MetricsAuditWorker shutdown complete")

    if hasattr(app.state, "ssh_connectivity_worker"):
        app.state.ssh_connectivity_worker.shutdown()
        logging.getLogger("airspan_acp_metrics").info(
            "SSHConnectivityChecker shutdown complete"
        )

    # Ensure the engine is disposed if it was created and added to app.state
    # This line might need adjustment depending on how async_session_maker is initialized
    if hasattr(app.state, "async_session_maker") and app.state.async_session_maker:
        await app.state.async_session_maker.kw["bind"].dispose()


def create_app(config) -> FastAPI:
    """
    Create FastAPI app
    Returns:
        FastAPI app
    """
    logger = logging.getLogger("airspan_acp_metrics")
    logger.info(
        f"Starting Airspan Acp Metrics Collection, application version {airspan_acp_metrics.__version__}"
    )

    # Setup Sentry error tracking
    _setup_sentry(config)

    description = """
                    Airspan Acp Metrics Collection is the main entry point collecting ACP Metrics.
                  """

    app = FastAPI(
        title="Airspan Acp Metrics Collection",
        version=airspan_acp_metrics.__version__,
        description=description,
        lifespan=lifespan,
    )
    app.state.config = config
    app.state.package_name = "airspan_acp_metrics"
    app.state.clients = config.build_clients()
    app.add_middleware(NMSMiddleware)
    register_routers(app)
    return app
