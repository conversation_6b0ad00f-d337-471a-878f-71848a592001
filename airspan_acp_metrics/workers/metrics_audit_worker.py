import datetime
import logging

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from da_common.config import Config
from da_common.models import Status
from sqlalchemy import func
from sqlalchemy import select

from airspan_acp_metrics.database.connection import get_async_session_maker
from airspan_acp_metrics.database.models.metrics_audits_db_model import MetricsAuditDbModel
from airspan_acp_metrics.database.services.instance_db_service import InstanceDbService
from airspan_acp_metrics.database.services.metric_audit_db_service import MetricsAuditDbService
from airspan_acp_metrics.database.session_management import get_db_session
from airspan_acp_metrics.managers import DatabaseOperationsManager
from airspan_acp_metrics.managers import SSHConnectionManager
from airspan_acp_metrics.pyd_models.metrics_audit_pyd_model import MetricsAuditPydModel
from airspan_acp_metrics.pyd_models.utils import MetricsCollectionStatus
from airspan_acp_metrics.utils import get_most_recent_metrics_time_interval
from airspan_acp_metrics.utils import merge_and_format_intervals


class MetricsAuditWorker:
    """
    Worker that checks for missing metrics audits.
    """

    def __init__(self, config: Config):
        self.config = config
        self.async_session_maker = get_async_session_maker(self.config)
        self.scheduler = AsyncIOScheduler()
        self._is_running = False
        self.logger = logging.getLogger("airspan_acp_metrics.workers.metrics_audit_worker")

        # Initialize metrics collector for reusing collection logic
        self.ssh_manager = SSHConnectionManager(config)
        self.db_ops = DatabaseOperationsManager()

        # Get metrics configurations with backfill settings
        self.metrics_configs = {}
        for key, value in self.config.data.get("metrics", {}).items():
            if isinstance(value, dict) and value.get("enabled", False):
                # Create a new dict with proper types - use Any to avoid type checker issues
                config_with_defaults: dict = {}
                config_with_defaults.update(value)
                config_with_defaults.update(
                    {
                        "backfill_enabled": bool(value.get("backfill_enabled", True)),
                        "max_days_lookback": int(value.get("max_days_lookback", 30)),
                        "max_retry_attempts": int(value.get("max_retry_attempts", 3)),
                    }
                )
                self.metrics_configs[key] = config_with_defaults

        # Check if any metric has backfill enabled
        self.any_backfill_enabled = any(
            config.get("backfill_enabled", False) for config in self.metrics_configs.values()
        )
        # TODO: Make this configurable
        self.max_concurrent_backfills = 10

    @staticmethod
    def _get_log_prefix(
        *,
        instance_name: str | None = None,
        metric_name: str | None = None,
        intervals: list[tuple[datetime.datetime, datetime.datetime]] | None = None,
    ) -> str:
        """Generate standardized log prefix for tracking."""

        prefix_log = "MetricsAuditWorker"
        if instance_name:
            prefix_log += f" {instance_name}"
        if metric_name:
            prefix_log += f" {metric_name}"
        if intervals:
            prefix_log += f" {merge_and_format_intervals(intervals)}"
        return prefix_log

    def start(self):
        """Start the backfill worker scheduler."""
        prefix_log = self._get_log_prefix()
        if self._is_running or not self.any_backfill_enabled:
            self.logger.info(
                f"{prefix_log}: Not starting - running: {self._is_running}, any_enabled: {self.any_backfill_enabled}"
            )
            return

        self.logger.info(f"{prefix_log}, scheduler starting")

        # Schedule the job to check for missing metrics every minute
        self.scheduler.add_job(
            self._create_missing_metrics_audits,
            CronTrigger(minute="*", second=15),  # Run every minute at 30 seconds
            id="missing_metrics_audit_checker",
            replace_existing=True,
            misfire_grace_time=30,
        )

        self.scheduler.start()
        self._is_running = True
        self.logger.info(f"{prefix_log}, scheduler started")

    async def _create_missing_metrics_audits(self):
        """
        Check for missing metrics by comparing expected intervals with actual collected metrics.
        Creates audit records for missing intervals.
        """
        if not self.any_backfill_enabled:
            return

        prefix_log = self._get_log_prefix()
        self.logger.info(f"{prefix_log}, Checking for missing metrics_audits")

        async with get_db_session(self.async_session_maker) as db_session:
            for metric_name, metric_config in self.metrics_configs.items():
                prefix_log = self._get_log_prefix(metric_name=metric_name)
                if not self._is_metric_backfill_enabled(metric_config):
                    self.logger.info(
                        f"{prefix_log} is not enabled, skipping backfill processing"
                    )
                    continue

                instance_db_service = InstanceDbService(db_session)
                instance_db_models = await instance_db_service.get_all_acp_instances(
                    metrics_collection_status=Status.OK
                )
                if not instance_db_models:
                    self.logger.info(
                        f"{prefix_log}, No instances with ssh connectivity found, skipping creating missing metrics audits"
                    )
                    continue

                self.logger.info(
                    f"{prefix_log}, Found {len(instance_db_models)} instances having ssh connectivity"
                )

                now = datetime.datetime.now(datetime.UTC)
                for instance_db_model in instance_db_models:
                    await self._create_instance_missing_metrics_audits(
                        instance_db_model=instance_db_model,
                        metric_config=metric_config,
                        now=now,
                    )

    async def _create_instance_missing_metrics_audits(
        self, *, instance_db_model, metric_config, now: datetime.datetime
    ) -> None:
        """Check for missing metrics for a single instance across all enabled metric types."""
        async with get_db_session(self.async_session_maker) as db_session:
            instance_name = str(instance_db_model.manager_instance)
            metric_name = metric_config["metric_name"]
            prefix_log = self._get_log_prefix(
                instance_name=instance_name, metric_name=metric_name
            )

            self.logger.info(f"{prefix_log}, Checking missing metrics audits")

            try:
                instance_name = str(instance_db_model.manager_instance)
                metric_name = metric_config["metric_name"]

                prefix_log = self._get_log_prefix(
                    instance_name=instance_name, metric_name=metric_name
                )

                # Calculate time range for this metric
                time_range = self._calculate_metric_time_range(
                    instance_db_model, metric_config, now
                )
                if not time_range:
                    self.logger.error(
                        f"{prefix_log}, No valid time range, skipping missing metrics check"
                    )
                    return

                oldest_allowed, newest_allowed = time_range
                frequency_minutes = metric_config.get("frequency", 5)

                # Generate expected intervals and get existing audits
                expected_intervals = self._generate_expected_intervals(
                    oldest_allowed, newest_allowed, frequency_minutes
                )

                existing_audit_intervals = await self._get_existing_audit_intervals(
                    db_session=db_session,
                    instance_name=instance_name,
                    metric_name=metric_name,
                    interval_start=oldest_allowed,
                    interval_end=newest_allowed,
                    prefix_log=prefix_log,
                )

                # Find and create missing intervals
                await self._create_missing_audit_records(
                    db_session=db_session,
                    instance_name=instance_name,
                    metric_name=metric_name,
                    expected_intervals=expected_intervals,
                    existing_intervals=existing_audit_intervals,
                    prefix_log=prefix_log,
                )

            except Exception as e:
                self.logger.error(
                    f"{prefix_log}, Error creating missing metrics audits: {e}",
                    exc_info=True,
                )

    @staticmethod
    def _is_metric_backfill_enabled(metric_config: dict) -> bool:
        """Check if backfill is enabled for a metric configuration."""
        return metric_config.get("enabled", False) and metric_config.get(
            "backfill_enabled", False
        )

    @staticmethod
    def _calculate_metric_time_range(
        instance_db_model, metric_config: dict, now: datetime.datetime
    ) -> tuple[datetime.datetime, datetime.datetime] | None:
        """Calculate the valid time range for checking missing metrics."""
        max_days_lookback = metric_config.get("max_days_lookback", 30)

        # Calculate the valid time range for this instance and metric
        oldest_allowed = now - datetime.timedelta(days=max_days_lookback)

        _, newest_allowed = get_most_recent_metrics_time_interval(
            now, interval_minutes=metric_config.get("frequency", 5)
        )

        # Ensure we have a valid time range
        if oldest_allowed >= newest_allowed:
            return None

        return oldest_allowed, newest_allowed

    async def _get_existing_audit_intervals(
        self,
        *,
        db_session,
        instance_name: str,
        metric_name: str,
        interval_start: datetime.datetime,
        interval_end: datetime.datetime,
        prefix_log: str,
    ) -> dict[tuple[datetime.datetime, datetime.datetime], int]:
        """Get existing audit intervals with max attempt_count for an instance within the time range."""

        # flake8: noqa
        stmt = (
            select(
                MetricsAuditDbModel.interval_start,
                MetricsAuditDbModel.interval_end,
                func.max(MetricsAuditDbModel.attempt_count).label("max_attempt_count"),
            )
            .where(
                MetricsAuditDbModel.manager_instance == instance_name,
                MetricsAuditDbModel.metric_name == metric_name,
                MetricsAuditDbModel.interval_end >= interval_start,
                MetricsAuditDbModel.interval_start <= interval_end,
            )
            .group_by(MetricsAuditDbModel.interval_start, MetricsAuditDbModel.interval_end)
            .order_by(MetricsAuditDbModel.interval_start.desc())
        )
        result = await db_session.execute(stmt)
        existing_intervals_data = result.fetchall()

        # Convert to dict mapping interval tuple to max attempt count
        existing_intervals = {
            (row.interval_start, row.interval_end): row.max_attempt_count
            for row in existing_intervals_data
        }

        self.logger.info(
            f"{prefix_log}, Found existing {len(existing_intervals)} metrics audit intervals from {merge_and_format_intervals(list(existing_intervals.keys()))}"
        )
        return existing_intervals

    async def _create_missing_audit_records(
        self,
        *,
        db_session,
        instance_name: str,
        metric_name: str,
        expected_intervals: list[tuple[datetime.datetime, datetime.datetime]],
        existing_intervals: dict[tuple[datetime.datetime, datetime.datetime], int],
        prefix_log: str,
    ):
        """Create audit records for missing intervals or retry attempts."""
        audit_service = MetricsAuditDbService(db_session)

        # Get max_retry_attempts from config
        max_retry_attempts = self.metrics_configs.get(metric_name, {}).get(
            "max_retry_attempts", 3
        )

        missing_audits = []
        for interval_start, interval_end in expected_intervals:
            interval_key = (interval_start, interval_end)

            if interval_key not in existing_intervals:
                # No existing audit for this interval, create with attempt_count=1
                audit_pyd_model = MetricsAuditPydModel(
                    manager_instance=instance_name,
                    metric_name=metric_name,
                    interval_start=interval_start,
                    interval_end=interval_end,
                    collection_status=MetricsCollectionStatus.NOT_STARTED,
                    attempt_count=1,
                )
                missing_audits.append(audit_pyd_model)
            else:
                # Existing audit found, check if we need to create a retry attempt
                max_existing_attempt_count = existing_intervals[interval_key]

                # Only create retry if we haven't reached max attempts
                if max_existing_attempt_count < max_retry_attempts:
                    # Check if the latest attempt needs a retry (check status)
                    needs_retry = await self._check_if_interval_needs_retry(
                        db_session=db_session,
                        instance_name=instance_name,
                        metric_name=metric_name,
                        interval_start=interval_start,
                        interval_end=interval_end,
                        max_attempt_count=max_existing_attempt_count,
                    )

                    if needs_retry:
                        audit_pyd_model = MetricsAuditPydModel(
                            manager_instance=instance_name,
                            metric_name=metric_name,
                            interval_start=interval_start,
                            interval_end=interval_end,
                            collection_status=MetricsCollectionStatus.NOT_STARTED,
                            attempt_count=max_existing_attempt_count + 1,
                        )
                        missing_audits.append(audit_pyd_model)

        if missing_audits:
            await audit_service.create_audits(missing_audits)
            await db_session.commit()
            self.logger.info(
                f"{prefix_log}, Created {len(missing_audits)} missing metrics audits from {merge_and_format_intervals([(missing_audit.interval_start, missing_audit.interval_end) for missing_audit in missing_audits])}"
            )
        else:
            self.logger.info(f"{prefix_log}, No missing metrics audits to create")

    async def _check_if_interval_needs_retry(
        self,
        *,
        db_session,
        instance_name: str,
        metric_name: str,
        interval_start: datetime.datetime,
        interval_end: datetime.datetime,
        max_attempt_count: int,
    ) -> bool:
        """Check if an interval needs a retry based on the latest attempt status."""
        stmt = (
            select(MetricsAuditDbModel.collection_status)
            .where(
                MetricsAuditDbModel.manager_instance == instance_name,
                MetricsAuditDbModel.metric_name == metric_name,
                MetricsAuditDbModel.interval_start == interval_start,
                MetricsAuditDbModel.interval_end == interval_end,
                MetricsAuditDbModel.attempt_count == max_attempt_count,
            )
            .limit(1)
        )
        result = await db_session.execute(stmt)
        row = result.fetchone()

        if not row:
            return False

        latest_status = row.collection_status

        # Need retry if the latest attempt failed or has empty metrics
        return latest_status in [
            MetricsCollectionStatus.FAILED,
            MetricsCollectionStatus.METRICS_EMPTY,
        ]

    @staticmethod
    def attempts_exhausted(
        audit,
        max_retry_attempts: int,
    ) -> bool:
        """Determine if an audit record should be skipped."""
        # Skip if max retries exceeded
        if audit.attempt_count >= max_retry_attempts:
            return True

        return False

    @staticmethod
    def _generate_expected_intervals(
        interval_start: datetime.datetime,
        interval_end: datetime.datetime,
        interval_minutes: int,
    ) -> list[tuple[datetime.datetime, datetime.datetime]]:
        """Generate a list of expected time intervals between interval_start and interval_end."""
        intervals = []

        # Round start_time down to the nearest interval
        minutes = interval_start.minute
        rounded_minutes = (minutes // interval_minutes) * interval_minutes
        current = interval_start.replace(minute=rounded_minutes, second=0, microsecond=0)

        while current < interval_end:
            _interval_end = current + datetime.timedelta(minutes=interval_minutes)
            if _interval_end <= interval_end:  # Only include complete intervals
                intervals.append((current, _interval_end))
            current = _interval_end

        return intervals

    def shutdown(self):
        """Shutdown the backfill worker scheduler."""
        if hasattr(self, "scheduler") and self.scheduler.running:
            self.scheduler.shutdown()  # type: ignore
            self._is_running = False
            self.logger.info("MetricsAuditWorker scheduler shutdown complete")
