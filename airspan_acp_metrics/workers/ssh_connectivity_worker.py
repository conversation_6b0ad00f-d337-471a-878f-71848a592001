import logging

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from da_common.config import Config
from da_common.models import Status
from dal_pubsub.pubsub import PubSub

from airspan_acp_metrics.constants import CONNECT_TIMEOUT
from airspan_acp_metrics.database.connection import get_async_session_maker
from airspan_acp_metrics.database.services.instance_db_service import InstanceDbService
from airspan_acp_metrics.database.session_management import get_db_session
from airspan_acp_metrics.managers import InstanceReconciliationManager
from airspan_acp_metrics.managers import SSHConnectionManager
from airspan_acp_metrics.managers.alarm_manager import AlarmManager
from airspan_acp_metrics.managers.database_operations_manager import DatabaseOperationsManager
from airspan_acp_metrics.pyd_models.alarm_enums import AlarmType
from airspan_acp_metrics.pyd_models.instance_pyd_model import InstancePydModel


class SSHConnectivityWorker:
    """
    Worker that checks SSH connectivity to all instances and updates their
    metrics_collection_status accordingly. This ensures other workers only
    operate on instances with working SSH connections.
    """

    def __init__(self, config: Config):
        self.config = config
        self.async_session_maker = get_async_session_maker(self.config)
        self.scheduler = AsyncIOScheduler()
        self._is_running = False
        self.logger = logging.getLogger("airspan_acp_metrics.managers.ssh_connectivity_checker")

        # Initialize managers
        self.ssh_manager = SSHConnectionManager(config)
        self.db_ops = DatabaseOperationsManager()
        self.instance_reconciliation = InstanceReconciliationManager(
            config, self.async_session_maker
        )
        self.connect_timeout = CONNECT_TIMEOUT

        # Initialize PubSub for alarms
        self.pubsub = PubSub(config=config)
        self.alarm_topic = config.data["pubsub"].get("alarm", {}).get("topic", "nms-alarms")

    def _get_log_prefix(self, instance_name: str | None = None) -> str:
        """Generate standardized log prefix for tracking."""
        if instance_name:
            return f"SSHConnectivityChecker: {instance_name},"
        else:
            return "SSHConnectivityChecker:"

    def start(self):
        """Start the SSH connectivity checker scheduler."""
        if self._is_running:
            self.logger.info(f"{self._get_log_prefix()} Already running")
            return

        self.logger.info(f"{self._get_log_prefix()} Starting SSH connectivity checker")

        # Schedule SSH connectivity checks every minute
        self.scheduler.add_job(
            self._check_all_instances_connectivity,
            CronTrigger(minute="*", second=0),
            id="ssh_connectivity_checker",
            replace_existing=True,
            misfire_grace_time=30,
        )

        self.scheduler.start()
        self._is_running = True
        self.logger.info(f"{self._get_log_prefix()} Scheduler started")

    async def _check_all_instances_connectivity(self):
        """Check SSH connectivity for all instances and update their status."""
        prefix_log = self._get_log_prefix()
        self.logger.info(f"{prefix_log}, Starting SSH connectivity check for all instances")

        # reconcile instances before checking connectivity
        await self.instance_reconciliation.reconcile_airspan_acp_agent_instances(prefix_log)

        async with get_db_session(self.async_session_maker) as db_session:
            try:
                # Get all instances
                instance_db_service = InstanceDbService(db_session)
                all_instances = await instance_db_service.get_all_acp_instances()

                if not all_instances:
                    self.logger.info(f"{prefix_log}, No instances found")
                    return

                self.logger.info(
                    f"{prefix_log}, Checking connectivity for {len(all_instances)} instances"
                )

                # Check connectivity for each instance that has required fields (OK status)
                checked_count = 0
                status_changed_count = 0
                skipped_count = 0

                for instance_db_model in all_instances:
                    # Only check SSH connectivity for instances which
                    # have both host_ip_address and host_secret_id
                    if (
                        not instance_db_model.host_secret_id
                        or not instance_db_model.host_ip_address
                    ):
                        skipped_count += 1
                        continue

                    instance_pyd_model = InstancePydModel.model_validate(instance_db_model)
                    try:
                        status_changed = await self._check_instance_connectivity(
                            db_session, instance_pyd_model
                        )
                        if status_changed:
                            status_changed_count += 1
                        checked_count += 1

                    except Exception as e:
                        instance_log_prefix = self._get_log_prefix(
                            str(instance_pyd_model.manager_instance)
                        )
                        self.logger.error(
                            f"{instance_log_prefix} Error during connectivity check: {e}",
                            exc_info=True,
                        )

                # Commit all status updates
                await db_session.commit()

                self.logger.info(
                    f"{self._get_log_prefix()} Connectivity check completed - "
                    f"checked: {checked_count}, status_changed: {status_changed_count}, "
                    f"skipped: {skipped_count} (missing required fields)"
                )

            except Exception as e:
                self.logger.error(
                    f"{self._get_log_prefix()} Error in connectivity check cycle: {e}",
                    exc_info=True,
                )
                await db_session.rollback()

    async def _check_instance_connectivity(
        self, db_session, instance_pyd_model: InstancePydModel
    ) -> bool:
        """
        Check SSH connectivity for a single instance and update status if changed.

        Returns:
            bool: True if status was changed, False otherwise
        """
        instance_name = str(instance_pyd_model.manager_instance)
        prefix_log = self._get_log_prefix(instance_name)

        try:
            # Test connectivity and determine new status
            connectivity_report = await self._get_instance_connectivity_report(
                instance_pyd_model, prefix_log
            )

            # Check if status needs to be updated
            status_changed = self._should_update_status(instance_pyd_model, connectivity_report)

            if status_changed:
                # Update instance status in database
                await self._update_instance_status(
                    db_session, instance_name, connectivity_report, prefix_log
                )

                # Handle alarm raising/clearing based on connectivity status
                await self._handle_connectivity_alarms(
                    db_session, instance_pyd_model, connectivity_report, prefix_log
                )

                # Log status change
                self._log_status_change(
                    prefix_log,
                    connectivity_report,
                    instance_pyd_model.metrics_collection_status,
                )

                return True
            else:
                self.logger.debug(
                    f"{prefix_log}, Metrics Collection Status unchanged ({instance_pyd_model.metrics_collection_status})"
                )
                return False

        except Exception as e:
            # Handle unexpected errors during connectivity check
            return await self._handle_connectivity_check_error(
                db_session, instance_pyd_model, e, prefix_log
            )

    async def _get_instance_connectivity_report(
        self, instance_pyd_model: InstancePydModel, prefix_log: str
    ) -> dict[str, str | bool]:
        """
        Test SSH connectivity, for instance, and return the connectivity result.

        Returns:
            dict: Contains status, reason, and connection success flag
        """

        instance_name = str(instance_pyd_model.manager_instance)

        self.logger.debug(f"{prefix_log}, Testing SSH connectivity")

        connection, error_response = await self.ssh_manager.get_connection(
            instance_pyd_model, self.connect_timeout, prefix_log
        )

        if connection:
            # Connection successful - close immediately
            self.ssh_manager.close_connection(connection, instance_name, prefix_log)

            return {
                "status": Status.OK.value,
                "reason": "Ready for metrics collection",
                "connection_successful": True,
            }
        else:
            # Connection failed
            reason = error_response.get("reason", "SSH connection failed")
            self.logger.warning(f"{prefix_log}, SSH connectivity test failed: {reason}")

            return {
                "status": Status.ERROR.value,
                "reason": reason,
                "connection_successful": False,
            }

    def _should_update_status(
        self, instance_pyd_model: InstancePydModel, connectivity_result: dict[str, str | bool]
    ) -> bool:
        """
        Determine if the instance status should be updated based on connectivity result.

        Only update status if:
        1. SSH connectivity status has changed (OK <-> ERROR)
        2. The reason has changed

        Note: We only check SSH for instances that already have OK status
        (meaning they have required fields), so we're only dealing with
        SSH-related status changes here.

        Returns:
            bool: True if status should be updated, False otherwise
        """
        current_status = instance_pyd_model.metrics_collection_status
        current_reason = instance_pyd_model.reason

        new_status = str(connectivity_result["status"])
        new_reason = str(connectivity_result["reason"])

        status_changed = current_status != new_status or current_reason != new_reason
        return bool(status_changed)

    async def _update_instance_status(
        self,
        db_session,
        instance_name: str,
        connectivity_result: dict[str, str | bool],
        prefix_log: str,
    ) -> None:
        """Update instance status in the database."""
        await self.db_ops.update_instance_metrics_collection_status(
            db_session=db_session,
            manager_instance=instance_name,
            status=str(connectivity_result["status"]),
            error_reason=str(connectivity_result["reason"]),
            prefix_log=prefix_log,
        )

    async def _handle_connectivity_alarms(
        self,
        db_session,
        instance_pyd_model: InstancePydModel,
        connectivity_result: dict[str, str | bool],
        prefix_log: str,
    ) -> None:
        """Handle alarm raising/clearing based on connectivity status."""
        try:
            # Initialize alarm manager
            alarm_manager = AlarmManager(db_session, self.pubsub, self.alarm_topic)

            new_status = connectivity_result["status"]
            reason = connectivity_result["reason"]

            if new_status == Status.ERROR.value:
                # SSH connectivity failed - raise alarm
                await alarm_manager.raise_alarm(
                    alarm_type=AlarmType.SSH_CONNECTIVITY_FAILED,
                    name=f"SSH connectivity failed for {instance_pyd_model.manager_instance}",
                    source="ssh_connectivity_worker",
                    instance=instance_pyd_model,
                    description=f"SSH connectivity test failed: {reason}",
                    prefix_log=prefix_log,
                )
            elif new_status == Status.OK.value:
                # SSH connectivity restored - clear alarm
                await alarm_manager.clear_alarm(
                    alarm_type=AlarmType.SSH_CONNECTIVITY_FAILED,
                    instance=instance_pyd_model,
                    resolver="ssh_connectivity_worker",
                    prefix_log=prefix_log,
                )

        except Exception as e:
            self.logger.error(
                f"{prefix_log}, Error handling connectivity alarms: {e}", exc_info=True
            )

    def _log_status_change(
        self, prefix_log: str, connectivity_result: dict[str, str | bool], previous_status: str
    ) -> None:
        """Log status changes with appropriate log levels."""
        new_status = connectivity_result["status"]
        reason = connectivity_result["reason"]

        if new_status == Status.OK.value:
            self.logger.info(
                f"{prefix_log}, Metrics Collection Status changed from {previous_status} to OK - Instance is ready for metrics collection"
            )
        else:
            self.logger.warning(
                f"{prefix_log}, Metrics Collection Status changed from {previous_status} to ERROR: {reason}"
            )

    async def _handle_connectivity_check_error(
        self,
        db_session,
        instance_pyd_model: InstancePydModel,
        error: Exception,
        prefix_log: str,
    ) -> bool:
        """
        Handle unexpected errors during connectivity check.

        Returns:
            bool: True if status was updated, False otherwise
        """
        instance_name = instance_pyd_model.manager_instance
        error_reason = f"Connectivity check failed: {error!s}"

        self.logger.error(f"{prefix_log}, {error_reason}", exc_info=True)

        # Check if this is a new error condition
        current_status = instance_pyd_model.metrics_collection_status
        current_reason = instance_pyd_model.reason

        if current_status != Status.ERROR.value or current_reason != error_reason:
            try:
                await self.db_ops.update_instance_metrics_collection_status(
                    db_session=db_session,
                    manager_instance=instance_name,
                    status=Status.ERROR.value,
                    error_reason=error_reason,
                    prefix_log=prefix_log,
                )

                # Raise alarm for connectivity check error
                try:
                    instance_pyd_model = InstancePydModel.model_validate(instance_pyd_model)
                    alarm_manager = AlarmManager(db_session, self.pubsub, self.alarm_topic)

                    await alarm_manager.raise_alarm(
                        alarm_type=AlarmType.SSH_CONNECTIVITY_FAILED,
                        name=f"SSH connectivity check failed for {instance_pyd_model.manager_instance}",
                        source="ssh_connectivity_worker",
                        instance=instance_pyd_model,
                        description=error_reason,
                        prefix_log=prefix_log,
                    )
                except Exception as alarm_error:
                    self.logger.error(
                        f"{prefix_log}, Error raising alarm for connectivity check error: {alarm_error}"
                    )

                self.logger.warning(
                    f"{prefix_log}, Metrics Collection Status changed from {current_status} to ERROR: {error_reason}"
                )
                return True
            except Exception as update_error:
                self.logger.error(
                    f"{prefix_log}, Failed to update error status: {update_error}"
                )

        return False

    def shutdown(self):
        """Shutdown the SSH connectivity checker scheduler."""
        if hasattr(self, "scheduler") and self.scheduler.running:
            self.scheduler.shutdown()
            self._is_running = False
            self.logger.info(f"{self._get_log_prefix()}, Scheduler shutdown complete")
