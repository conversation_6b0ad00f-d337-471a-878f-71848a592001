import asyncio
import datetime
import logging
import time
from collections.abc import Sequence

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from asyncssh import SSHClientConnection
from da_common.config import Config
from da_common.models import Status
from sqlalchemy.ext.asyncio import AsyncSession

from airspan_acp_metrics.constants import CONNECT_TIMEOUT
from airspan_acp_metrics.database.connection import get_async_session_maker
from airspan_acp_metrics.database.models.instance_db_model import InstanceDbModel
from airspan_acp_metrics.database.models.metrics_audits_db_model import MetricsAuditDbModel
from airspan_acp_metrics.database.services.instance_db_service import InstanceDbService
from airspan_acp_metrics.database.services.metric_audit_db_service import MetricsAuditDbService
from airspan_acp_metrics.database.session_management import get_db_session
from airspan_acp_metrics.managers import DatabaseOperationsManager
from airspan_acp_metrics.managers import SSHConnectionManager
from airspan_acp_metrics.managers.metrics_collector_manager import MetricsCollector
from airspan_acp_metrics.pyd_models.instance_pyd_model import InstancePydModel
from airspan_acp_metrics.pyd_models.metrics_audit_pyd_model import UpdateMetricsAuditPydModel
from airspan_acp_metrics.pyd_models.utils import MetricsCollectionStatus
from airspan_acp_metrics.utils import get_most_recent_metrics_time_interval
from airspan_acp_metrics.utils import merge_and_format_intervals


class MetricsCollectionWorker:
    """
    Worker that schedules metrics collection tasks.
    Runs every minute to identify gaps in a metrics collection and fills them.
    """

    def __init__(self, config: Config):
        self.config = config
        self.async_session_maker = get_async_session_maker(self.config)
        self.scheduler = AsyncIOScheduler()
        self._is_running = False
        self.logger = logging.getLogger("airspan_acp_metrics.workers.metrics_collection_worker")

        # Initialize metrics collector for reusing collection logic
        self.metrics_collector = MetricsCollector(config)
        self.ssh_manager = SSHConnectionManager(config)
        self.db_ops = DatabaseOperationsManager()

        # Get metrics configurations with backfill settings
        self.metrics_configs = {}
        for key, value in self.config.data.get("metrics", {}).items():
            if isinstance(value, dict) and value.get("enabled", False):
                # Create a new dict with proper types - use Any to avoid type checker issues
                config_with_defaults: dict = {}
                config_with_defaults.update(value)
                config_with_defaults.update(
                    {
                        "backfill_enabled": bool(value.get("backfill_enabled", True)),
                        "max_days_lookback": int(value.get("max_days_lookback", 30)),
                        "max_retry_attempts": int(value.get("max_retry_attempts", 3)),
                    }
                )
                self.metrics_configs[key] = config_with_defaults

        # Check if any metric has backfill enabled
        self.any_backfill_enabled = any(
            config.get("backfill_enabled", False) for config in self.metrics_configs.values()
        )
        # TODO: Make this configurable
        self.max_concurrent_backfills = 10
        self.worker_name = "MetricsCollectionWorker"

    def _get_log_prefix(
        self,
        *,
        instance_name: str | None = None,
        metric_name: str | None = None,
        intervals: list[tuple[datetime.datetime, datetime.datetime]] | None = None,
        attempt_count: int | None = None,
    ) -> str:
        """Generate standardized log prefix for tracking."""

        prefix_log = self.worker_name
        if instance_name:
            prefix_log += f" {instance_name}"
        if metric_name:
            prefix_log += f" {metric_name}"
        if intervals:
            prefix_log += f" {merge_and_format_intervals(intervals)}"
        if attempt_count:
            prefix_log += f" attempt_count: {attempt_count}"
        return prefix_log

    def start(self):
        """Start the backfill worker scheduler."""
        prefix_log = self._get_log_prefix()

        # Schedule the backfill processor to run every minute
        self.scheduler.add_job(
            self._execute_metrics_collection_cycle,
            CronTrigger(minute="*/1", second=30),  # Run every 1 minute
            id="metrics_collection_worker",
            replace_existing=True,
            misfire_grace_time=30,
            max_instances=10,
        )

        self.scheduler.start()
        self.logger.info(f"{prefix_log}, Starting")

    @staticmethod
    def _is_metric_backfill_enabled(metric_config: dict) -> bool:
        """Check if backfill is enabled for a metric configuration."""
        return metric_config.get("enabled", False) and metric_config.get(
            "backfill_enabled", False
        )

    @staticmethod
    def _calculate_metric_time_range(
        instance_db_model, metric_config: dict, now: datetime.datetime
    ) -> tuple[datetime.datetime, datetime.datetime] | None:
        """Calculate the valid time range for checking missing metrics."""
        max_days_lookback = metric_config.get("max_days_lookback", 30)

        # Calculate the valid time range for this instance and metric
        instance_created = instance_db_model.created_at
        lookback_time = now - datetime.timedelta(days=max_days_lookback)
        oldest_allowed = instance_created if instance_created > lookback_time else lookback_time

        _, newest_allowed = get_most_recent_metrics_time_interval(
            now, interval_minutes=metric_config.get("frequency", 5)
        )

        # Ensure we have a valid time range
        if oldest_allowed >= newest_allowed:
            return None

        return oldest_allowed, newest_allowed

    async def _execute_metrics_collection_cycle(self):
        """
        Execute a complete metrics collection cycle.
        Processes all enabled metric types across all SSH-enabled instances.
        """
        prefix_log = self._get_log_prefix()

        if self._is_running or not self.any_backfill_enabled:
            self.logger.info(
                f"{prefix_log}, Already running: {self._is_running}, any_backfill_enabled: {self.any_backfill_enabled}"
            )
            return

        self.logger.info(f"{prefix_log}, Started")

        async with get_db_session(self.async_session_maker) as db_session:
            ssh_enabled_instances = await self._get_instances_with_ssh_connectivity(db_session)
            if not ssh_enabled_instances:
                return

            total_metrics_collected = await self._process_all_metric_types(
                db_session, ssh_enabled_instances
            )

            await db_session.commit()
            self.logger.info(
                f"{prefix_log}, Completed processing metrics collection for {total_metrics_collected} items"
            )

    async def _get_instances_with_ssh_connectivity(
        self, db_session: AsyncSession
    ) -> Sequence[InstanceDbModel]:
        """Get all instances that have SSH connectivity enabled."""
        prefix_log = self._get_log_prefix()

        instance_db_service = InstanceDbService(db_session)
        ssh_enabled_instances = await instance_db_service.get_all_acp_instances(
            metrics_collection_status=Status.OK
        )

        if not ssh_enabled_instances:
            self.logger.info(
                f"{prefix_log}, No instances with ssh connectivity found, skipping metrics collection"
            )
            return []

        self.logger.info(
            f"{prefix_log}, Found {len(ssh_enabled_instances)} instances having ssh connectivity"
        )
        return ssh_enabled_instances

    async def _process_all_metric_types(
        self, db_session: AsyncSession, ssh_enabled_instances: Sequence[InstanceDbModel]
    ) -> int:
        """Process metrics collection for all enabled metric types."""
        total_metrics_collected = 0

        for metric_name, metric_config in self.metrics_configs.items():
            if not self._is_metric_backfill_enabled(metric_config):
                prefix_log = self._get_log_prefix(metric_name=metric_name)
                self.logger.info(
                    f"{prefix_log}, Backfill Metrics Collection is not enabled, skipping processing metrics for this metric type"
                )
                continue

            try:
                metrics_collected_count = await self._collect_metrics_for_metric_type(
                    db_session=db_session,
                    metric_name=metric_name,
                    metric_config=metric_config,
                    ssh_enabled_instances=ssh_enabled_instances,
                )
                total_metrics_collected += metrics_collected_count
            except Exception as e:
                prefix_log = self._get_log_prefix(metric_name=metric_name)
                self.logger.error(
                    f"{prefix_log}, Error processing metrics collection: {e}",
                    exc_info=True,
                )

        return total_metrics_collected

    async def _collect_metrics_for_metric_type(
        self,
        *,
        db_session: AsyncSession,
        metric_name: str,
        metric_config: dict,
        ssh_enabled_instances: Sequence[InstanceDbModel],
    ) -> int:
        """Collect metrics for a specific metric type across all instances."""
        total_metrics_collected = 0

        for instance_db_model in ssh_enabled_instances:
            instance_pyd_model = InstancePydModel.model_validate(instance_db_model)
            instance_name = str(instance_db_model.manager_instance)
            prefix_log = self._get_log_prefix(
                metric_name=metric_name,
                instance_name=instance_name,
            )

            try:
                connection, response = await self.ssh_manager.get_connection(
                    instance_pyd_model, CONNECT_TIMEOUT, prefix_log
                )
            except Exception as e:
                self.logger.error(
                    f"{prefix_log}, Error establishing connection, error: {e}",
                    exc_info=True,
                )
                continue

            self.logger.info(f"{prefix_log}, SSH connection successful")

            try:
                instance_metrics_collected = await self._collect_metrics_for_instance(
                    db_session=db_session,
                    metric_config=metric_config,
                    instance_db_model=instance_db_model,
                    connection=connection,
                )
                total_metrics_collected += instance_metrics_collected
            except Exception as e:
                self.logger.error(
                    f"{prefix_log}, Error processing metrics collection, error: {e}",
                    exc_info=True,
                )
            finally:
                self.ssh_manager.close_connection(connection, instance_name, prefix_log)

        return total_metrics_collected

    async def _preprocess_audits(
        self,
        *,
        db_session: AsyncSession,
        instance_name: str,
        pending_audits: list[MetricsAuditDbModel],
        metric_config: dict,
    ) -> list[MetricsAuditDbModel]:
        """Mark audit records as RUNNING status and update last attempt timestamp."""
        now = datetime.datetime.now(datetime.UTC)

        audit_service = MetricsAuditDbService(db_session)
        eligible_audits = []
        for index, audit_record in enumerate(pending_audits):
            prefix_log = self._get_log_prefix(
                instance_name=instance_name,
                metric_name=metric_config["metric_name"],
                intervals=[
                    (
                        audit_record.interval_start.astimezone(datetime.UTC),
                        audit_record.interval_end.astimezone(datetime.UTC),
                    )
                ],
                attempt_count=audit_record.attempt_count,
            )

            if self._should_skip_recent_audit(
                audit_record, now, index, metric_config.get("recent_buffer_minutes", 1)
            ):
                self.logger.info(
                    f"{prefix_log}, Skipping metrics collection as it is not yet generated on the host machine"
                )
                continue

            max_retry_attempts = metric_config.get("max_retry_attempts", 3)
            if self._should_skip_exhausted_retries(audit_record, max_retry_attempts):
                # it will occur only if after creating metrics_audit the max_retry_attempts is decreased
                await self._handle_exhausted_retry_attempts(
                    audit_service=audit_service,
                    audit_record=audit_record,
                    max_retry_attempts=max_retry_attempts,
                    current_time=now,
                    prefix_log=prefix_log,
                )
                continue

            audit_record.collection_status = MetricsCollectionStatus.RUNNING
            audit_record.last_attempt = now
            self.logger.info(
                f"{prefix_log}, Updating metrics audit status to {MetricsCollectionStatus.RUNNING} and also last_attempt to current time"
            )
            eligible_audits.append(audit_record)

        await db_session.commit()

        # Refresh the audit records
        for audit_record in eligible_audits:
            await db_session.refresh(audit_record)

        return eligible_audits

    # TODO: below method is copied from druid-agent, we might use it
    async def _prepare_audits_for_processing(
        self,
        *,
        db_session: AsyncSession,
        metrics_audit_db_models: list[MetricsAuditDbModel],
        instance_name: str,
    ) -> list[MetricsAuditDbModel] | None:
        """Filter and prepare audit records for processing.

        This method now includes proper race condition prevention:
        1. Increments attempt_count when setting status to RUNNING
        2. Filters out audits that exceed retry limits or are too recent
        3. Prevents processing of already-running audits
        """
        now = datetime.datetime.now(datetime.UTC)
        audit_service = MetricsAuditDbService(db_session)
        valid_audits = []

        for metrics_audit_db_model in metrics_audit_db_models:
            _prefix_log = self._get_log_prefix(
                instance_name=instance_name,
                intervals=[
                    (
                        metrics_audit_db_model.interval_start,
                        metrics_audit_db_model.interval_end,
                    )
                ],
                attempt_count=metrics_audit_db_model.attempt_count,
            )

            # Skip if audit is already running (race condition check)
            if metrics_audit_db_model.collection_status == MetricsCollectionStatus.RUNNING:
                self.logger.info(f"{_prefix_log}: Skipping, audit is already RUNNING")
                continue

            # Skip if within buffer time
            if now - metrics_audit_db_model.interval_end < datetime.timedelta(
                minutes=self.recent_buffer_minutes
            ):
                self.logger.info(
                    f"{_prefix_log}: Skipping, audit record havent passed buffer minutes {self.recent_buffer_minutes}"
                )
                continue

            # Check retry limit
            if metrics_audit_db_model.attempt_count > self.max_retry_attempts:
                self.logger.info(
                    f"{_prefix_log}: Skipping, audit record exceeded max retry attempts ({metrics_audit_db_model.attempt_count} > {self.max_retry_attempts})"
                )
                continue

            # Set to RUNNING and increment attempt count
            update_metrics_audit_pyd_model = UpdateMetricsAuditPydModel(
                collection_status=MetricsCollectionStatus.RUNNING,
                last_attempt=now,
                attempt_count=metrics_audit_db_model.attempt_count
                + 1,  # Increment attempt count
            )

            await audit_service.update_audit(
                metrics_audit_db_model,
                update_metrics_audit_pyd_model,
                _prefix_log,
                flush=False,
            )

            valid_audits.append(metrics_audit_db_model)

        # Commit all changes at once
        await db_session.commit()

        # Refresh all valid audits
        for metrics_audit_db_model in valid_audits:
            await db_session.refresh(metrics_audit_db_model)

            _prefix_log = self._get_log_prefix(
                instance_name=instance_name,
                intervals=[
                    (
                        metrics_audit_db_model.interval_start,
                        metrics_audit_db_model.interval_end,
                    )
                ],
                attempt_count=metrics_audit_db_model.attempt_count,
            )
            self.logger.info(
                f"{_prefix_log}: updated audits with values collection_status: {metrics_audit_db_model.collection_status} {metrics_audit_db_model.error_message if metrics_audit_db_model.error_message else ''} "
            )

        return valid_audits

    def _should_skip_recent_audit(
        self,
        audit_record: MetricsAuditDbModel,
        current_time: datetime.datetime,
        audit_index: int,
        recent_buffer_minutes: int = 1,
    ) -> bool:
        """Check if audit should be skipped because it's too recent (within 1 minute as a default)."""
        return audit_index == 0 and current_time <= audit_record.interval_end.astimezone(
            datetime.UTC
        ) + datetime.timedelta(minutes=recent_buffer_minutes)

    async def _collect_metrics_for_instance(
        self,
        *,
        db_session: AsyncSession,
        metric_config: dict,
        instance_db_model: InstanceDbModel,
        connection: SSHClientConnection,
    ) -> int:
        """Collect metrics for a specific instance and metric type."""
        instance_pyd_model = InstancePydModel.model_validate(instance_db_model)
        instance_name = str(instance_pyd_model.manager_instance)
        audit_service = MetricsAuditDbService(db_session)

        max_retry_attempts = metric_config.get("max_retry_attempts", 3)

        # Get audit records that need metrics collection for this instance
        audits_to_process = await audit_service.get_instance_audit_metrics_for_processing(
            instance_name, self.max_concurrent_backfills, max_retry_attempts
        )
        if not audits_to_process:
            return 0

        audits_to_process = await self._preprocess_audits(
            db_session=db_session,
            instance_name=instance_name,
            pending_audits=audits_to_process,
            metric_config=metric_config,
        )
        if not audits_to_process:
            return 0

        return await self._execute_metrics_collection_tasks(
            instance_pyd_model=instance_pyd_model,
            connection=connection,
            metric_config=metric_config,
            audits_to_process=audits_to_process,
        )

    async def _execute_metrics_collection_tasks(
        self,
        *,
        instance_pyd_model: InstancePydModel,
        connection: SSHClientConnection,
        metric_config: dict,
        audits_to_process: list[MetricsAuditDbModel],
    ) -> int:
        """Execute metrics collection tasks for all audit records."""
        instance_name = str(instance_pyd_model.manager_instance)
        prefix_log = self._get_log_prefix(
            instance_name=instance_name,
            metric_name=metric_config["metric_name"],
            intervals=[
                (
                    audit.interval_start.astimezone(datetime.UTC),
                    audit.interval_end.astimezone(datetime.UTC),
                )
                for audit in audits_to_process
            ],
        )

        self.logger.info(
            f"{prefix_log}, Updated {len(audits_to_process)} metrics audits to RUNNING status"
        )

        tasks = []
        start_time = time.perf_counter()
        metrics_collected_count = 0

        for audit_record in audits_to_process:
            audit_prefix_log = self._get_log_prefix(
                instance_name=instance_name,
                metric_name=metric_config["metric_name"],
                intervals=[
                    (
                        audit_record.interval_start.astimezone(datetime.UTC),
                        audit_record.interval_end.astimezone(datetime.UTC),
                    )
                ],
                attempt_count=audit_record.attempt_count,
            )

            try:
                task = self.metrics_collector.collect_instance_metrics_task(
                    instance_pyd_model=instance_pyd_model,
                    connection=connection,
                    metric_config=metric_config,
                    metrics_audit_db_model=audit_record,
                    interval_start=audit_record.interval_start.astimezone(datetime.UTC),
                    interval_end=audit_record.interval_end.astimezone(datetime.UTC),
                    prefix_log=prefix_log,
                )
                tasks.append(task)
                metrics_collected_count += 1
            except Exception as e:
                self.logger.error(
                    f"{audit_prefix_log}, Error creating metrics collection task: {e}",
                    exc_info=True,
                )

        await asyncio.gather(*tasks)
        self.logger.info(
            f"{prefix_log}, Processed {metrics_collected_count} metrics collection in {round(time.perf_counter() - start_time, 2)} seconds"
        )

        self.ssh_manager.close_connection(
            connection,
            instance_pyd_model.manager_instance,
            prefix_log,
        )

        return metrics_collected_count

    def _should_skip_exhausted_retries(
        self, audit_record: MetricsAuditDbModel, max_retry_attempts: int
    ) -> bool:
        """Check if audit should be skipped due to exhausted retry attempts."""
        return self.attempts_exhausted(audit_record, max_retry_attempts)

    async def _handle_exhausted_retry_attempts(
        self,
        audit_service: MetricsAuditDbService,
        audit_record: MetricsAuditDbModel,
        max_retry_attempts: int,
        current_time: datetime.datetime,
        prefix_log: str,
    ):
        """Handle audit records that have exhausted their retry attempts."""
        # Append "max_limit reached" to existing reason or create new one
        existing_reason = audit_record.reason or ""
        new_reason = "max_limit reached"
        if existing_reason:
            new_reason += f"; {existing_reason}"
        else:
            new_reason += f": Exceeded maximum retry attempts ({max_retry_attempts})"

        update_audit_metrics_pyd_model = UpdateMetricsAuditPydModel(
            collection_status=MetricsCollectionStatus.SKIPPED,
            reason=new_reason,
            updated_at=current_time,
        )

        await audit_service.update_audit(
            audit_db_model=audit_record,
            update_metrics_audit_pyd_model=update_audit_metrics_pyd_model,
            prefix_log=prefix_log,
        )
        self.logger.info(f"{prefix_log}, Skipped metrics collection - max retries exceeded")

    @staticmethod
    def attempts_exhausted(
        audit,
        max_retry_attempts: int,
    ) -> bool:
        """Determine if an audit record should be skipped."""
        # Skip if max retries exceeded
        if audit.attempt_count > max_retry_attempts:
            return True

        return False

    def shutdown(self):
        """Shutdown the backfill worker scheduler."""
        if hasattr(self, "scheduler") and self.scheduler.running:
            self.scheduler.shutdown()  # type: ignore
            self._is_running = False
            self.logger.info("MetricsCollectionWorker scheduler shutdown complete")
