import logging

from aiocache import <PERSON><PERSON><PERSON>ory<PERSON><PERSON>
from aiocache import cached
from da_common.secrets import get_secret_data as get_gcp_secret_data
from google.api_core.exceptions import GoogleAPIError
from google.api_core.exceptions import NotFound
from google.api_core.exceptions import PermissionDenied


# Cache configuration and key builder for credentials
def _credentials_key_builder(*args, **kwargs):
    return args[2]


# Custom cache class with logging
class LoggingSimpleMemoryCache(SimpleMemoryCache):
    async def get(self, key, *args, **kwargs):
        value = await super().get(key, *args, **kwargs)
        logger = logging.getLogger("airspan_acp_metrics.client.cache")
        if value is not None:
            logger.info(f"Instance: [ {key} ], Credentials Cache HIT")
        else:
            logger.info(f"Instance: [ {key} ], Credentials Cache MISS")
        return value

    async def set(self, key, value, *args, **kwargs):  # flake8: noqa
        logger = logging.getLogger("airspan_acp_metrics.client.cache")
        logger.info(f"Instance: [ {key} ], Credentials Storing in cache key=[ {key} ]")
        return await super().set(key, value, *args, **kwargs)


# Function to clear credentials from a cache
async def clear_credentials_cache(
    project_id: str, secret_id: str, manager_instance: str
) -> bool:
    """Clear credentials for a specific project and secret from the cache.

    Args:
        project_id: Google Cloud project ID
        secret_id: Secret ID to clear from cache
        manager_instance: Instance name for logging purposes

    Returns:
        True if the key was in the cache and was deleted, False otherwise
    """
    logger = logging.getLogger("airspan_acp_metrics.client.clear_credentials_cache")
    cache = LoggingSimpleMemoryCache()
    key = _credentials_key_builder(get_credentials, project_id, secret_id, manager_instance)

    try:
        # Check if the key exists before trying to delete
        exists = await cache.exists(key)
        if exists:
            await cache.delete(key)
            logger.info(f"Instance: [ {manager_instance} ], Cleared credentials cache")
            return True
        else:
            logger.info(f"Instance: [ {manager_instance} ], No cached credentials found")
            return False
    except Exception as e:
        logger.error(f"Instance: [ {manager_instance} ], Error clearing credentials cache: {e}")
        return False


@cached(
    ttl=86400,
    cache=LoggingSimpleMemoryCache,
    key_builder=_credentials_key_builder,
)
async def get_credentials(
    project_id: str, secret_id: str, manager_instance: str
) -> dict[str, str]:
    """Get credentials from a secret manager.

    Args:
        project_id: Google Cloud project ID
        secret_id: Secret ID to retrieve
        manager_instance: Instance name for logging purposes

    Returns:
        Dictionary with username and password

    Raises:
        GoogleAPIError: If there's an error accessing the secret
        ValueError: If the secret format is invalid
    """
    logger = logging.getLogger("airspan_acp_metrics.client.get_credentials")
    logger.info(
        f"Instance: [ {manager_instance} ], Getting credentials from secret manager for key: {secret_id}"
    )

    try:
        secret_data = await get_gcp_secret_data(project_id=project_id, secret_id=secret_id)

        # Validate secret format
        if not isinstance(secret_data, dict) or len(secret_data) != 2:
            raise ValueError("Invalid credentials format")

        logger.info(
            f"Instance: [ {manager_instance} ], Successfully retrieved credentials from secret manager for key: {secret_id}"
        )
        return secret_data

    except NotFound as e:
        logger.error(f"Instance: [ {manager_instance} ], Secret not found: {e}")
        raise GoogleAPIError(e) from e
    except PermissionDenied as e:
        logger.error(f"Instance: [ {manager_instance} ], Permission denied: {e}")
        raise GoogleAPIError(f"Permission denied: {e}") from e
    except Exception as e:
        logger.error(
            f"Instance: [ {manager_instance} ], unknown error while accessing credentials: {e}"
        )
        raise GoogleAPIError(f"Error accessing credentials: {e}") from e
