[tool:pytest]
minversion = 6.0
addopts =
    -ra
    --strict-markers
    --strict-config
    --tb=short
    --cov=airspan_acp_metrics.metrics_collector
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-fail-under=70
testpaths = airspan_acp_metrics/test
python_files = test_*.py
python_classes = Test*
python_functions = test_*
asyncio_mode = auto
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
