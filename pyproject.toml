# For possible options see https://peps.python.org/pep-0621/

[project]
name = "airspan_acp_metrics"
version = "0.3.0"
description = "airspan-acp-metrics"
readme = "README.md"
requires-python = ">=3.11"
license = { text = "Proprietary" }
keywords = []
authors = [{ name = "<PERSON><PERSON><PERSON>" }]
classifiers = ["Programming Language :: Python"]
dependencies = [
    "apischema >= 0.17.5",
    "buildpg >= 0.4",
    "httpx >= 0.23.0",
    "hypercorn >= 0.14.3",
    "importlib-resources>=6.5.2",
    "starlette >= 0.21.0",
    "tomli >= 2.0.0; python_version < '3.11'",
]

[project.optional-dependencies]
deploy = [
    "alembic >= 1.15.2",
    "aiocache >= 0.12.3",
    "apscheduler >= 3.11.0",
    "apischema >= 0.17.5",
    "asyncpg >= 0.27.0",
    "asyncssh >= 2.21.0",
    "buildpg >= 0.4",
    "da-common >= 1.3.8",
    "dal-pubsub >= 1.1.1",
    "google-api-core == 2.11.1",
    "google-auth == 2.22.0",
    "google-cloud-pubsub == 2.18.4",
    "google-cloud-secret-manager == 2.16.4",
    "google-cloud-storage >= 2.11.0",
    "googleapis-common-protos == 1.60.0",
    "greenlet >= 3.2.1",
    "grpc-google-iam-v1 == 0.12.6",
    "grpcio == 1.56.2",
    "grpcio-status == 1.56.2",
    "httpx >= 0.23.0",
    "hypercorn >= 0.14.3",
    "metrics-collector >= 1.35.6",
    "protobuf == 4.21.12",
    "psycopg2-binary >= 2.9.9",              # For synchronous database operations
    "sentry-sdk[fastapi]>=2.32.0",
    "sqlalchemy >= 2.0.40",
    "starlette >= 0.21.0",
    "tomli >= 2.2.1",
    "uvicorn >= 0.34.3",

]

dev = [
    "uv>=0.7.10",
    "pre-commit>=3.0.4",
    "black>=22.8.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "flake8-bugbear>=23.1.20",
    "flake8-comprehensions>=3.10.1",
    "flake8-simplify>=0.19.3",
    "pytest-xdist>=3.3.1",
    "ruff>=0.11.12",
]

test = [
    "pytest>=7.2.1",
    "pytest-asyncio>=0.21.0",
    "pytest-httpx>=0.35.0",
    "coverage",
    "greenlet",
    "factory-boy>=3.3.3",
]

audit = ["pip-licenses >= 3.5.5"]
doc = ["sphinx >= 5.3.0", "sphinx-sizzle-theme >= 0.1.1"]


[project.scripts]

[build-system]
requires = ["hatchling >= 1.27.0", "ruff >= 0.9.1", "setuptools", "wheel"]

build-backend = "hatchling.build"

[tool.hatch.build]
include = ["airspan_acp_metrics/*"]

[tool.ruff]
line-length = 96
src = ["airspan_acp_metrics"]
# Enable specific rules but exclude flake8 style rules (E, W)
# F = Pyflakes, I = isort, UP = pyupgrade, B = flake8-bugbear
lint.select = ["F", "I", "UP", "B", "C4", "PIE", "T20", "RUF"]
# Ignore specific rules
lint.ignore = [
    "RUF012", # Mutable class attributes should be annotated with `typing.ClassVar`
]
# Allow autofix for all enabled rules
lint.fixable = ["ALL"]
# Allow unused variables when prefixed with _
lint.dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"


[tool.ruff.lint.isort]
known-first-party = ["airspan_acp_metrics"]
no-lines-before = ["local-folder"]
lines-after-imports = 2
lines-between-types = 0
force-single-line = true
single-line-exclusions = []                 # Empty list = no exclusions
combine-as-imports = false


# Section order (ruff uses different names)
section-order = [
    "future",
    "standard-library",
    "third-party",
    "first-party",
    "local-folder",
]

# Skip files (convert your skip_glob)
[tool.ruff.lint.per-file-ignores]
"airspan_acp_metrics/__init__.py" = ["I"]
"airspan_acp_metrics/cfg/__init__.py" = ["I"]
"airspan_acp_metrics/schemas/__init__.py" = ["I"]
"airspan_acp_metrics/test/__init__.py" = ["I"]


[tool.pytest.ini_options]
asyncio_default_fixture_loop_scope = "function"
