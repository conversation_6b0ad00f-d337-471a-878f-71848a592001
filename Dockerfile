# Multi-stage build for optimized Python microservice
# Using the lightest Python image with uv for fast dependency management

FROM ghcr.io/astral-sh/uv:python3.11-alpine AS builder

# Install build dependencies for native packages (grpcio, cryptography, etc.)
RUN apk add --no-cache \
    gcc \
    g++ \
    musl-dev \
    libffi-dev \
    openssl-dev \
    cargo \
    rust \
    make \
    cmake \
    linux-headers \
    postgresql-dev \
    && rm -rf /var/cache/apk/*

# Set environment variables for uv and building
ENV UV_COMPILE_BYTECODE=1 \
    UV_LINK_MODE=copy \
    UV_PYTHON_DOWNLOADS=never \
    GRPC_PYTHON_BUILD_SYSTEM_OPENSSL=1 \
    GRPC_PYTHON_BUILD_SYSTEM_ZLIB=1

# Create app directory
WORKDIR /app

# Copy source code first
COPY . .

# Install dependencies directly from the copied project
RUN pip install --no-cache-dir -e .[deploy,test]

# Production stage - minimal runtime image
FROM python:3.11-alpine AS runtime

# Install runtime dependencies (only what's needed at runtime)
RUN apk add --no-cache \
    bash \
    libpq \
    libffi \
    openssl \
    && rm -rf /var/cache/apk/*

# Create non-root user
ARG user=service
RUN adduser -h /home/<USER>"" -s /bin/bash $user

# Set working directory
WORKDIR /home/<USER>/app

# Copy virtual environment from builder
COPY --from=builder --chown=$user:$user /app/.venv /home/<USER>/app/.venv

# Copy application code
COPY --chown=$user:$user . .

# Switch to non-root user
USER $user

# Add virtual environment to PATH
ENV PATH="/home/<USER>/app/.venv/bin:$PATH"

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:8080/health')" || exit 1

# Entry point
ENTRYPOINT ["python", "airspan_acp_metrics/main.py", "--config", "airspan_acp_metrics/cfg/starter.toml"]
