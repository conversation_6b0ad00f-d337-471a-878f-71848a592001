#!/bin/bash

# Ensure pre-commit is installed
if [ ! -f .venv/bin/pre-commit ]; then
    echo "Installing pre-commit..."
    .venv/bin/pip install pre-commit
fi

# Install pre-commit hooks
echo "Installing pre-commit hooks..."
.venv/bin/pre-commit install

# Install prepare-commit-msg hook
echo "Installing prepare-commit-msg hook..."
.venv/bin/pre-commit install --hook-type prepare-commit-msg

echo "Pre-commit hooks installed successfully!"
echo "You can run them manually with: .venv/bin/pre-commit run --all-files"
echo "The prepare-commit-msg hook will automatically add ticket numbers to your commit messages."
