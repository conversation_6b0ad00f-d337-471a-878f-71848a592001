# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: trailing-whitespace
        stages: [pre-commit]

      - id: end-of-file-fixer
        stages: [pre-commit]

      - id: detect-private-key
        stages: [pre-commit]

  # Ruff for fast linting (imports, pyflakes, etc.) - excluding style checks
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.11.13
    hooks:
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]
      - id: ruff-format

  # flake8 for style checks and additional linting
  - repo: https://github.com/pycqa/flake8
    rev: 7.2.0
    hooks:
      - id: flake8
        args: [--config=.flake8]
        additional_dependencies: [
          flake8-bugbear,
          flake8-comprehensions,
          flake8-simplify,
          flake8-expression-complexity,
          flake8-cognitive-complexity,
        ]

  - repo: local
    hooks:
      - id: prepare-commit-msg
        name: Add ticket number from branch name
        entry: hooks/prepare-commit-msg
        language: script
        stages: [prepare-commit-msg]
