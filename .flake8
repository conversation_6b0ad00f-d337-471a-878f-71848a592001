[flake8]
max-line-length = 96
max-cognitive-complexity = 15

# Focus on style and additional checks since <PERSON><PERSON> handles imports/pyflakes
extend-ignore = E203,E266,E501,W503,W504

# Enable flake8-bugbear, comprehensions, and simplify plugins
extend-select = B,C4,SIM

exclude =
    .git,
    .hg,
    .mypy_cache,
    .tox,
    .venv,
    _build,
    buck-out,
    build,
    dist,
    airspan_acp_metrics/database/models/__init__.py,
    airspan_acp_metrics/alembic_migrations

# Per-file ignores
per-file-ignores =
    __init__.py:F401
    airspan_acp_metrics/test/*:B008,B011
